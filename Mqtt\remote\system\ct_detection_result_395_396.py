from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# CT检测状态/结果上报

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 396

cmd_type_c2s = 395

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    **attr_map_common_opt,

    'detectState': ('detect_state', 'int'),
    'fastCheckRet': ('ct_check_result', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class CtResult(object):

    def ct_result_get_from_aws_get(self, *args, **kwargs):
        """  ct result get from AWS,the message get-T396

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | CT Result Get From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def ct_result_get_from_aws_get_check(self, **kwargs):
        """  ct result get from AWS,the message get check-T396

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | CT Result Get From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.ct_result_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def ct_result_get_from_device_get(self, *args, **kwargs):
        """   ct result get response from device,the message get-T395

        Args：
            | opt | int | 操作，0:检测状态，1:检测结果 |
            | detect_state | int | 1:开始检测, 2：检测中,3：检测结束 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT检测结果 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | CT Result Get From Device Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def ct_result_get_from_device_get_check(self, **kwargs):
        """  ct result get response from device,the message get check-T395

        Kwargs:
            | opt | int | 操作，0:检测状态，1:检测结果 |
            | detect_state | int | 1:开始检测, 2：检测中,3：检测结束 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT检测结果 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  CT Result Get From Device Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.ct_result_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def ct_result_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T395/396

        Args for T396:
            | result | int | 结果，0:成功 1:失败 |
        Args for T395：
            | opt | int | 操作，0:检测状态，1:检测结果 |
            | detect_state | int | 1:开始检测, 2：检测中,3：检测结束 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT检测结果 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 395/396 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=396 | rx_time_window=300 |
        | ${status} | CT Result Get From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.ct_result_get_from_device_get, cmd_type_s2c: self.ct_result_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def ct_result_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T395/396

        Kwargs for T396:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for T395：
            | opt | int | 操作，0:检测状态，1:检测结果 |
            | detect_state | int | 1:开始检测, 2：检测中,3：检测结束 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT检测结果 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 395/396 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=395 | rx_time_window=300 | filter_mode=and |
        | ${status} | CT Result Get From Msg Get Check | opt=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.ct_result_get_from_device_get_check, cmd_type_s2c: self.ct_result_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
