from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 故障录波结果上传

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 380

cmd_type_c2s = 379

attr_map_s2c = {

    'cmd': ('', 'int'),
    'result': ('', 'int'),

}

attr_map_c2s = {

    'cmd': ('', 'int'),
    'data': ('', 'dict'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class FaultWaveRecordUpload(object):

    def fault_wave_record_result_upload_from_aws_get(self, *args, **kwargs):
        """  fault wave record result upload from AWS,the message get-T380

        Args：
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | result | int | 结果，0：成功，-1：失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Result Upload From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_result_upload_from_aws_get_check(self, **kwargs):
        """  upgrade info upload get from AWS,the message get check-T380

        Kwargs:
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | result | int | 结果，0：成功，-1：失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Fault Wave Record Result Upload From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_result_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_result_upload_from_device_get(self, *args, **kwargs):
        """   upgrade info upload get response from device,the message get-T379

        Args:
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Result Upload From Device Get | data |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_result_upload_from_device_get_check(self, **kwargs):
        """  upgrade info upload get response from device,the message get check-T379

        Kwargs:
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Fault Wave Record Result Upload From Device Get Check | cmd=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_result_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_result_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T379/380

        Args for T379:
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据 |
        Args for T380：
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | result | int | 结果，0：成功，-1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 379/380 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=380 | rx_time_window=300 |
        | ${status} | Fault Wave Record Result Upload From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_result_upload_from_device_get, cmd_type_s2c: self.fault_wave_record_result_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def fault_wave_record_result_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T379/380

        Kwargs for T379:
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据 |
        Kwargs for T380：
            | cmd | int | 类型，0:实时数据获取；1：参数设置；2文件获取 |
            | result | int | 结果，0：成功，-1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 379/380 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=379 | rx_time_window=300 | filter_mode=and |
        | ${status} | Fault Wave Record Result Upload From Msg Get Check | cmd=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_result_upload_from_device_get_check, cmd_type_s2c: self.fault_wave_record_result_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
