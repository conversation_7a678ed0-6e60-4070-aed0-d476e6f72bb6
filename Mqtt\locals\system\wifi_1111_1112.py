from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# WiFi热点账号与密码

cmd_type_c2s = 1111

cmd_type_s2c = 1112

attr_map_c2s = {


    **attr_map_common_opt,

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'wifi_SSID': ('wifi_SSID', 'string'),
    'wifi_Pw': ('wifi_password', 'string'),
    'ap_SSID': ('AP_SSID', 'string'),
    'ap_Pw': ('AP_password', 'string'),
    'wifi_Safety': ('wifi_safety', 'int'),

}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalWiFiHotSpot(Base):

    def local_wifi_hotspot_set(self, *args, **kwargs):
        """  local wifi hotspot set-T1111
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Wifi Hotspot Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_wifi_hotspot_set_result_check(self, _response, **kwargs):
        """  local wifi hotspot set result check-T1112

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Wifi Hotspot Set |
        | ${status} = | Local Wifi Hotspot Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
