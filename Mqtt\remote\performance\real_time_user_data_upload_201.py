from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 用户实时数据上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 201

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'report_type': ('', 'int'),
    'mode': ('', 'int'),
    'run_status': ('', 'int'),
    'slaver_stat': ('slave_status', 'int'),
    'elecnet_state': ('grid_status', 'int'),

    'fhpSn': ('device_sn', 'list'),
    'infi_status': ('fhp_status', 'list'),
    'pe_stat': ('pe_status', 'list'),
    'bms_work': ('bms_status', 'list'),
    'bms_heat_state': ('bms_heating_status', 'list'),

    'p_uti': ('grid_power', 'float'),
    'p_sun': ('solar_power', 'float'),
    'p_gen': ('generator_power', 'float'),
    'p_fhp': ('fhp_generate_power', 'float'),
    'p_load': ('user_power', 'float'),

    'kwh_uti_in': ('kwh_grid_in', 'float'),
    'kwh_uti_out': ('kwh_grid_out', 'float'),
    'kwh_sun': ('kwh_solar', 'float'),
    'kwh_gen': ('kwh_generator', 'float'),
    'kwh_fhp_di': ('kwh_fhp_discharging', 'float'),
    'kwh_fhp_chg': ('kwh_fhp_charging', 'float'),
    'kwh_load': ('kwh_user', 'float'),

    'solarSupply': ('solar_supply_rate', 'float'),  # not used anymore
    'soc': ('', 'float'),
    'fhpSoc': ('fhp_soc', "list"),
    'fhpPower': ('fhp_power', "list"),

    't_amb': ('env_temperature', 'float'),
    'main_sw': ('main_switch', 'list'),
    'pro_load': ('smart_load_status', 'list'),
    'do': ('dry_contact_relay_output_status', 'list'),  # optional
    'di': ('dry_contact_relay_input_status', 'list'),  # optional

    'signal': ('4g_signal', 'int'),
    'wifiSignal': ('wifi_signal', 'int'),
    'connType': ('network_connect_type', 'int'),
    'genStat': ('generator_status', 'int'),

    'kwhSolarLoad': ('kwh_solar_load', 'int'),
    'kwhGridLoad': ('kwh_grid_load', 'int'),
    'kwhFhpLoad': ('kwh_fhp_load', 'int'),
    'kwhGenLoad': ('kwh_generator_load', 'int'),
    'remoteSolarEn': ('remote_solar_enable', 'int'),
    'solarPower': ('near_solar_power', 'int'),
    'remoteSolar1Power': ('remote_solar1_power', 'int'),
    'remoteSolar2Power': ('remote_solar2_power', 'int'),
    'DSPNEMPVPower': ('dsp_nem_solar_power', 'int'),
    'threePhasePvPower': ('three_phase_pv_power', 'int'),  # optional
    'BFPVApboxRelay': ('before_pv_apbox_relay_status', 'int'),

    'batOutGrid': ('kwh_apower_feed_in_dayily', 'float'),
    'soOutGrid': ('kwh_pv_feed_in_dayily', 'float'),
    'soChBat': ('kwh_pv_charged_dayily', 'float'),
    'gridChBat': ('kwh_grid_charged_dayily', 'float'),
    'genChBat': ('kwh_generator_charged_dayily', 'float'),
    'sinHTemp': ('highest_sys_single_unit_temp', 'float'),
    'sinLTemp': ('lowest_sys_single_unit_temp', 'float'),

    'offgridreason': ('off_grid_reason', 'int'),  # optional
    'name': ('', 'string'),
    'electricity_type': ('', 'int'),

    'gridRelay2': ('grid_relay_status', 'int'),  # optional
    'pvRelay2': ('pv_relay_status', 'int'),  # optional
    'blackStartRelay': ('black_start_relay_status', 'int'),
    'genVoltage': ('generator_voltage', 'int'),  # optional
    'gridPhaseConSet': ('grid_phase_set', 'int'),
    'v2lModeEnable': ('v2l_mode_enable', 'int'),
    'v2lRunState': ('v2l_run_state', 'int'),
    'kwhV21ToFhp': ('kwh_v2l_to_fhp', 'float'),  # optional
    'kwhV2lToHome': ('kwh_v2l_to_home', 'float'),  # optional
    'kwhIqLoad1': ('kwh_iq_load', 'list'),  # optional
    'mpptAllPower': ('total_power_mppt', 'float'),  # optional
    'mpptAactPower': ('active_power_mppt', 'list'),  # optional
    'mpptSta': ('mppt_status', 'list'),  # optional

    'proximalSolarWh': ('kwh_near_end_solar_daily', 'float'),  # optional
    'remoteSolar1Wh': ('kwh_remote_solar1_daily', 'float'),  # optional
    'remoteSolar2Wh': ('kwh_remote_solar2_daily', 'float'),  # optional
    'remoteSolar3Wh': ('kwh_remote_solar3_daily', 'float'),  # optional

    'meterkitPvWh': ('kwh_meter_kit_pv_daily', 'float'),  # optional
    'secondaryPvWh': ('kwh_second_pv_daily', 'float'),  # optional
    'mpptWh': ('kwh_mppt', 'float'),  # optional
    'kwhV2l': ('kwh_v2l', 'float'),  # optional
    'acPower': ('inverter_ac_power', 'float'),  # optional
    'aggAcPvPower': ('total_ac_pv_power', 'float'),  # optional

    'apbox20Pv': ('apbox20_pv', 'list'),  # optional
    'apbox20PvIndex': ('apbox20_pv_index', 'list'),  # optional
    'apbox20PvStat': ('apbox20_pv_relay_status', 'list'),  # optional
    'apbox20PvPower': ('apbox20_pv_power', 'list'),  # optional
    'apbox20PvWh': ('kwh_apbox20_pv_daily', 'list'),  # optional
    'mPanPv1Power': ('main_pan_pv1_power', 'float'),  # optional
    'mPanPv1Wh': ('kwh_main_pan_pv1_power', 'float'),  # optional
    'mPanPv2Power': ('main_pan_pv2_power', 'float'),  # optional
    'mPanPv2Wh': ('kwh_main_pan_pv2_power', 'float'),  # optional

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class RealTimeUserData(object):

    def real_time_user_data_from_device_get(self, *args, **kwargs):
        """  the real time user data from device,the message get-T201

        Args：
            | report_type | int | 上报类型，0：周期；1：召唤；2：事件变化  |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | pe_status | int list | PE运行状态， -1：离线，0：初始化，1：参数初始化，2：监听，3：待机，4：预充检测，5：预充，6：起机延时，7：逆变继电器吸合，8：运行，9：故障，10：离线（老版本） |
            | bms_status | int list | BMS工作状态，0：停机，1：初始化，2：自检，3：待机，4：预充，5：运行，6：充电，7：放电，8：充电保护，9：放电保护，10：充放电保护，11：故障，12：异常充电准备，13：异常充电，14：异常充电停止，15：工装模式，16：补电模式，17：补电故障 |
            | bms_heating_status | int list | 0：未使能，1：使能，bit0:加热膜1状态，bit1:加热膜2状态，bit2:加热膜3状态，bit3：加热膜4状态 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | fhp_power | list | FHP电池有功功率，单位：kw |
            | env_temperature | float | 环境环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | smart_load_status | int list | 3个智能负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | 4g_signal | int | 4G信号强度 |
            | wifi_signal | int | Wifi信号强度 |
            | network_connect_type | int | 0:未接入网络，1：eth1,2：eth1, 3：wifi，4：4G |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int | 发电机供载电量，单位：kwh |
            | remote_solar_enable | int | 远端光伏接入使能，0：不使能，1：使能数量1个，2：使能数量2个 |
            | near_solar_power | int | 近端光伏功率，单位：100w |
            | remote_solar1_power | int | 远端光伏1功率，单位：100w |
            | remote_solar2_power | int | 远端光伏2功率，单位：100w |
            | dsp_nem_solar_power | int | DSP NEM光伏功率，单位：100w |
            | three_phase_pv_power | int | 三相光伏功率，单位：100w |
            | before_pv_apbox_relay_status | int | 光伏电表前aPbox继电器状态 | 0：断开，1：闭合 |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | off_grid_reason | int | 离网原因，0：非主动离网，1：主动离网 |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | grid_relay_status | int | 市电继电器2状态 | 0：断开，1：闭合 |
            | pv_relay_status | int | 光伏继电器2状态 | 0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器2状态 | 0：断开，1：闭合 |
            | generator_voltage | int | 油机电压，单位：0.1V |
            | grid_phase_set | int | 市电相制设置：00：splip,01：单相三角形接法，02：三相三角形接法 |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | kwh_v2l_to_fhp | float | V2L给电池充电电量，单位：kwh |
            | kwh_v2l_to_home | float | V2L给家庭负载消耗电量，单位：kwh |
            | kwh_iq_load | float list | 智能负载线路使用电量，单位：kwh |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障 |
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | kwh_v2l | float | V2L供载量，单位：kwh |
            | inverter_ac_power | float | 逆变器侧功率（包括电池电量和光伏发电量），单位：w |
            | total_ac_pv_power | float | 交流侧光伏总功率（总光伏减去直流光伏功率），单位：w |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Real Time User Data From Device Get | soc | mode |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_user_data_from_device_get_check(self, **kwargs):
        """  the real time user data from device,the message get check-T201

        Kwargs:
            | report_type | int | 上报类型，0：周期；1：召唤；2：事件变化  |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | pe_status | int list | PE运行状态， -1：离线，0：初始化，1：参数初始化，2：监听，3：待机，4：预充检测，5：预充，6：起机延时，7：逆变继电器吸合，8：运行，9：故障，10：离线（老版本） |
            | bms_status | int list | BMS工作状态，0：停机，1：初始化，2：自检，3：待机，4：预充，5：运行，6：充电，7：放电，8：充电保护，9：放电保护，10：充放电保护，11：故障，12：异常充电准备，13：异常充电，14：异常充电停止，15：工装模式，16：补电模式，17：补电故障 |
            | bms_heating_status | int list | 0：未使能，1：使能，bit0:加热膜1状态，bit1:加热膜2状态，bit2:加热膜3状态，bit3：加热膜4状态 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | fhp_power | list | FHP电池有功功率，单位：kw |
            | env_temperature | float | 环境环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | smart_load_status | int list | 3个智能负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | 4g_signal | int | 4G信号强度 |
            | wifi_signal | int | Wifi信号强度 |
            | network_connect_type | int | 0:未接入网络，1：eth1,2：eth1, 3：wifi，4：4G |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int | 发电机供载电量，单位：kwh |
            | remote_solar_enable | int | 远端光伏接入使能，0：不使能，1：使能数量1个，2：使能数量2个 |
            | near_solar_power | int | 近端光伏功率，单位：100w |
            | remote_solar1_power | int | 远端光伏1功率，单位：100w |
            | remote_solar2_power | int | 远端光伏2功率，单位：100w |
            | dsp_nem_solar_power | int | DSP NEM光伏功率，单位：100w |
            | three_phase_pv_power | int | 三相光伏功率，单位：100w |
            | before_pv_apbox_relay_status | int | 光伏电表前aPbox继电器状态 | 0：断开，1：闭合 |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | off_grid_reason | int | 离网原因，0：非主动离网，1：主动离网 |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | grid_relay_status | int | 市电继电器2状态 | 0：断开，1：闭合 |
            | pv_relay_status | int | 光伏继电器2状态 | 0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器2状态 | 0：断开，1：闭合 |
            | generator_voltage | int | 油机电压，单位：0.1V |
            | grid_phase_set | int | 市电相制设置：00：splip,01：单相三角形接法，02：三相三角形接法 |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | kwh_v2l_to_fhp | float | V2L给电池充电电量，单位：kwh |
            | kwh_v2l_to_home | float | V2L给家庭负载消耗电量，单位：kwh |
            | kwh_iq_load | float list | 智能负载线路使用电量，单位：kwh |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障 |
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | kwh_v2l | float | V2L供载量，单位：kwh |
            | inverter_ac_power | float | 逆变器侧功率（包括电池电量和光伏发电量），单位：w |
            | total_ac_pv_power | float | 交流侧光伏总功率（总光伏减去直流光伏功率），单位：w |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Real Time User Data From Device Get Check | mode=2 | run_status=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_user_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_user_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T201

        Args for T201：
            | report_type | int | 上报类型，0：周期；1：召唤；2：事件变化  |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | pe_status | int list | PE运行状态， -1：离线，0：初始化，1：参数初始化，2：监听，3：待机，4：预充检测，5：预充，6：起机延时，7：逆变继电器吸合，8：运行，9：故障，10：离线（老版本） |
            | bms_status | int list | BMS工作状态，0：停机，1：初始化，2：自检，3：待机，4：预充，5：运行，6：充电，7：放电，8：充电保护，9：放电保护，10：充放电保护，11：故障，12：异常充电准备，13：异常充电，14：异常充电停止，15：工装模式，16：补电模式，17：补电故障 |
            | bms_heating_status | int list | 0：未使能，1：使能，bit0:加热膜1状态，bit1:加热膜2状态，bit2:加热膜3状态，bit3：加热膜4状态 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | fhp_power | list | FHP电池有功功率，单位：kw |
            | env_temperature | float | 环境环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | smart_load_status | int list | 3个智能负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | 4g_signal | int | 4G信号强度 |
            | wifi_signal | int | Wifi信号强度 |
            | network_connect_type | int | 0:未接入网络，1：eth1,2：eth1, 3：wifi，4：4G |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int | 发电机供载电量，单位：kwh |
            | remote_solar_enable | int | 远端光伏接入使能，0：不使能，1：使能数量1个，2：使能数量2个 |
            | near_solar_power | int | 近端光伏功率，单位：100w |
            | remote_solar1_power | int | 远端光伏1功率，单位：100w |
            | remote_solar2_power | int | 远端光伏2功率，单位：100w |
            | dsp_nem_solar_power | int | DSP NEM光伏功率，单位：100w |
            | three_phase_pv_power | int | 三相光伏功率，单位：100w |
            | before_pv_apbox_relay_status | int | 光伏电表前aPbox继电器状态 | 0：断开，1：闭合 |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | off_grid_reason | int | 离网原因，0：非主动离网，1：主动离网 |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | grid_relay_status | int | 市电继电器2状态 | 0：断开，1：闭合 |
            | pv_relay_status | int | 光伏继电器2状态 | 0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器2状态 | 0：断开，1：闭合 |
            | generator_voltage | int | 油机电压，单位：0.1V |
            | grid_phase_set | int | 市电相制设置：00：splip,01：单相三角形接法，02：三相三角形接法 |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | kwh_v2l_to_fhp | float | V2L给电池充电电量，单位：kwh |
            | kwh_v2l_to_home | float | V2L给家庭负载消耗电量，单位：kwh |
            | kwh_iq_load | float list | 智能负载线路使用电量，单位：kwh |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障 |
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | kwh_v2l | float | V2L供载量，单位：kwh |
            | inverter_ac_power | float | 逆变器侧功率（包括电池电量和光伏发电量），单位：w |
            | total_ac_pv_power | float | 交流侧光伏总功率（总光伏减去直流光伏功率），单位：w |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 201 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=201 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time User Data From Msg Get | mode | soc | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_user_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_user_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T201

        Kwargs for T201：
            | report_type | int | 上报类型，0：周期；1：召唤；2：事件变化  |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | pe_status | int list | PE运行状态， -1：离线，0：初始化，1：参数初始化，2：监听，3：待机，4：预充检测，5：预充，6：起机延时，7：逆变继电器吸合，8：运行，9：故障，10：离线（老版本） |
            | bms_status | int list | BMS工作状态，0：停机，1：初始化，2：自检，3：待机，4：预充，5：运行，6：充电，7：放电，8：充电保护，9：放电保护，10：充放电保护，11：故障，12：异常充电准备，13：异常充电，14：异常充电停止，15：工装模式，16：补电模式，17：补电故障 |
            | bms_heating_status | int list | 0：未使能，1：使能，bit0:加热膜1状态，bit1:加热膜2状态，bit2:加热膜3状态，bit3：加热膜4状态 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | fhp_power | list | FHP电池有功功率，单位：kw |
            | env_temperature | float | 环境环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | smart_load_status | int list | 3个智能负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | 4g_signal | int | 4G信号强度 |
            | wifi_signal | int | Wifi信号强度 |
            | network_connect_type | int | 0:未接入网络，1：eth1,2：eth1, 3：wifi，4：4G |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int | 发电机供载电量，单位：kwh |
            | remote_solar_enable | int | 远端光伏接入使能，0：不使能，1：使能数量1个，2：使能数量2个 |
            | near_solar_power | int | 近端光伏功率，单位：100w |
            | remote_solar1_power | int | 远端光伏1功率，单位：100w |
            | remote_solar2_power | int | 远端光伏2功率，单位：100w |
            | dsp_nem_solar_power | int | DSP NEM光伏功率，单位：100w |
            | three_phase_pv_power | int | 三相光伏功率，单位：100w |
            | before_pv_apbox_relay_status | int | 光伏电表前aPbox继电器状态 | 0：断开，1：闭合 |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | off_grid_reason | int | 离网原因，0：非主动离网，1：主动离网 |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | grid_relay_status | int | 市电继电器2状态 | 0：断开，1：闭合 |
            | pv_relay_status | int | 光伏继电器2状态 | 0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器2状态 | 0：断开，1：闭合 |
            | generator_voltage | int | 油机电压，单位：0.1V |
            | grid_phase_set | int | 市电相制设置：00：splip,01：单相三角形接法，02：三相三角形接法 |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | kwh_v2l_to_fhp | float | V2L给电池充电电量，单位：kwh |
            | kwh_v2l_to_home | float | V2L给家庭负载消耗电量，单位：kwh |
            | kwh_iq_load | float list | 智能负载线路使用电量，单位：kwh |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障 |
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | kwh_v2l | float | V2L供载量，单位：kwh |
            | inverter_ac_power | float | 逆变器侧功率（包括电池电量和光伏发电量），单位：w |
            | total_ac_pv_power | float | 交流侧光伏总功率（总光伏减去直流光伏功率），单位：w |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 201 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=201 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time User Data From Msg Get Check | mode=2 | soc=56 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_user_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
