from selenium.common.exceptions import NoSuchElementException
from time import sleep
from APP.base import Base
from robot.api import logger
from .locator import get_locator

common_up_locator = "//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView"


class System(Base):

    def assert_system_page(self, timeout=20):

        value = "Locator_system_page"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, will_do_loading_detect=True, timeout=timeout)

    def go_to_device_page(self, device=None):
        """ Go to device main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Device Page | device=ULG_EMSTEST_T004-P134 |
        """
        logger.info(f'the user parameters:device:{device}')

        self.device = device

        locator_comm = get_locator("locator_search")

        self.find_element(*locator_comm, click=True)

        if self.platform_name == 'iOS':

            # self.hide_keyboard()

            locator = ('acc_id', 'Search')

            ele = self.find_element(*locator, click=True, element_visible_check=False)
            self.find_element(*locator, element_visible_check=False, send_keys=f'{device}\n')

            locator = ('cls_name', 'XCUIElementTypeImage')

            ele = self.find_element(*locator, click=True)

        else:

            locator = get_locator("locator_device_input_image")

            self.find_element(*locator, click=True, send_keys=device)

            self.find_element(*locator_comm, click=True)

            locator = get_locator("locator_device_input")

            eles = self.find_element(*locator, multiple=True, timeout=45)

            logger.debug(f"the matched device list:{eles}")

            if len(eles) == 1:

                eles[0].click()

            else:

                logger.info(f"Found {len(eles)} matched devices!")

                for j in eles:

                    value = j.get_attribute("content-desc")

                    if f'{device}\n' in value:

                        logger.info('OK,found the exact matched device!')

                        j.click()

                        break

    def go_to_system_commission_page(self):
        """ Go to system->commission page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Commission Page |
        """
        value = "locator_commission"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True, timeout=120)

    def exit_system_commission_page(self):
        """ exit system->commission page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Commission Page |
        """
        self._general_backward_upper_page()

    def go_to_system_tools_page(self):
        """ Go to system->tools page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Tools Page |
        """
        locator = get_locator("locator_tools")
        self._go_to_sub_page(locator=locator)

    def exit_system_tools_page(self):
        """ exit system->tools page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Tools Page |
        """
        locator = get_locator("locator_cancel")
        self._go_to_sub_page(locator=locator)

    def go_to_system_history_page(self):
        """ Go to system->history page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System History Page |
        """
        locator = get_locator("locator_history")
        self._go_to_sub_page(locator=locator)

    def exit_system_history_page(self):
        """ exit system->history page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System History Page |
        """
        value = f"{common_up_locator}[1]"

        self._go_to_sub_page(value=value, method='xpath')

    def go_to_system_setting_page(self):
        """ Go to system->setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Setting Page |
        """
        locator = get_locator("locator_settings")
        self._go_to_sub_page(locator=locator)

    def exit_system_setting_page(self):
        """ exit system->setting page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Setting Page |
        """
        self._general_backward_upper_page()

    def go_to_system_apower_page(self):

        #  locator = get_locator("locator_aPower")

        #  self._go_to_sub_page(locator=locator)

        #  x_ratio, y_ratio = 805 / 1080, 912 / 2047

        x_ratio, y_ratio = 805 / self.window_width, 912 / self.window_height

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)

        self.click_by_coordinate(*coord)

    def exit_system_apower_page(self):

        self._general_backward_upper_page()

    def go_to_system_agate_page(self):
        """ Go to system->aGate page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Agate Page |
        """
        locator = get_locator("locator_aGate")

        self._go_to_sub_page(locator=locator)

    def exit_system_agate_page(self):
        """ exit system->aGate page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Agate Page |
        """
        locator = get_locator("locator_aGate_exit")

        self._go_to_sub_page(locator=locator)

        sleep(2)

    def _detect_home_solar_grid_apower(self, timeout=10):

        locator = get_locator("locator_home_solar_grid_apower")

        self.find_element(*locator, timeout=timeout)

    def go_to_system_power_usage_home_page(self):
        """ Go to system->home page(power usage)

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Power Usage Home Page |
        """
        x_ratio, y_ratio = 161 / 1080, 775 / 2120

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)

        self._detect_home_solar_grid_apower()
        self.click_by_coordinate(*coord)

    def exit_system_power_usage_home_page(self):
        """ exit system-> home page(power usage) and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Power Usage Home Page |
        """
        self._general_backward_upper_page()

    def go_to_system_power_usage_solar_page(self):
        """ Go to system->solar page(power usage)

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Power Usage Solar Page |
        """
        x_ratio, y_ratio = 596 / 1080, 674 / 2120

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)
        self._detect_home_solar_grid_apower()
        self.click_by_coordinate(*coord)

    def exit_system_power_usage_solar_page(self):
        """ exit system-> solar page(power usage) and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Power Usage Solar Page |
        """
        self._general_backward_upper_page()

    def go_to_system_power_usage_apower_page(self):
        """ Go to system->aPower page(power usage)

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Power Usage Apower Page |
        """
        x_ratio, y_ratio = 794 / 1080, 909 / 2120

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)
        self._detect_home_solar_grid_apower()
        self.click_by_coordinate(*coord)

    def exit_system_power_usage_apower_page(self):
        """ exit system-> aPower page(power usage) and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Power Usage Apower Page |
        """
        self._general_backward_upper_page()

    def go_to_system_power_usage_grid_page(self):
        """ Go to system->grid page(power usage)

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Power Usage Grid Page |
        """
        x_ratio, y_ratio = 913 / 1080, 560 / 2120

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)
        self._detect_home_solar_grid_apower()
        self.click_by_coordinate(*coord)

    def exit_system_power_usage_grid_page(self):
        """ exit system-> grid page(power usage) and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Power Usage Grid Page |
        """
        self._general_backward_upper_page()

    def go_to_system_power_usage_generator_page(self):
        """ Go to system->generator page(power usage)

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Power Usage Generator Page |
        """
        x_ratio, y_ratio = 950 / 1080, 1014 / 2120

        coord = (x_ratio * self.window_width, y_ratio * self.window_height)
        self._detect_home_solar_grid_apower()
        self.click_by_coordinate(*coord)

    def exit_system_power_usage_generator_page(self):
        """ exit system-> generator page(power usage) and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Power Usage Generator Page |
        """
        self._general_backward_upper_page()

    def get_system_alarm(self):

        locator_comm = ('xpath',)
        """
        //android.view.View[@content-desc="Grid disconnection\nThere is an issue with the grid supply. This could be due to a grid outage or poor power quality causing a grid disconnection. Contact your service provider if you believe the grid is actually present"]/android.widget.ImageView[2]
        //android.view.View[@content-desc="Fail-Safe Mode\nThe aGate is in a fail-safe mode. Please contact your installer"]/android.widget.ImageView[1]
        //android.view.View[@content-desc="Fault\nContact your installer to resolve"]/android.widget.ImageView[2]
        //android.view.View[@content-desc="FHP firmware upgrade failed\nPlease ensure that FHP has a stable Internet connection and try the upgrade again.\nIf the upgrade keeps failing, please reboot the FHP system and try again.\nContact FranklinWH support team for further assistance."]/android.widget.ImageView[2]
        //android.view.View[@content-desc="Emergency Power Off\nAll relays have been disconnected due to an emergency stop"]/android.widget.ImageView[2]
        //android.widget.ImageView[@content-desc="aGate Offline\nThe aGate is offline. Click to connect to aGate directly"]
        //android.view.View[@content-desc="Emergency Power Off\nAll relays have been disconnected due to an emergency stop"]
        """
        self.find_element(*locator_comm, click=True)

    def get_system_info(self, *args, **kwargs):
        logger.info(f'the user parameters:args:{args},kwargs:{kwargs}')

        ret_dict = {}

        # operating mode   Time of Use/Fail into safe mode/Emergency stop

        locator = get_locator("locator_operating_mode")

        ele = self.find_element(*locator)

        value = ele.get_attribute("content-desc")

        _value = value.split(': ')

        operating_mode = _value[-1].strip()

        logger.info(f'the operating mode:{operating_mode}')

        ret_dict.update({'operating_mode': operating_mode})

        # fault/offline/Standby
        locator = get_locator("locator_fault_mode")

        eles = self.find_element(*locator, multiple=True)

        ele = eles[-1] if len(eles) > 1 else eles[0]

        sys_status = ele.get_attribute("content-desc")
        logger.info(f'the fault mode:{sys_status}')

        ret_dict.update({'system_status': sys_status})

        # power remain 0~100
        locator = get_locator("locator_power_remain")

        eles = self.find_element(*locator, multiple=True)

        ele = eles[0]

        power_remain = ele.get_attribute("content-desc")

        if 'SN' in power_remain:
            power_remain = ''

        else:
            power_remain = f'{power_remain}%'

        logger.info(f'the power remain:{power_remain}')

        ret_dict.update({'remain_power': power_remain})

        # location
        locator = get_locator("locator_location")

        eles = self.find_element(*locator, multiple=True)

        ele = eles[0]

        value = ele.get_attribute("content-desc")

        logger.info(f'the possible location:{value}')

        if value != 'aGate':

            location = eles[0].get_attribute("content-desc")
            logger.info(f'the system location:{location}')

        else:

            logger.info('no location information is shown!')
            location = ''

        ret_dict.update({'location': location})

        # power data

        locator = get_locator("locator_home_solar_grid_apower")
        ele = self.find_element(*locator)

        value = ele.get_attribute("content-desc")
        logger.info(f'the possible power data:{value}')

        power_data = value.replace('\n', ' ')

        ret_dict.update({'power': power_data})

        # system connect mode
        locator = get_locator("locator_system_connect_mode")

        eles = self.find_element(*locator, multiple=True)

        ele = eles[0]

        sys_conn_mode = ele.get_attribute("content-desc")
        logger.info(f'the system connect mode:{sys_conn_mode}')

        ret_dict.update({'system_connection_mode': sys_conn_mode})

        # aPower number  0/1~15

        locator = get_locator("locator_aPower")
        ele = self.find_element(*locator)

        value = ele.get_attribute("content-desc")

        _value = value.replace('\n', ' ').replace('aPower', '')

        apower_number = _value.strip()

        logger.info(f'the aPower number:{apower_number}')
        ret_dict.update({'apower_number': apower_number})

        logger.info(f'the system info:{ret_dict}')

        return ret_dict

    def go_to_system_home_solar_grid_apower_page(self):
        """ Go to system->home_solar_grid_apower page??,disturbed by unexpected news

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To System Home Solar Grid Apower Page |
        """
        locator = get_locator("locator_home_solar_grid_apower")

        self._go_to_sub_page(locator=locator)

    def exit_system_home_solar_grid_apower_page(self):
        """ exit system->home_solar_grid_apower page and go back to the system main page??,disturbed by unexpected news

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit System Home Solar Grid Apower Page |
        """
        locator = get_locator("locator_home_solar_grid_apower_exit")

        self._go_to_sub_page(locator=locator)

    def system_set_connect_method(self, mode='internet', wifi_hotspot=None, wifi_password=None):
        """ Set system connect method

        args:
            | mode | string | internet(默认)/direct_connect |
            | wifi_hotspot |  wifi热点名，默认为None |
            | wifi_password | wifi密码，默认为None |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | None |
        Examples:
            | System Set Connect Method | mode=direct_connect   | wifi_hotspot=${device_AP_SSID} | wifi_password=${device_AP_password} |
            | System Set Connect Method	| mode=internet |  wifi_hotspot=${internet_AP_SSID} | wifi_password=${internet_AP_password} |
        """
        logger.info(f'the user parameters:mode:{mode}')

        mode = mode.lower()

        current_mode = self.system_get_connect_method()

        if current_mode != mode:

            locator = get_locator("locator_system_connect_mode")

            eles = self.find_element(*locator, multiple=True)

            eles[0].click()

            # try to set the mode

            if mode in ['internet']:

                locator = get_locator("locator_system_connect_mode_internet")

            elif mode in ['direct_connect']:

                locator = get_locator("locator_system_connect_mode_direct")

            self.find_element(*locator, click=True)

            if mode in ['direct_connect']:

                if wifi_hotspot is not None and wifi_password is not None:

                    self.commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=2, network_mode_change=True)

            if mode in ['internet']:

                if wifi_hotspot is not None and wifi_password is not None:

                    self._system_confirm_locator()

                    self.commission_network_wifi_agate_disconnect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, network_mode_change=True)

    def system_get_connect_method(self):
        """ get current system connection method

        args:
            | None |
        Kwargs:
            | None |
        Return:
            | string | internet/direct_connect |
        Examples:
            | ${result}	| System Get Connect Method  |
        """
        locator = get_locator("locator_system_connect_mode")
        ele = self.find_element(*locator)
        value = ele.get_attribute("content-desc")

        return value.lower()

    def system_get_energy_diagram_info(self, value='internet'):
        ...

    def system_get_operating_mode(self):

        # operating mode   Time of Use/Fail into safe mode/Emergency stop

        locator = get_locator("locator_operating_mode")

        ele = self.find_element(*locator)

        value = ele.get_attribute("content-desc")

        _value = value.split(': ')

        operating_mode = _value[-1].strip()

    def system_get_charging_status(self):

        locator = "Charging"

        try:

            self.find_element_by_accessibility_id(locator)

            return locator

        except NoSuchElementException:
            pass

    def system_get_sn(self):

        locator = "SN:  10180002A00C22121104"

        try:
            self.find_element_by_accessibility_id(locator)

            return locator

        except NoSuchElementException:
            pass

    def system_get_ball_status(self):

        locator = "//android.view.View[@content-desc=""]/android.view.View[2]/android.widget.ImageView[1]"

        try:
            self.find_element_by_xpath(locator)

            return locator

        except NoSuchElementException:
            pass
