from Web.base import Base
from time import sleep
from robot.api import logger
from selenium.common.exceptions import (NoSuchElementException, ElementNotInteractableException)
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException


class Login(Base):

    def web_login(self, username='SuperAdmin', password='1234567890T'):

        logger.info(f"the user kwargs is:username:{username},password:{password}")

        try:
            locator = ('xpath', "//span[contains(.,'中文')]")

            self.find_element(*locator, timeout=8)

            logger.info("OK,detect the current lang:Chinese")

        except (TimeoutException, ElementNotInteractableException):

            logger.info("OK,detect the current lang:Engilsh")

            locator = ('xpath', "//span[contains(.,'English')]")

            ele = self.find_element(*locator, timeout=2)

            logger.info(f"the element:{ele}")

            logger.info("switch to lang:Chinese")

            self.move_to_element(ele)

            ele.click()

            sleep(2)

            locator = ('css', "div.cursor-pointer:nth-child(2)")

            ele = self.find_element(*locator, click=True)

        locator = ('xpath', """//*[@type="text"]""")

        ele = self.find_element(*locator, clear=True, send_keys=username)

        locator = ('xpath', """//*[@type="password"]""")

        ele = self.find_element(*locator, clear=True, send_keys=password)

        sleep(3)

        locator = ('cls_name', "login-btn")

        ele = self.find_element(*locator, click=True)
