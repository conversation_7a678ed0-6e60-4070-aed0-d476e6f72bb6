import pywifi
import time
from time import (sleep, time)
from pywifi import const, PyWiFi, Profile

wifi = PyWiFi()

status_dict = {0: 'disconnected',
               1: 'scanning',
               2: 'inactive',
               3: 'connecting...',
               4: 'connected',
               }

akm_dict = {'WPA': const.AKM_TYPE_WPA,
            'WPA2': const.AKM_TYPE_WPA2,
            'WPAPSK': const.AKM_TYPE_WPA,
            'WPA2PSK': const.AKM_TYPE_WPA2PSK,
            'NONE': const.AKM_TYPE_NONE,
            }


class Wifi(object):

    def wifi_init(self):

        self.interface = wifi.interfaces()[0]
        self.profile = Profile()

    def wifi_scan(self):

        self.interface.scan()

        return [i.ssid for i in self.interface.scan_results()]

    def wifi_connect(self, SSID=None, password=None, auth='WPA2PSK'):
        """
        auth:WPA/WPA2/WPAPSK/WPA2PSK/none

        """
        self.profile.ssid = SSID
        self.profile.key = password
        self.profile.auth = const.AUTH_ALG_OPEN
        self.profile.akm.append(akm_dict[auth.upper()])
        self.profile.cipher = const.CIPHER_TYPE_CCMP

        self.interface.remove_all_network_profiles()

        tmp_profile = self.interface.add_network_profile(self.profile)

        self.interface.connect(tmp_profile)

    def wifi_disconnect(self):

        return self.interface.disconnect()

    def wifi_connect_status(self):

        num = self.interface.status()

        return status_dict[num]
