import asyncio
import warnings
from time import sleep
from bleak import BleakScanner as BS
from bleak import BleakClient as BC

warnings.filterwarnings('ignore')

test_number = 100
BT_seach_time = 15
BT_connection_test_time = 120


class Bluetooth(object):

    def __init__(self):

        self.dev = None

    async def bluetooth_scan(self, name=None, timeout=15):

        self.scanner = BS()

        if name is None:

            dev = await self.scanner.discover()

            print(dev)

            return dev

        else:

            self.dev = await self.scanner.find_device_by_name(name, timeout=timeout)

            print('the name:', self.dev.name)
            print('the address:', self.dev.address)
            print('the rssi:', self.dev.rssi)
            print('the metadata:', self.dev.metadata)
            print('the details:', dir(self.dev.details))

            self.dev = self.dev.address

            return self.dev

    async def bluetooth_connect(self, name=None):

        self.client = BC(self.dev)

        await self.client.connect()

        print('connected status:', self.client.is_connected)

        services = self.client.services

        for i in services:
            print('service UUID:', i.uuid)
            for character in i.characteristics:
                print(character)
                print('特征值UUID:', character.uuid)
                print('特征值属性:', character.properties)

        print(self.client.services._BleakGATTServiceCollection__services)

        """

        with BC(self.dev) as client:
            print('connected status:', client.is_connected)

            print(client.services._BleakGATTServiceCollection__services)

            for i in client.services:
                print(i)

                for j in i.characteristics:
                    print(j)
        """
    async def bluetooth_write(self, uuid, data):

        ret = await self.client.write_gatt_char(uuid, data)

        return ret

    async def bluetooth_read(self, uuid):

        ret = await self.client.read_gatt_char(uuid)

        return ret

    async def bluetooth_disconnect(self):

        await self.client.disconnect()

    async def bluetooth_pair(self, password=None):

        if password is None:

            return self.client.pair(protection_level=2)

        else:
            return self.client.pair(protection_level=2, password=password)

    async def bluetooth_unpair(self):

        return self.client.unpair(protection_level=2)
