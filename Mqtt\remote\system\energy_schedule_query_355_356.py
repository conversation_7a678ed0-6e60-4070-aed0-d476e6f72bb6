from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 能量调度策略查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 355

cmd_type_c2s = 356

attr_map_s2c = {

    **attr_map_common_opt,

    'sn': ('SN', 'int'),
    'id': ('', 'int'),
}

attr_map_c2s = {

    **attr_map_common_opt,

    **attr_map_common_result,

    'current_id': ('', 'int'),
    'list': ('list', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class EnergyScheduleQuery(object):

    def energy_schedule_get(self, *args, **kwargs):
        """  energy schedule get-T355/356

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
        Kwargs:
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | sn |  string | 设备序列号 |
            | id | int | 当前工作序列号,配合opt=2使用 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Energy Schedule Get | opt=1 |
        | ${status} = | Energy Schedule Get | opt=2 | id=33210 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def energy_schedule_get_result_check(self, _response, **kwargs):
        """  energy schedule get result check-T356

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Energy Schedule Set | opt=0 |
        | ${status} = | Energy Schedule Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def energy_schedule_get_from_aws_get(self, *args, **kwargs):
        """  Energy Schedule get from AWS,the message get-T355

        Args：
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | id | int | 当前工作序列号,配合opt=2使用 |
            | sn |  string | 设备序列号 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Energy Schedule Get From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def energy_schedule_get_from_aws_get_check(self, **kwargs):
        """  energy schedule get from AWS,the message get check-T355

        Kwargs:
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | id | int | 当前工作序列号,配合opt=2使用 |
            | sn |  string | 设备序列号 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Energy Schedule Get From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.energy_schedule_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def energy_schedule_get_from_device_get(self, *args, **kwargs):
        """   energy schedule get response from device,the message get-T356

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Energy Schedule Get From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def energy_schedule_get_from_device_get_check(self, **kwargs):
        """  energy schedule get response from device,the message get check-T356

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Energy Schedule Get From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.energy_schedule_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def energy_schedule_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T355/356

        Args for T355:
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | id | int | 当前工作序列号,配合opt=2使用 |
            | sn |  string | 设备序列号 |
        Args for T356：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 355/356 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=355 | rx_time_window=300 |
        | ${status} | Energy Schedule Get From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.energy_schedule_get_from_device_get, cmd_type_s2c: self.energy_schedule_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def energy_schedule_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T355/356

        Kwargs for T355:
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | id | int | 当前工作序列号,配合opt=2使用 |
            | sn |  string | 设备序列号 |
        Kwargs for T356：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，1：查询工作序列表，2:查询工作序列表详情 |
            | current_id | int | 当前工作序列号 |
            | list | int | 工作序列列表，其中包含id/name/data/electricity_type/reserved_soc/scheduling_type |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 355/356 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=356 | rx_time_window=300 | filter_mode=and |
        | ${status} | Energy Schedule Get From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.energy_schedule_get_from_device_get_check, cmd_type_s2c: self.energy_schedule_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
