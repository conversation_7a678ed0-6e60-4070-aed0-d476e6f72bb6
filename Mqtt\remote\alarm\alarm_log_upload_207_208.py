from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 告警日志上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 207

cmd_type_s2c = 208

attr_map_s2c = {

    **attr_map_common_result,
    'alarmId': ('alarm_log_id', 'int'),

}

attr_map_c2s = {

    'alarmEqSn': ('alarm_device_sn', 'string'),
    'alarmCode': ('alarm_code', 'string'),
    'level': ('alarm_level', 'int'),
    'startTime': ('start_time', 'string'),
    'endTime': ('end_time', 'string'),
    'alarmId': ('alarm_log_id', 'int'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AlarmLogUpload(object):

    def alarm_log_from_aws_get(self, *args, **kwargs):
        """  the  alarm log upload response from AWS,the message get-T208

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | alarm_log_id | int | 告警日志ID |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Log From AWS Get | alarm_log_id |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_log_from_aws_get_check(self, **kwargs):
        """  the  alarm log upload response from AWS,the message get check-T208

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | alarm_log_id | int | 告警日志ID |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Log From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_log_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_log_from_device_get(self, *args, **kwargs):
        """  the  alarm log from device,the message get-T207

        Args：
            | alarm_device_sn | string | 告警设备编号 |
            | alarm_code | string | 告警编码 |
            | alarm_level | int | 告警等级 |
            | start_time | string | 告警生成时间 |
            | end_time | string | 告警结束时间 |
            | alarm_log_id | int | 告警日志ID |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Log From Device Get | alarm_code |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_log_from_device_get_check(self, **kwargs):
        """  the alarm log from device,the message get check-T207

        Kwargs:
            | alarm_device_sn | string | 告警设备编号 |
            | alarm_code | string | 告警编码 |
            | alarm_level | int | 告警等级 |
            | start_time | string | 告警生成时间 |
            | end_time | string | 告警结束时间 |
            | alarm_log_id | int | 告警日志ID |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Log From Device Get Check | current_alarm_list=@{EMPTY} |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_log_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_log_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T207/208

        Args for T207:
            | alarm_device_sn | string | 告警设备编号 |
            | alarm_code | string | 告警编码 |
            | alarm_level | int | 告警等级 |
            | start_time | string | 告警生成时间 |
            | end_time | string | 告警结束时间 |
            | alarm_log_id | int | 告警日志ID |
        Args for T208：
            | result | int | 结果，0:成功，1:失败 |
            | alarm_log_id | int | 告警日志ID |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 207/208 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=207 | rx_time_window=300 |
        | ${status} | Alarm Log From Msg Get | alarm_code | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_log_from_device_get, cmd_type_s2c: self.alarm_log_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def alarm_log_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T207/208

        Kwargs for T207:
            | alarm_device_sn | string | 告警设备编号 |
            | alarm_code | string | 告警编码 |
            | alarm_level | int | 告警等级 |
            | start_time | string | 告警生成时间 |
            | end_time | string | 告警结束时间 |
            | alarm_log_id | int | 告警日志ID |
        Kwargs for T208：
            | result | int | 结果，0:成功，1:失败 |
            | alarm_log_id | int | 告警日志ID |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 207/208 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=208 | rx_time_window=300 | filter_mode=and |
        | ${status} | Alarm Log From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_log_from_device_get_check, cmd_type_s2c: self.alarm_log_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
