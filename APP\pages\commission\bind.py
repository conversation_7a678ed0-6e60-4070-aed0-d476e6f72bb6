from APP.base import Base
from robot.api import logger
from .locator import get_locator
from APP.util import ret_value_processing
from time import sleep


class Bind(Base):

    def go_to_commission_bind_or_unbind_page(self):
        """ Go to system->commission->bind or unbind page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Bind Or Unbind Page |
        """
        value = "locator_bind_or_unbind"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_bind_or_unbind_page(self, operate='complete'):
        """ Go to system->commission->bind or unbind page and go back to the system->commission page or next page

        Kwargs:
            | operate | string | exit or complete(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Bind Or Unbind Page | operate=exit |
            | Exit Commission Bind Or Unbind Page | operate=complete |
        """
        _operate = operate.lower()

        if operate == 'exit':

            self._general_backward_upper_page_type2()

        elif operate == 'complete':

            self._system_wait_to_show(locator="locator_general_complete", click=False)

    def commission_bind(self, **kwargs):
        """ Config Bind/Unbind information

        Kwargs:
            | operate | string | unbind or complete(默认） |
            | bind | string | Email |
            | financier |  string |
            | distributor | string |
        Return:
            | None |
        Examples:
            | Config Commission Bind Or Unbind | operate=unbind |
            | Config Commission Bind Or Unbind | bind=<EMAIL>  | financier=Sunnova1    | distributor=CED |
            | Config Commission Bind Or Unbind | bind=<EMAIL>  |
        """
        self.config_commission_bind_or_unbind(**kwargs)

    def config_commission_bind_or_unbind(self, **kwargs):
        """ Config Bind/Unbind information

        Kwargs:
            | operate | string | unbind or complete(默认） |
            | bind | string | Email |
            | financier |  string |
            | distributor | string |
        Return:
            | None |
        Examples:
            | Config Commission Bind Or Unbind | operate=unbind |
            | Config Commission Bind Or Unbind | bind=<EMAIL>  | financier=Sunnova1    | distributor=CED |
            | Config Commission Bind Or Unbind | bind=<EMAIL>  |
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        operate = kwargs.pop('operate', 'complete')

        if operate == 'unbind':

            value = "locator_unbind"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            self._system_confirm_locator()

        else:

            bind = kwargs.get('bind', None)

            financier = kwargs.get('financier', None)

            distributor = kwargs.get('distributor', None)

            _list = [bind, financier, distributor]

            result_list = [i is not None for i in _list]

            if any(result_list):

                if bind is not None:

                    locator_name = "locator_bind_address_edit"

                    ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

                    ele.click()

                    ele.clear()

                    ele.send_keys(bind)

                if financier is not None:

                    locator_name = "locator_bind_financier"

                    eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                    ele = eles[-1]

                    self.set_value_in_seekbar(ele, target_attribute='financier', _dict=kwargs)

                if distributor is not None:

                    locator_name = "locator_bind_distibutor"

                    eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                    ele = eles[-1]

                    self.set_value_in_seekbar(ele, target_attribute='distributor', _dict=kwargs)

                value = "locator_bind"

                self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

                self._system_confirm_locator()

                sleep(0.5)

                # confirm the firmware upgrading
                locator = "locator_general_confirm"

                _locator = get_locator(locator)

                ele = self.find_element(*_locator, throw_exception=False, timeout=1, click=True)

        self._system_wait_to_show(locator="locator_general_complete", click=True, timeout=30)

    def get_commission_bind_or_unbind_parameters(self, *args, **kwargs):
        """ get commission->Bind or Unbind parameters

        args:
            | bind | string | Email |
            | financier |  string |
            | distributor | string |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission Bind Or Unbind Parameters  |
            | ${result}	| Get Commission Bind Or Unbind Parameters  |  distributor | bind |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        self._get_bind_parameters(ret_dict)

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_bind_parameters(self, ret_dict):

        locator_name = "locator_bind_address"

        _value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='text', ctrl_enter_strip=False, ctrl_empty_strip=False, replace_empty_to_dash=False, ctrl_lower_case=False)

        ret_dict.update({"bind": _value})

        locator_name = "locator_bind_financier"

        eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        _value = self.get_element_attribute(eles[-1], "content-desc")

        ret_dict.update({"financier": _value})

        locator_name = "locator_bind_distibutor"

        value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True, ctrl_lower_case=False)

        ret_dict.update({"distributor": value})
