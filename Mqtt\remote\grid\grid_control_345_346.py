from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 电网配置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 345

cmd_type_c2s = 346

attr_map_s2c = {

    **attr_map_common_opt,

    'batFd': ('allow_battery_feed', 'int'),
    'solarFd': ('allow_pv_feed', 'int'),
    'gridCh': ('allow_grid_feed', 'int'),
    'solarCh': ('allow_battery_charge_from_pv', 'int'),

    'sysFdPw': ('allow_system_feed_power_threshold', 'int'),
    'gridChPw': ('allow_grid_charge_power', 'int'),
    'solarChPw': ('allow_pv_charge_power', 'int'),


    'sysFdRt': ('allow_system_feed_power_ratio', 'int'),
    'gridChRt': ('allow_grid_charge_power_ratio', 'int'),
    'solarChRt': ('allow_pv_charge_power_ratio', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class GridControl(object):

    def grid_control_set(self, *args, **kwargs):
        """  grid control set-T345/346

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Grid Control Set | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def grid_control_set_result_check(self, _response, **kwargs):
        """  grid control set result check-T346

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | grid control Set | opt=0 |
        | ${status} = | Grid Control Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def grid_control_set_from_aws_get(self, *args, **kwargs):
        """  grid control set from AWS,the message get-T345

        Args：
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Grid Control Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def grid_control_set_from_aws_get_check(self, **kwargs):
        """  grid control set from AWS,the message get check-T345

        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Grid Control Set From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.grid_control_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def grid_control_set_from_device_get(self, *args, **kwargs):
        """   grid control set response from device,the message get-T346

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Grid Control Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def grid_control_set_from_device_get_check(self, **kwargs):
        """  grid control set response from device,the message get check-T346

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Grid Control Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.grid_control_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def grid_control_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T345/346

        Args for T345:
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Args for T346：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 345/346 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=345 | rx_time_window=300 |
        | ${status} | Grid Control Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.grid_control_set_from_device_get, cmd_type_s2c: self.grid_control_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def grid_control_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T345/346

        Kwargs for T345:
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs for T346：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | allow_battery_feed | int | 允许电池馈网,0:不允许，1：允许 |
            | allow_pv_feed | int | 允许光伏馈网，0:不允许，1：允许 |
            | allow_grid_feed | int | 允许电网给电池充电，0:不允许，1：允许 |
            | allow_battery_charge_from_pv | int | 允许光伏给电池充电，0:不允许，1：允许 |
            | allow_system_feed_power_threshold | int | 系统允许馈网功率上限，单位：w，-1：不限制 |
            | allow_grid_charge_power | int | 电网允许充电功率，单位：w，-1：不限制 | |
            | allow_pv_charge_power | int | 光伏允许充电功率，单位：w，-1：不限制 | |
            | allow_system_feed_power_ratio | int | 系统允许馈网功率百分比，单位：% |
            | allow_grid_charge_power_ratio | int | 电网允许充电功率百分比，单位：% |
            | allow_pv_charge_power_ratio | int | 光伏允许充电功率百分比，单位：% |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 345/346 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=346 | rx_time_window=300 | filter_mode=and |
        | ${status} | Grid Control Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.grid_control_set_from_device_get_check, cmd_type_s2c: self.grid_control_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
