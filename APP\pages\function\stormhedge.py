from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from .locator import get_locator


class StormHedge(Base):

    def config_storm_hedge(self, enable='true'):
        """ Go to settings->Storm Hedge page
        Kwargs:
            | storm_hedge | bool | true/false |
            | enable_backup_before_storm_hours | string | 1 hour/2 hours |
            | enable_backup_before_storm_minutes | string | 0/30 minutes |
            | before_event_power_backup | string | auto_activate/ask_each_time |
            | nws_weather_event_type | string | Ashfall/Blizzard....Winter Storm |
        Return:
            | None |
        Examples:
            | Config Storm Page |

        """
        locator_name = "locator_storm_hedge_switch"

        status = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

        if status == 'false' and enable == 'true':

            self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif status == 'true' and enable == 'false':

            self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # backup settings
        value = 'locator_backup_settings'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # 设置时间 "1 hour" 和 "30 minutes"
        for time_value, target_locator in [('1 hour', 'locator_time_hour'), ('30 minutes', 'locator_time_min')]:
            value = 'locator_time_before_storm'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            self.set_value_in_scroll_element(value=time_value, locator_fun=get_locator, target_locator=target_locator)

        # 点击其他选项: locator_auto_activate_backup_power, locator_ask_each_time, locator_backup_settings_save
        for value in ['locator_auto_activate_backup_power', 'locator_ask_each_time', 'locator_backup_settings_save']:
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    # config the weather warning
        value = 'locator_weather_warnings'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        for _ in range(2):
            value = 'locator_nms_weather_event_type'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            for _ in range(1, 2):
                self.swipe_up()
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # 点击 locator_backward_weather_warning
        value = 'locator_backward_weather_warning'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # 点击 locator_storm_hedge_events
        value = 'locator_storm_hedge_events'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
