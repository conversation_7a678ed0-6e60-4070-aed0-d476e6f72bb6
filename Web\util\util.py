import abc
from robot.api import logger
from robot.utils import is_truthy, is_falsy


def escape_xpath_value(value: str):

    value = str(value)

    if '"' in value and "'" in value:

        parts_wo_apos = value.split("'")

        escaped = "', \"'\", '".join(parts_wo_apos)

        return f"concat('{escaped}')"

    if "'" in value:

        return f'"{value}"'

    return f"'{value}'"


class Event:

    @abc.abstractmethod
    def trigger(self, *args, **kwargs):
        pass


def ret_value_handle(result, *args, ret_mode='auto'):
    """
    ret_mode: auto/list/dict for different ret value format
              auto for single element
    """

    ret_tmp = list()

    if ret_mode == 'auto':

        if len(result) == 1:

            return result[args[0]]

        else:

            for i in args:

                ret_tmp.append(result[i])

            return ret_tmp

    elif ret_mode == 'list':

        ret_tmp = list()

        for i in args:

            ret_tmp.append(result[i])

        return ret_tmp

    else:

        return result


def log_as_html(message):

    logger.info(message, True, False)


def ret_value_processing(args, resp=None, user_dict=None, ret_type='auto', ret_format='list'):

    logger.debug(f"the args:{args}")

    logger.debug(f"the user_dict:{user_dict}")

    ret_list = list()

    ret_dict = dict()

    if args:

        for i in args:

            if ret_format == 'list':

                ret_list.append(user_dict[i])

            else:
                try:
                    ret_dict.update({i: user_dict[i]})

                except KeyError as e:
                    logger.info(f'Fail to find the key: {i} in {user_dict}!')

                    raise e

        if ret_format == 'list':

            if ret_type == 'auto':

                if len(ret_list) == 1:

                    return ret_list[0]

            return ret_list

        else:

            return ret_dict
    else:
        resp = user_dict
        return resp


def reverse_dict(_dict=None):

    return {v: k for k, v in _dict.items()}
