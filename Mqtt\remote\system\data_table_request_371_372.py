from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 点表配置请求

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 372

cmd_type_c2s = 371

attr_map_s2c = {

    'id': ('', 'int'),

}

attr_map_c2s = {

    'id': ('', 'int'),
    'dataTableVer': ('data_table_version', 'int'),

    'mqttQos': ('mqtt_qos', 'int'),
    'mqttTopic': ('mqtt_topic', 'string'),
    'mqttSubTopic': ('mqtt_subtopic', 'string'),
    'offlineEnable': ('offline_message_enabled', 'int'),

    'type': ('data_table_type', 'int'),
    'interval': ('', 'int'),
    'status': ('', 'int'),
    'startTime': ('start_time', 'int'),
    'endTime': ('end_time', 'int'),

    'listenData': ('listen_data_fields', 'list'),
    'dataArea': ('uploaded_data_table_fields', 'list'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DataTableRequest(object):

    def data_table_request_from_aws_get(self, *args, **kwargs):
        """  data table request from AWS,the message get-T372

        Args：
            | id | int | 点表配置表的唯一标识 |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Request From AWS Get | id |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_request_from_aws_get_check(self, **kwargs):
        """  upgrade info upload get from AWS,the message get check-T372

        Kwargs:
            | id | int | 点表配置表的唯一标识 |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Data Table Request From AWS Get Check | id=24 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_request_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_request_from_device_get(self, *args, **kwargs):
        """   upgrade info upload get response from device,the message get-T371

        Args:
            | id | int | 点表配置表的唯一标识 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Request From Device Get | id |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_request_from_device_get_check(self, **kwargs):
        """  upgrade info upload get response from device,the message get check-T371

        Kwargs:
            | id | int | 点表配置表的唯一标识 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Data Table Request From Device Get Check | id=24 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_request_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_request_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T371/372

        Args for T371:
            | id | int | 点表配置表的唯一标识 |
        Args for T372：
            | id | int | 点表配置表的唯一标识 |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 371/372 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=372 | rx_time_window=300 |
        | ${status} | Data Table Request From Msg Get | id | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_request_from_device_get, cmd_type_s2c: self.data_table_request_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def data_table_request_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T371/372

        Kwargs for T371:
            | id | int | 点表配置表的唯一标识 |
        Kwargs for T372：
            | id | int | 点表配置表的唯一标识 |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 371/372 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=371 | rx_time_window=300 | filter_mode=and |
        | ${status} | Data Table Request From Msg Get Check | id=24 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_request_from_device_get_check, cmd_type_s2c: self.data_table_request_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
