from .system.login_101_102 import Login
from .system.bind_103_104 import Bind
from .system.device_para_refresh_107_108 import DeviceRefresh
from .system.cloud_para_refresh_109_110 import CloundRefresh
from .system.device_data_upload_211_212 import DeviceData
from .system.realy_switch_number_225 import RelaySwitchNumber
from .system.upgrade_info_upload_501_502 import UpgradeInfoUpload
from .system.system_install_info_503_504 import SystemInstallInfo
from .system.upgrade_505_506 import Upgrade
from .system.upgrade_result_upload_507_508 import UpgradeResultUpload
from .system.install_info_upload_509_510 import InstallInfoUpload
from .system.boot_info_upload_515_516 import SpecialUpgradeUpload
from .system.upgrade_step_info_upload_513_514 import UpgradeStepInfoUpload
from .system.upgrade_step_511_512 import UpgradeStep
from .system.device_event_upload_309_310 import DevicePush
from .system.device_control_311_312 import DeviceControl
from .system.device_control_315_316 import SystemSetting
from .system.global_system_parameter_317_318 import GlobalSystem
from .system.tou_policy_319_320 import TouPolicy
from .system.battery_control_343_344 import BatControl
from .system.auto_addressing_329_330 import AutoAdressing
from .system.auto_addressing_query_331_332 import AutoAdressingQuery
from .system.led_327_328 import LedSet
from .system.energy_schedule_query_355_356 import EnergyScheduleQuery
from .system.energy_schedule_set_357_358 import EnergyScheduleSet
from .system.modbus_set_369_370 import ModbusSet
from .system.data_table_set_373_374 import DataTableSet
from .system.data_table_request_371_372 import DataTableRequest
from .system.data_table_response_set_375_376 import DataTableResponseSet
from .system.device_sleeping_381_382 import DeviceSleeping
from .system.device_sleeping_control_385_386 import DeviceSleepingControl
from .system.ct_detection_393_394 import CT
from .system.ct_detection_result_395_396 import CtResult

from .grid.off_grid_result_upload_349_350 import OffGrid
from .grid.grid_event_upload_347_348 import GridEventPush
from .grid.grid_off_event_upload_351_352 import GridOffEventPush
from .grid.grid_control_345_346 import GridControl

from .performance.real_time_user_data_upload_201 import RealTimeUserData
from .performance.real_time_vendor_data_upload_202 import RealTimeVendorData
from .performance.real_time_data_upload_203_204 import RealTimeData
from .performance.sys_resource_monitor_file_upload_220 import ResourceMonitorFileUpload
from .performance.device_real_time_data_upload_221 import DeviceRealTimeDataProactiveUpload
from .performance.device_real_time_data_upload_227 import DeviceRealTimeDataUpload
from .performance.real_time_data_upload_reissue_209_210 import RealTimeDataReissue
from .performance.real_time_data_reissue_upload_229_230 import RealTime227Upload
from .performance.real_time_data_upload_203_237 import PowerDataReport
from .performance.backup_power_supply_log_upload_405_406 import BackupPowerSupplyLogUpload
from .performance.daily_power_upload_407_408 import DailyPowerUpload
from .performance.histrical_energy_upload_409_410 import HistricalEnergyUpload
from .performance.daily_energy_upload_411_412 import DailyEnergyUpload
from .performance.offline_221_file_upload_223_224 import Offline221Upload

from .alarm.alarm_log_upload_207_208 import AlarmLogUpload
from .alarm.alarm_log_file_upload_213 import AlarmLogFileUpload
from .alarm.current_alarm_data_upload_214_215 import AlarmDataUpload
from .alarm.current_alarm_collection_query_216_217 import AlarmQuery
from .alarm.alarm_monitor_data_upload_218_219 import AlarmSnapUpload

from .network.wifi_321_322 import WifiSet
from .network.wifi_scan_335_336 import WifiScan
from .network.wifi_hotspot_337_338 import WifiHotSpot
from .network.network_connect_status_339_340 import NetworkConnectStatus
from .network.network_switch_341_342 import NetworkSwitch
from .network.network_detection_set_361_362 import NetworkDetectionSet
from .network.network_detection_query_363_364 import NetworkDetectionResultGet

from .pv.pv_325_326 import PvSet
from .pv.pv_charging_query_359_360 import PvChargingSet

from .generator.generator_323_324 import GeneratorSet

from .smart_load.smart_load_query_353_354 import SmartLoadQuery
from .smart_load.smart_load_control_387_388 import SmartLoadControl
from .smart_load.smart_load_switch_query_389_390 import SmartLoadSwitch

from .storm.storm_mode_305_306 import StormMode
from .storm.storm_release_upload_307_308 import StormRelease

from .apbox20.apbox20_391_392 import Apbox20

from .apower20.apower20_info_upload_517_518 import Apower20InfoUpload
from .apower20.apower20_info_519_520 import APower20Info

from .debug.debug_mode_333_334 import DebugMode
from .debug.fault_record_wave_get_377_378 import FaultWaveRecordGet
from .debug.fault_wave_record_upload_379_380 import FaultWaveRecordUpload
from .debug.debug_601_602 import Debug
from .debug.device_log_collection_query_603_604 import DeviceLogUpload
from .debug.device_log_upload_result_605_606 import DeviceLogUploadResult
from .debug.fault_wave_record_log_collection_query_607_608 import FaultWaveRecording
from .debug.fault_wave_record_upload_result_609_610 import FaultWaveRecordUploadResult
from .debug.module_record_log_collection_query_611_612 import ModuleLog
from .debug.module_log_upload_result_613_614 import ModuleLogUploadResult

from .sunnova.sunnova_data_upload_enable_802_803 import SunnovaUploadEnable
from .sunnova.sunnova_data_upload_801 import sunnovaDataUpload

from .sunrun.sunrun_data_upload_231 import SunrunDataUpload
from .sunrun.sunrun_real_time_data_reissue_upload_233_234 import RealTime231Upload
from .sunrun.sunrun_data_upload_enable_235_236 import SunrunUploadEnable


class Remote(SystemInstallInfo, Upgrade, Login, Bind, DeviceRefresh, CloundRefresh,
             Debug, RealTimeUserData, RealTimeVendorData, AlarmLogUpload, RealTimeData,
             RealTimeDataReissue, DeviceData, AlarmLogFileUpload, AlarmDataUpload,
             ResourceMonitorFileUpload, DeviceRealTimeDataProactiveUpload, RelaySwitchNumber,
             DeviceRealTimeDataUpload, SunrunDataUpload, AlarmQuery, AlarmSnapUpload,
             Offline221Upload, RealTime227Upload, RealTime231Upload, SunrunUploadEnable,
             PowerDataReport, SunnovaUploadEnable, sunnovaDataUpload, BackupPowerSupplyLogUpload,
             DailyPowerUpload, HistricalEnergyUpload, DailyEnergyUpload, StormMode,
             DeviceControl, SystemSetting, GlobalSystem, TouPolicy, WifiSet, GeneratorSet,
             PvSet, LedSet, AutoAdressing, AutoAdressingQuery, DebugMode, WifiScan, WifiHotSpot,
             NetworkConnectStatus, NetworkSwitch, BatControl, GridControl, SmartLoadQuery,
             EnergyScheduleQuery, EnergyScheduleSet, PvChargingSet, NetworkDetectionSet,
             NetworkDetectionResultGet, ModbusSet, DataTableSet, DataTableResponseSet,
             FaultWaveRecordGet, DeviceSleeping, DeviceSleepingControl, SmartLoadControl,
             SmartLoadSwitch, Apbox20, CT, CtResult, UpgradeInfoUpload, UpgradeResultUpload,
             InstallInfoUpload, SpecialUpgradeUpload, APower20Info, Apower20InfoUpload, UpgradeStepInfoUpload,
             UpgradeStep, DeviceLogUpload, FaultWaveRecording, ModuleLog, OffGrid, StormRelease,
             DevicePush, GridEventPush, GridOffEventPush, DataTableRequest, FaultWaveRecordUpload,
             DeviceLogUploadResult, FaultWaveRecordUploadResult, ModuleLogUploadResult):

    pass
