from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 设备分步升级

app_name = "device"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 511

cmd_type_c2s = 512

common_dict = {

    **attr_map_common_result,

    'fileName': ('file_name', 'string'),
    'order': ('', 'string'),
}

attr_map_s2c = {

    'url': ('', 'string'),
    'cdnUrl': ('cdn_url', 'string'),
    'baseUrl': ('base_url', 'string'),
    'sign': ('', 'string'),
    'type': ('', 'int'),
    'fileSize': ('file_size', 'int'),
}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class UpgradeStep(object):

    def upgrade_step_set(self, *args, **kwargs):
        """  upgrade step set-T511/512

        Args:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Kwargs:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | url | string | string like https://hostname[:port]/path,'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/'(by default),<=128Bytes |
            | cdn_url | string | the address after CDN process,<=128Bytes |
            | base_url | string | the download address of NSC/ULG module,'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com'(by default) |
            | order | string | 升级工单号，<=32Byes|
            | sign | string | MD5 check sum,32Bytes |
            | type | int | 0:ALL,4:EMS_SL,5:PE_FPGA,6:PE_DC,7:PE_INV,8:BMS_MAIN,9:BMS_BL,10:BMS_TH,11:METER |
            | file_size | int | 文件大小，单位：byte |
            | internal_file_name | string | the internal file name in AWS server |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${result} | Upgrade Step Set | file_name=ibg_ud_V10R10B09D00_ems_0511_v1.zip  | internal_file_name=FILE_43968387246488586.zip |order=DH20240517090359780 | signature=d429ace67efd73ec06c36b0c164510fb | file_size=55883352 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        file_name = kwargs.pop('file_name', 'ibg_ud_V10R10B09D00_ems_0511_v1.zip')
        internal_file_name = kwargs.pop('internal_file_name', 'FILE_43968387246488586.zip')

        url = kwargs.pop('url', f'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/{internal_file_name}')
        base_url = kwargs.pop('base_url', 'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com')
        cdn_url = kwargs.pop('cdn_url', f'https://d2mqobdvzs4df5.cloudfront.net/Iot_Firmware/{internal_file_name}')

        signature = kwargs.pop('sign', 'd429ace67efd73ec06c36b0c164510fb')
        _type = int(kwargs.pop('type', 0))
        file_size = kwargs.pop('file_size', 55883352)
        step_upgrade = int(kwargs.pop('step_upgrade', 1))

        order = kwargs.pop('order', 'DH20240517090359780')

        tx_dict = dict(fileName=file_name, url=url, cdnUrl=cdn_url, baseUrl=base_url, type=_type, fileSize=file_size, sign=signature, stepUpgrade=step_upgrade, order=order)

        if kwargs:

            tx_dict.setdefault(**kwargs)

        kwargs = tx_dict

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def upgrade_step_set_check(self, *args, **kwargs):
        """  upgrade step set check-T511/512

        Args：
            | None |
        Kwargs:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Step Set Check | result=0 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        func_get = self.upgrade_step_set

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_step_set_from_aws_get(self, *args, **kwargs):
        """  upgrade info upload get from AWS,the message get-T511

        Args：
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | url | string | string like https://hostname[:port]/path,'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/'(by default),<=128Bytes |
            | cdn_url | string | the address after CDN process,<=128Bytes |
            | base_url | string | the download address of NSC/ULG module,'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com'(by default) |
            | order | string | 升级工单号，<=32Byes|
            | sign | string | MD5 check sum,32Bytes |
            | type | int | 0:ALL,4:EMS_SL,5:PE_FPGA,6:PE_DC,7:PE_INV,8:BMS_MAIN,9:BMS_BL,10:BMS_TH,11:METER |
            | file_size | int | 文件大小，单位：byte |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Step Set From AWS Get | order |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_step_set_from_aws_get_check(self, **kwargs):
        """  upgrade info upload get from AWS,the message get check-T511

        Kwargs:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | url | string | string like https://hostname[:port]/path,'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/'(by default),<=128Bytes |
            | cdn_url | string | the address after CDN process,<=128Bytes |
            | base_url | string | the download address of NSC/ULG module,'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com'(by default) |
            | order | string | 升级工单号，<=32Byes|
            | sign | string | MD5 check sum,32Bytes |
            | type | int | 0:ALL,4:EMS_SL,5:PE_FPGA,6:PE_DC,7:PE_INV,8:BMS_MAIN,9:BMS_BL,10:BMS_TH,11:METER |
            | file_size | int | 文件大小，单位：byte |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Step Set From AWS Get Check | type=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_step_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_step_set_from_device_get(self, *args, **kwargs):
        """   upgrade info upload get response from device,the message get-T512

        Args:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Step Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_step_set_from_device_get_check(self, **kwargs):
        """  upgrade info upload get response from device,the message get check-T512

        Kwargs:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Step Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_step_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_step_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T511/512

        Args for T511:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | url | string | string like https://hostname[:port]/path,'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/'(by default),<=128Bytes |
            | cdn_url | string | the address after CDN process,<=128Bytes |
            | base_url | string | the download address of NSC/ULG module,'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com'(by default) |
            | order | string | 升级工单号，<=32Byes|
            | sign | string | MD5 check sum,32Bytes |
            | type | int | 0:ALL,4:EMS_SL,5:PE_FPGA,6:PE_DC,7:PE_INV,8:BMS_MAIN,9:BMS_BL,10:BMS_TH,11:METER |
            | file_size | int | 文件大小，单位：byte |
        Args for T512：
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 501/502 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=511 | rx_time_window=300 |
        | ${status} | Upgrade Step Set From Msg Get | file_name | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_step_set_from_device_get, cmd_type_s2c: self.upgrade_step_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def upgrade_step_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T511/512

        Args for T511:
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | url | string | string like https://hostname[:port]/path,'https://web-file-library.s3-us-west-2.amazonaws.com/Iot_Firmware/'(by default),<=128Bytes |
            | cdn_url | string | the address after CDN process,<=128Bytes |
            | base_url | string | the download address of NSC/ULG module,'https://device-ems-sa-upgrade-test.s3.us-west-2.amazonaws.com'(by default) |
            | order | string | 升级工单号，<=32Byes|
            | sign | string | MD5 check sum,32Bytes |
            | type | int | 0:ALL,4:EMS_SL,5:PE_FPGA,6:PE_DC,7:PE_INV,8:BMS_MAIN,9:BMS_BL,10:BMS_TH,11:METER |
            | file_size | int | 文件大小，单位：byte |
        Kwargs for T512：
            | file_name |  string | the file name with tar.bz2或bin文件，<=64Byes |
            | order | string | 升级工单号，<=32Byes|
            | result | int | 0：下发成功；1：禁止升级；2：解析错误；3：类型不匹配；4：升级消息发送失败；5：(下发成功)该工单升级中 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 501/502 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=512 | rx_time_window=300 | filter_mode=and |
        | ${status} | Upgrade Step Set From Msg Get Check| result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_step_set_from_device_get_check, cmd_type_s2c: self.upgrade_step_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
