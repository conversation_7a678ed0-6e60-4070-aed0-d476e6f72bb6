from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name
from ..constant import attr_map_common

# 设备登录上行数据/设备登录应答下行数据

app_name = "auth"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_c2s = 101

cmd_type_s2c = 102

attr_map_c2s = {
    'IBG_SN': ('device', 'string'),
    'deviceType': ('device_type', 'int'),
    'timeStamp': ('timestamp', 'int'),
    'protocolVer': ('protocol_version', 'string'),
    'SysHdVersion': ('hardware_version', 'int'),
    'deviceVer': ('device_version', 'int'),
    'dataTableVer': ('data_table_version', 'int'),
}


attr_map_s2c = {
    **attr_map_common,
    'url': ('upload_url', 'string'),
    'dataUrl': ('battery_data_upload_url', 'string'),  # 废弃
    'idList': ('data_table_list', 'list'),
}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class Login(Base):

    def login_info_from_device_get(self, *args, **kwargs):
        """  Device Login Info get-T101

        Args：
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | timestamp | int | 登录时戳 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | hardware_version | int | 硬件版本号，100:1.0,101:1.1,102:1.2,103:1.3 |
            | device_version | int | 设备版本号,0:1.0 software,1:2.0 software |
            | data_table_version | int | 点表版本 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Login Info From Device Get | device | protocol_version | device_version | hardware_version |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def login_info_from_device_get_check(self, **kwargs):
        """  Device Login Info get check-T101

        Kwargs:
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | timestamp | int | 登录时戳 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | hardware_version | int | 硬件版本号，100:1.0,101:1.1,102:1.2,103:1.3 |
            | device_version | int | 设备版本号,0:1.0 software,1:2.0 software |
            | data_table_version | int | 点表版本 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${status} = | Login Info From Device Get Check | device_type=0 | device_version=1 | hardware_version=103 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.login_info_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def login_info_from_aws_get(self, *args, **kwargs):
        """  AWS to Device Login Info get-T102

        Args：
            | result |  int | 0:成功,1:失败  |
            | reason | int | 0:登录成功,1: 设备未注册,2:设备异常,3:接收包类型异常 |
            | upload_url | string | <=128Bytes,实时日志和报告日志的上传URL |
            | battery_data_upload_url | string | 电池数据的上传URL，已废弃 |
            | data_table_list | list | 点表 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Login Info From Aws Get | device | protocol_version | device_version | hardware_version |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def login_info_from_aws_get_check(self, **kwargs):
        """  AWS to Device Login Info get check-T102

        Kwargs:

            | result |  int | 0:成功,1:失败  |
            | reason | int | 0:登录成功,1: 设备未注册,2:设备异常,3:接收包类型异常 |
            | upload_url | string | <=128Bytes,实时日志和报告日志的上传URL |
            | battery_data_upload_url | string | 电池数据的上传URL，已废弃 |
            | data_table_list | list | 点表 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${status} = | Login Info From Aws Get Check | result=0 | reason=0 | upload_url=http://18.144.74.200:9005/v1/file/upload |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.login_info_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def login_info_from_msg_get(self, *args, msg=None, cmd_type=101, **kwargs):
        """  the monitored messages get  between device<->AWS cloud-T101/102

        Args for T101：
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | timestamp | int | 登录时戳 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | hardware_version | int | 硬件版本号，100:1.0,101:1.1,102:1.2,103:1.3 |
            | device_version | int | 设备版本号,0:1.0 software,1:2.0 software |
            | data_table_version | int | 点表版本 |
        Args for T102：
            | result |  int | 0:成功,1:失败  |
            | reason | int | 0:登录成功,1: 设备未注册,2:设备异常,3:接收包类型异常 |
            | upload_url | string | <=128Bytes,实时日志和报告日志的上传URL |
            | battery_data_upload_url | string | 电池数据的上传URL，已废弃 |
            | data_table_list | list | 点表 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 101 or 102 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${packets} | Receive | expected_cmd_type=102/101 | rx_time_window=300 | filter_mode=and |
        | ${status} | Login Info From Msg Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets} | cmd_type=101 |
        | ${status} | Login Info From Msg Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets} | cmd_type=101 | ret_format=dict |
        | ${status} | Login Info From Msg Get | upload_url | msg=${packets} | cmd_type=102 |
        | ${status} | Login Info From Msg Get | upload_url | msg=${packets} | cmd_type=102 | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=101 | rx_time_window=300 |
        | ${status} | Login Info From Msg Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets}[0] | cmd_type=101 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.login_info_from_device_get, cmd_type_s2c: self.login_info_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def login_info_from_msg_get_check(self, msg=None, cmd_type=101, **kwargs):
        """  the monitored messages get check between device<->AWS cloud-T101/102

        Kwargs for T101：
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | timestamp | int | 登录时戳 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | hardware_version | int | 硬件版本号，100:1.0,101:1.1,102:1.2,103:1.3 |
            | device_version | int | 设备版本号,0:1.0 software,1:2.0 software |
            | data_table_version | int | 点表版本 |
        Kwargs for T102：
            | result |  int | 0:成功,1:失败  |
            | reason | int | 0:登录成功,1: 设备未注册,2:设备异常,3:接收包类型异常 |
            | upload_url | string | <=128Bytes,实时日志和报告日志的上传URL |
            | battery_data_upload_url | string | 电池数据的上传URL，已废弃 |
            | data_table_list | list | 点表 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 101 or 102 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${packets} | Receive | expected_cmd_type=102/101 | rx_time_window=300 | filter_mode=and |
        | ${status} | Login Info From Msg Get Check | device=ULG_EMSTEST_T004 | device_type=0 | protocol_version=V1.11.00 | hardware_version=103 | msg=${packets} | cmd_type=101 |
        | ${status} | Login Info From Msg Get Check | upload_url=http://52.53.190.143:11001/manage/file/upload | msg=${packets} | cmd_type=102 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.login_info_from_device_get_check, cmd_type_s2c: self.login_info_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
