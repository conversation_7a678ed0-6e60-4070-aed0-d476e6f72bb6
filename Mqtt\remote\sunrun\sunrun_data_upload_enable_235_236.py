from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result, attr_map_common_opt

# sunrun数据上传使能

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 236

cmd_type_s2c = 235

attr_map_s2c = {

    **attr_map_common_opt,

    'sunrunEn': ('sunrun_enabled', 'int'),
    'sunrunPeriod': ('sunrun_period', 'int'),
    'sunrunDurationTime': ('sunrun_duration', 'int'),

}

attr_map_c2s = {

    **attr_map_common_opt,
    **attr_map_common_result,

    'sunrunEn': ('sunrun_enabled', 'int'),
    'sunrunPeriod': ('sunrun_period', 'int'),
    'sunrunDurationTime': ('sunrun_duration', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SunrunUploadEnable(object):

    def sunrun_data_upload_enabled(self, *args, **kwargs):
        """  sunrun data upload enabled-T235/T236

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunrun_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，单位：s |
            | sunrun_duration | int | 数据持续上传时间，单位：s |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunrun Data Upload Enabled | sunrun_duration | sunrun_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def sunrun_data_upload_from_aws_get(self, *args, **kwargs):
        """  sunrun data upload from AWS,the message get-T235

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，单位：s |
            | sunrun_duration | int | 数据持续上传时间，单位：s |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunrun Data Upload From AWS Get | sunrun_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def sunrun_data_upload_from_aws_get_check(self, **kwargs):
        """  sunrun data upload from AWS,the message get check-T235

        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，单位：s |
            | sunrun_duration | int | 数据持续上传时间，单位：s |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Sunrun Data Upload From AWS Get Check | sunrun_enabled=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunrun_data_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunrun_data_upload_from_device_get(self, *args, **kwargs):
        """  sunrun data upload from device,the message get-T236

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunrun_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunrun Data Upload From Device Get | sunrun_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def sunrun_data_upload_from_device_get_check(self, **kwargs):
        """  sunrun data upload from device,the message get check-T236

        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunrun_duration | int | 数据持续上传时间，60s（默认），单位：s |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Sunrun Data Upload From Device Get Check | sunrun_enabled=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunrun_data_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunrun_data_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T235/T236

        Args for T235:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，单位：s |
            | sunrun_duration | int | 数据持续上传时间，单位：s |
        Args for T236：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunrun_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 235/236 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=236 | rx_time_window=300 |
        | ${status} | Sunrun Data Upload From Msg Get | sunrun_enabled | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunrun_data_upload_from_device_get, cmd_type_s2c: self.sunrun_data_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def sunrun_data_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T235/T236

        Kwargs for T235:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，单位：s |
            | sunrun_duration | int | 数据持续上传时间，单位：s |
        Kwargs for T236：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败 |
            | sunrun_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunrun_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunrun_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 235/236 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=236 | rx_time_window=300 | filter_mode=and |
        | ${status} | Sunrun Data Upload From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunrun_data_upload_from_device_get_check, cmd_type_s2c: self.sunrun_data_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
