from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备网络连接状态查询

cmd_type_c2s = 1113

cmd_type_s2c = 1114

attr_map_c2s = {


    **attr_map_common_opt,

}

attr_map_s2c = {

    **attr_map_c2s,

    'routerStatus': ('router', 'int'),
    'result': ('result', 'int'),
    'netStatus': ('net', 'int'),
    'awsStatus': ('aws', 'int'),
    'EthConnectRouterStatus': ('Eth', 'int'),
    'wifiConnectRouterStatus': ('WiFi', 'int'),
    '4GConnectBSStatus': ('4G', 'int'),
    'WifiSignalStrength': ('WiFi_signal', 'int'),
    '4GSignalStrength': ('4G_signal', 'int'),
    'currentNetType': ('current_network', 'int')
}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalNetwork(Base):

    def local_network_connect_status_get(self, *args, **kwargs):
        """  local network connection status get-T1113
        Args：
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Network Connect Status Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_network_connect_status_get_result_check(self, _response, **kwargs):
        """  local wifi hotspot set result check-T1114

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Network Connect Status Get |
        | ${status} = | Local Network Connect Status Get Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
