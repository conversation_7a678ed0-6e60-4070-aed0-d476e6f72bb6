from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备网络属性查询设置

cmd_type_c2s = 1117

cmd_type_s2c = 1118


attr_map_c2s = {

    **attr_map_common_opt,

    'currentNetType': ('current_network_type', 'int'),
    'wifiDHCP': ('wifi_dhcp_enabled', 'int'),
    'wifiMAC': ('wifi_mac', 'string'),
    'wifiStaticIP': ('wifi_static_ip', 'string'),
    'wifiDNS': ('wifi_dns', 'int'),
    'wifiGateWay': ('wifi_gateway', 'string'),

    'eth0DHCP': ('eth0_dhcp_enabled', 'int'),
    'eth0MAC': ('eth0_mac', 'string'),
    'eth0StaticIP': ('eth0_Static_IP', 'string'),
    'eth0DNS': ('eth0_dns', 'string'),
    'eth0GateWay': ('eth0_gateway', 'string'),

    'eth1DHCP': ('eth1_dhcp_enabled', 'int'),
    'eth1MAC': ('eth1_mac', 'string'),
    'eth1StaticIP': ('eth1_Static_IP', 'string'),
    'eth1DNS': ('eth1_dns', 'string'),
    'eth1GateWay': ('eth1_gateway', 'string'),

    'operatorDNS': ('4G_dns', 'string'),
    'operatorMAC': ('4G_mac', 'string'),
    'operatorRSSI': ('4G_RSSI', 'int'),
    'awsStatus': ('aws_connection_status', 'int')

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}
com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalNetworkSet(Base):

    def local_network_set(self, *args, **kwargs):
        """  local network connection set-T1117
        Args：
            | result | int | 操作结果，0：成功；1：失败 |
            | reason | int | 原因，0：操作成功；1：静态IP格式错误；2：MAC格式错误；3：DNS格式错误 |
            | opt | int | 操作类型，0:查询，1：设置 |
            | current_network_type | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | wifi_dhcp_enabled | int | WiFI DHCP使能，0：不启用，1：启用 |
            | wifi_mac | string | WiFI网卡 MAC地址 |
            | wifi_static_ip | string | WiFI静态IP |
            | wifi_dns | int | WiFI DNS |
            | wifi_gateway | WiFi网关 |
            | eth0_DHCP_enabled | int | Eth0 DHCP使能，0：不启用，1：启用 |
            | eth0_MAC | string | Eth0 MAC地址 |
            | eth0_Static_IP | string | Eth0 静态IP |
            | eth0_DNS | string | Eth0 DNS |
            | eth0_gateway | string | Eth0 网关 |
            | eth1_DHCP_enabled | int | Eth1 DHCP使能，0：不启用，1：启用 |
            | eth1_MAC | string | Eth1 MAC地址 |
            | eth1_Static_IP | string | Eth1 静态IP |
            | eth1_DNS | string | Eth1 DNS |
            | eth1_gateway | string | Eth1 网关 |
            | 4G_DNS | string | 4G DNS |
            | 4G_MAC | string | 4G MAC地址 |
            | 4G_RSSI | int | 4G RSSI |
            | aws_connection_status | int | AWS连接状态 | 0：未连接；1：连接 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1：设置 |
            | current_network_type | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | wifi_dhcp_enabled | int | WiFI DHCP使能，0：不启用，1：启用 |
            | wifi_mac | string | WiFI网卡 MAC地址 |
            | wifi_static_ip | string | WiFI静态IP |
            | wifi_dns | int | WiFI DNS |
            | wifi_gateway | WiFi网关 |
            | eth0_DHCP_enabled | int | Eth0 DHCP使能，0：不启用，1：启用 |
            | eth0_MAC | string | Eth0 MAC地址 |
            | eth0_Static_IP | string | Eth0 静态IP |
            | eth0_DNS | string | Eth0 DNS |
            | eth0_gateway | string | Eth0 网关 |
            | eth1_DHCP_enabled | int | Eth1 DHCP使能，0：不启用，1：启用 |
            | eth1_MAC | string | Eth1 MAC地址 |
            | eth1_Static_IP | string | Eth1 静态IP |
            | eth1_DNS | string | Eth1 DNS |
            | eth1_gateway | string | Eth1 网关 |
            | 4G_DNS | string | 4G DNS |
            | 4G_MAC | string | 4G MAC地址 |
            | 4G_RSSI | int | 4G RSSI |
            | aws_connection_status | int | AWS连接状态 | 0：未连接；1：连接 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Network Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_network_connect_status_get_result_check(self, _response, **kwargs):
        """  local network set result check-T1118

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 操作结果，0：成功；1：失败 |
            | reason | int | 原因，0：操作成功；1：静态IP格式错误；2：MAC格式错误；3：DNS格式错误 |
            | opt | int | 操作类型，0:查询，1：设置 |
            | current_network_type | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | wifi_dhcp_enabled | int | WiFI DHCP使能，0：不启用，1：启用 |
            | wifi_mac | string | WiFI网卡 MAC地址 |
            | wifi_static_ip | string | WiFI静态IP |
            | wifi_dns | int | WiFI DNS |
            | wifi_gateway | WiFi网关 |
            | eth0_DHCP_enabled | int | Eth0 DHCP使能，0：不启用，1：启用 |
            | eth0_MAC | string | Eth0 MAC地址 |
            | eth0_Static_IP | string | Eth0 静态IP |
            | eth0_DNS | string | Eth0 DNS |
            | eth0_gateway | string | Eth0 网关 |
            | eth1_DHCP_enabled | int | Eth1 DHCP使能，0：不启用，1：启用 |
            | eth1_MAC | string | Eth1 MAC地址 |
            | eth1_Static_IP | string | Eth1 静态IP |
            | eth1_DNS | string | Eth1 DNS |
            | eth1_gateway | string | Eth1 网关 |
            | 4G_DNS | string | 4G DNS |
            | 4G_MAC | string | 4G MAC地址 |
            | 4G_RSSI | int | 4G RSSI |
            | aws_connection_status | int | AWS连接状态 | 0：未连接；1：连接 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Network Set |
        | ${status} = | Local Network Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
