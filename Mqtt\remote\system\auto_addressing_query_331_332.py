from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 自动编址结果

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 331

cmd_type_c2s = 332

attr_map_s2c = {

    **attr_map_common_opt,

}

attr_map_c2s = {

    **attr_map_common,

    **attr_map_s2c,

    'devNum': ('device_number', 'int'),
    'isOver': ('addressing_status', 'int'),
    'devMap': ('device_map', 'list'),
    'snMap': ('SN_map', 'list'),
    'findSn': ('current_SN', 'list'),

    'apbox20Num': ('apbox2.0_number', 'int'),
    'apbox20Map': ('apbox2.0_map', 'list'),
    'msaModel': ('mas_model', 'int'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AutoAdressingQuery(object):

    def auto_addressing_result_get(self, *args, **kwargs):
        """  auto addressing result get-T331/332

        Args：
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs:
            | opt | int | 0:查询 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Auto Addressing Result Get |
        | ${status} = | Auto Addressing Result Get | result | reason |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def auto_addressing_result_get_check(self, _response, **kwargs):
        """  auto addressing set result check-T332

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Auto Addressing Result Get |
        | ${status} = | Auto Addressing Result Get Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def auto_addressing_result_from_aws_get(self, *args, **kwargs):
        """  auto addressing set from AWS,the message get-T331

        Args：
            | opt | int | 0:查询 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Auto Addressing Result From AWS Get | device_number |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def auto_addressing_result_from_aws_get_check(self, **kwargs):
        """  auto addressing set from AWS,the message get check-T331

        Kwargs:
            | opt | int | 0:查询 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Auto Addressing Result From AWS Get Check | device_number=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.auto_addressing_result_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def auto_addressing_result_from_device_get(self, *args, **kwargs):
        """   auto addressing set response from device,the message get-T332

        Args：
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Auto Addressing Result From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def auto_addressing_result_from_device_get_check(self, **kwargs):
        """  auto addressing set response from device,the message get check-T332

        Kwargs:
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Auto Addressing Result From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.auto_addressing_result_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def auto_addressing_result_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T331/332

        Args for T331:
            | opt | int | 0:查询 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Args for T332:
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 331/332 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=331 | rx_time_window=300 |
        | ${status} | Auto Addressing Result From Msg Get | device_number | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.auto_addressing_result_from_device_get, cmd_type_s2c: self.auto_addressing_result_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def auto_addressing_result_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T331/332

        Kwargs for T331:
            | opt | int | 0:查询 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs for T332:
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息 |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 331/332 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=332 | rx_time_window=300 | filter_mode=and |
        | ${status} | Auto Addressing Result From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.auto_addressing_result_from_device_get_check, cmd_type_s2c: self.auto_addressing_result_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
