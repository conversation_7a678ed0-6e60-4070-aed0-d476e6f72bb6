from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 智能负载查询

cmd_type_c2s = 1411

cmd_type_s2c = 1412

attr_map_c2s = {

    **attr_map_common_opt,
    'Sw1Volt': ('smart_load1_voltage', 'int'),
    'Sw2Volt': ('smart_load2_voltage', 'int'),

    'CarSWCurr': ('charging_current_for_car', 'int'),
    'CarSWPower': ('charging_power_for_car', 'int'),
    'CarSWExpEnergy': ('charging_out_energy_for_car', 'int'),
    'CarSWImpEnergy': ('charging_in_energy_for_car', 'int'),

    'SW1Curr': ('smart_load1_current', 'int'),
    'SW2Curr': ('smart_load2_current', 'int'),

    'SW1ExpPower': ('smart_load1_power', 'int'),
    'SW2ExpPower': ('smart_load2_power', 'int'),
    'SW1ExpEnergy': ('smart_load1_energy', 'int'),
    'SW2ExpEnergy': ('smart_load2_energy', 'int'),

    'CarSwConsSupExpEnerge': ('charged_energy_for_car_with_fixed_energy_mode', 'int'),

    'power': ('generator_power', 'int'),
    'curr': ('generator_current', 'int'),
    'volt': ('generator_voltage', 'int'),
    'freq': ('generator_frequency', 'int'),
    'genpowerGen': ('generator_generated_energy', 'int'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSmartLoadGet(Base):

    def local_smart_load_get(self, *args, **kwargs):
        """  local smart load get-T1411
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0：操作成功；1：查询失败；2：设置失败 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Smart Load Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_smart_load_get_result_check(self, _response, **kwargs):
        """  local smart load get result check-T1412

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0：操作成功；1：查询失败；2：设置失败 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Smart Load Get |
        | ${status} = | Local Smart Load Get Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
