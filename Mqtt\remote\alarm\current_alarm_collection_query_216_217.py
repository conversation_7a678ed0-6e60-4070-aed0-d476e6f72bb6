from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_type_result, attr_map_common_type

# 当前告警集合查询

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 217

cmd_type_s2c = 216

attr_map_s2c = {

    **attr_map_common_type,

}

attr_map_c2s = {

    **attr_map_common_type_result,
    'curAlarm': ('current_alarm_list', 'list'),
}

"""
    'alarmEqSn': ('alarm_device_sn', 'string'),
    'alarmCode': ('alarm_code', 'string'),
    'level': ('alarm_level', 'int'),
    'time': ('', 'string'),
"""
com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AlarmQuery(object):

    def alarm_query(self, *args, **kwargs):
        """  the alarm collection query-T216/T217

        Args：
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
        Kwargs:
            | type | int | 1:查询 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Query | current_alarm_list | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('type', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def alarm_query_result_check(self, _response, **kwargs):
        """  the alarm collection query-T217

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Alarm Query |
        | ${status} = |  Alarm Query Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def alarm_query_from_aws_get(self, *args, **kwargs):
        """  the alarm collection query from AWS,the message get-T216

        Args：
            | type |  int | 命令类型， 1:查询 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Query From AWS Get | type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_query_from_aws_get_check(self, **kwargs):
        """  the alarm collection query from AWS,the message get check-T216

        Kwargs:
            | type |  int | 命令类型， 1:查询 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Log From AWS Get Check | type=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_query_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_query_from_device_get(self, *args, **kwargs):
        """  the alarm collection result from device,the message get-T217

        Args：
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Query From Device Get | current_alarm_list |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_query_from_device_get_check(self, **kwargs):
        """  the alarm collection query from device,the message get check-T217

        Kwargs:
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Query From Device Get Check | current_alarm_list=@{EMPTY} |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_query_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_query_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T216/217

        Args for T216:
            | type |  int | 命令类型， 1:查询 |
        Args for T217：
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 216/217 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=217 | rx_time_window=300 |
        | ${status} | Alarm Query From Msg Get | current_alarm_list | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_query_from_device_get, cmd_type_s2c: self.alarm_query_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def alarm_query_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T216/217

        Kwargs for T216:
            | type |  int | 命令类型， 1:查询 |
        Kwargs for T217：
            | current_alarm_list | list | 告警列表 |
            | type |  int | 命令类型， 1:查询 |
            | result | int | 结果，0：成功，1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 216/217 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=217 | rx_time_window=300 | filter_mode=and |
        | ${status} | Alarm Query From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_query_from_device_get_check, cmd_type_s2c: self.alarm_query_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
