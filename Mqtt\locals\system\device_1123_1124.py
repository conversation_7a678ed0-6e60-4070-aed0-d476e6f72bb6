from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 客户物联网服务器地址查询设置

cmd_type_c2s = 1123

cmd_type_s2c = 1124

attr_map_c2s = {

    **attr_map_common_opt,

    'aGate_SN': ('device_SN', 'string'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,
}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDeviceSet(Base):

    def local_device_set(self, *args, **kwargs):
        """  local device set-T1123
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：设置失败 |
            | opt | int | 操作类型，1:设置 |
            | device_SN | string | aGate SN,<=32Bytes |
        Kwargs:
            | opt | int | 操作类型，1:设置 |
            | device_SN | string | aGate SN,<=32Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Device Set |   device_SN=12345678 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_device_set_result_check(self, _response, **kwargs):
        """  local device set result check-T1124

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：设置失败 |
            | opt | int | 操作类型，1:设置 |
            | device_SN | string | aGate SN,<=32Bytes |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Device Set |   device_SN=12345678 |
        | ${status} = | Local Device Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
