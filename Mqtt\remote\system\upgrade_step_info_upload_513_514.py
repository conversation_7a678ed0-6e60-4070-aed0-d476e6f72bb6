from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 设备分布升级下载完成信息上报

app_name = "device"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 514

cmd_type_c2s = 513

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    **attr_map_common_result,

    'fileName': ('file_name', 'string'),
    'order': ('', 'string'),
    'bakFileRenew': ('backup_firmware_update', 'int'),
    'stepType': ('step_type', 'int'),
    'errMsg': ('error_message', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class UpgradeStepInfoUpload(object):

    def upgrade_step_info_upload_get_from_aws_get(self, *args, **kwargs):
        """  upgrade step info upload get from AWS,the message get-T514

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Step Info Upload Get From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_step_info_upload_get_from_aws_get_check(self, **kwargs):
        """  upgrade step info upload get from AWS,the message get check-T514

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Step Info Upload Get From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_step_info_upload_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_step_info_upload_get_from_device_get(self, *args, **kwargs):
        """   upgrade step info upload get response from device,the message get-T513

        Args:
            | file_name | string | 升级的文件名称，<=64Bytes |
            | order | string | 下载工单号，<=32Bytes |
            | backup_firmware_update | int | 备份固件是否更新，0：更新成功，1：更新失败 |
            | step_type | int | 升级步骤，1：第一次下载发送，2：第二次升级完成发送 |
            | error_message | int | 错误码字符串 |
            | result | int | 0：升级成功；1：文件下载失败；2：url解析失败；3：设备升级失败(子CPU)；4：连接失败；5：解压失败；6：MD5校验失败；7：文件类型不匹配；8：升级超时(总)；9：升级未启动，内部通信异常；10：升级未启动，电网异常；11：升级未启动，SOC过低；14：数据库读取失败；15：版本读取失败；16：PE bin 不匹配；17：PEDC不兼容；18：PE INV不兼容；19：PE版本读取错误 20 通用错误（21以后NSC不使用，全部用错误字符串errMsg表示）21 bin 传输超时 22 块发送错误 23 有bin文件但是所有的bin 都不匹配，一个bin都不需要升级 24 没发发现任何升级文件 51 PE_DC bin相同 52 PE_INV bin相同 53 PE_FPGA bin相同 54 PE_DC bin 不匹配 55 PE_INV bin 不匹配 56 PE_FPGA bin 不匹配 57 PE_DC 版本比较错误 58 PE_INV 版本比较错误 59 PE_版本比较错误 60 PE_DC CRC错误 61 PE_INV CRC错误 62 PE_PPGA CRC错误 63 PE_DC 禁止错误 64 PE_INV 禁止错误 65 PE_PPGA 禁止错误 66 PE_DC bin打开 67 PE_INV bin打开错误 68 PE_PPGA bin打开错误 69 PE_DC 读版本错误 70 PE_INV 读版本错误 71 PE_FPGA 读版本错误 101 BMS_main bin相同 102 BMS_TH bin相同 103 BMS_BL bin相同 104 BMS_main 不匹配 105 BMS_TH bin不匹配 106 BMS_BL bin不匹配 107 BMS_main 版本比较错误 108 BMS_TH bin 版本比较错误 109 BMS_BL bin 版本比较错误 110 BMS_main crc 错误 111 BMS_TH bin  crc 错误 112 BMS_BL bin  crc错误 113 BMS_main 禁止错误 114 BMS_TH bin  禁止错误 115 BMS_BL bin  禁止错误 116 BMS_main 打开错误 117 BMS_TH   bin打开错误 118 BMS_BL   bin打开错误 119 BMS_main 读版本错误 120 BMS_TH   读版本错误 121 BMS_BL   读版本错误 151 SL bin相同 152 SL bin不匹配 153 SL 版本比较错误 154 SL 版本CRC错误 155 SL 禁止错误 156 SL bin打开错误 157 SL 读版本错误 201 bin文件错误 202 读取版本失败 203电表线程无响应 204电表发送失败 205电表发送文件超时 206电表版本对比失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Step Info Upload Get From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_step_info_upload_get_from_device_get_check(self, **kwargs):
        """  upgrade step info upload get response from device,the message get check-T513

        Kwargs:
            | file_name | string | 升级的文件名称，<=64Bytes |
            | order | string | 下载工单号，<=32Bytes |
            | backup_firmware_update | int | 备份固件是否更新，0：更新成功，1：更新失败 |
            | step_type | int | 升级步骤，1：第一次下载发送，2：第二次升级完成发送 |
            | error_message | int | 错误码字符串 |
            | result | int | 0：升级成功；1：文件下载失败；2：url解析失败；3：设备升级失败(子CPU)；4：连接失败；5：解压失败；6：MD5校验失败；7：文件类型不匹配；8：升级超时(总)；9：升级未启动，内部通信异常；10：升级未启动，电网异常；11：升级未启动，SOC过低；14：数据库读取失败；15：版本读取失败；16：PE bin 不匹配；17：PEDC不兼容；18：PE INV不兼容；19：PE版本读取错误 20 通用错误（21以后NSC不使用，全部用错误字符串errMsg表示）21 bin 传输超时 22 块发送错误 23 有bin文件但是所有的bin 都不匹配，一个bin都不需要升级 24 没发发现任何升级文件 51 PE_DC bin相同 52 PE_INV bin相同 53 PE_FPGA bin相同 54 PE_DC bin 不匹配 55 PE_INV bin 不匹配 56 PE_FPGA bin 不匹配 57 PE_DC 版本比较错误 58 PE_INV 版本比较错误 59 PE_版本比较错误 60 PE_DC CRC错误 61 PE_INV CRC错误 62 PE_PPGA CRC错误 63 PE_DC 禁止错误 64 PE_INV 禁止错误 65 PE_PPGA 禁止错误 66 PE_DC bin打开 67 PE_INV bin打开错误 68 PE_PPGA bin打开错误 69 PE_DC 读版本错误 70 PE_INV 读版本错误 71 PE_FPGA 读版本错误 101 BMS_main bin相同 102 BMS_TH bin相同 103 BMS_BL bin相同 104 BMS_main 不匹配 105 BMS_TH bin不匹配 106 BMS_BL bin不匹配 107 BMS_main 版本比较错误 108 BMS_TH bin 版本比较错误 109 BMS_BL bin 版本比较错误 110 BMS_main crc 错误 111 BMS_TH bin  crc 错误 112 BMS_BL bin  crc错误 113 BMS_main 禁止错误 114 BMS_TH bin  禁止错误 115 BMS_BL bin  禁止错误 116 BMS_main 打开错误 117 BMS_TH   bin打开错误 118 BMS_BL   bin打开错误 119 BMS_main 读版本错误 120 BMS_TH   读版本错误 121 BMS_BL   读版本错误 151 SL bin相同 152 SL bin不匹配 153 SL 版本比较错误 154 SL 版本CRC错误 155 SL 禁止错误 156 SL bin打开错误 157 SL 读版本错误 201 bin文件错误 202 读取版本失败 203电表线程无响应 204电表发送失败 205电表发送文件超时 206电表版本对比失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Step Info Upload Get From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_step_info_upload_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_step_info_upload_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T513/514

        Kwargs for T513：
            | file_name | string | 升级的文件名称，<=64Bytes |
            | order | string | 下载工单号，<=32Bytes |
            | backup_firmware_update | int | 备份固件是否更新，0：更新成功，1：更新失败 |
            | step_type | int | 升级步骤，1：第一次下载发送，2：第二次升级完成发送 |
            | error_message | int | 错误码字符串 |
            | result | int | 0：升级成功；1：文件下载失败；2：url解析失败；3：设备升级失败(子CPU)；4：连接失败；5：解压失败；6：MD5校验失败；7：文件类型不匹配；8：升级超时(总)；9：升级未启动，内部通信异常；10：升级未启动，电网异常；11：升级未启动，SOC过低；14：数据库读取失败；15：版本读取失败；16：PE bin 不匹配；17：PEDC不兼容；18：PE INV不兼容；19：PE版本读取错误 20 通用错误（21以后NSC不使用，全部用错误字符串errMsg表示）21 bin 传输超时 22 块发送错误 23 有bin文件但是所有的bin 都不匹配，一个bin都不需要升级 24 没发发现任何升级文件 51 PE_DC bin相同 52 PE_INV bin相同 53 PE_FPGA bin相同 54 PE_DC bin 不匹配 55 PE_INV bin 不匹配 56 PE_FPGA bin 不匹配 57 PE_DC 版本比较错误 58 PE_INV 版本比较错误 59 PE_版本比较错误 60 PE_DC CRC错误 61 PE_INV CRC错误 62 PE_PPGA CRC错误 63 PE_DC 禁止错误 64 PE_INV 禁止错误 65 PE_PPGA 禁止错误 66 PE_DC bin打开 67 PE_INV bin打开错误 68 PE_PPGA bin打开错误 69 PE_DC 读版本错误 70 PE_INV 读版本错误 71 PE_FPGA 读版本错误 101 BMS_main bin相同 102 BMS_TH bin相同 103 BMS_BL bin相同 104 BMS_main 不匹配 105 BMS_TH bin不匹配 106 BMS_BL bin不匹配 107 BMS_main 版本比较错误 108 BMS_TH bin 版本比较错误 109 BMS_BL bin 版本比较错误 110 BMS_main crc 错误 111 BMS_TH bin  crc 错误 112 BMS_BL bin  crc错误 113 BMS_main 禁止错误 114 BMS_TH bin  禁止错误 115 BMS_BL bin  禁止错误 116 BMS_main 打开错误 117 BMS_TH   bin打开错误 118 BMS_BL   bin打开错误 119 BMS_main 读版本错误 120 BMS_TH   读版本错误 121 BMS_BL   读版本错误 151 SL bin相同 152 SL bin不匹配 153 SL 版本比较错误 154 SL 版本CRC错误 155 SL 禁止错误 156 SL bin打开错误 157 SL 读版本错误 201 bin文件错误 202 读取版本失败 203电表线程无响应 204电表发送失败 205电表发送文件超时 206电表版本对比失败 |
        Kwargs for T514:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 514/513 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=513 | rx_time_window=300 |
        | ${status} | Upgrade Step Info Upload Get From Msg Get | file_name | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_step_info_upload_get_from_device_get, cmd_type_s2c: self.upgrade_step_info_upload_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def upgrade_step_info_upload_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T513/514

        Kwargs for T513：
            | file_name | string | 升级的文件名称，<=64Bytes |
            | order | string | 下载工单号，<=32Bytes |
            | backup_firmware_update | int | 备份固件是否更新，0：更新成功，1：更新失败 |
            | step_type | int | 升级步骤，1：第一次下载发送，2：第二次升级完成发送 |
            | error_message | int | 错误码字符串 |
            | result | int | 0：升级成功；1：文件下载失败；2：url解析失败；3：设备升级失败(子CPU)；4：连接失败；5：解压失败；6：MD5校验失败；7：文件类型不匹配；8：升级超时(总)；9：升级未启动，内部通信异常；10：升级未启动，电网异常；11：升级未启动，SOC过低；14：数据库读取失败；15：版本读取失败；16：PE bin 不匹配；17：PEDC不兼容；18：PE INV不兼容；19：PE版本读取错误 20 通用错误（21以后NSC不使用，全部用错误字符串errMsg表示）21 bin 传输超时 22 块发送错误 23 有bin文件但是所有的bin 都不匹配，一个bin都不需要升级 24 没发发现任何升级文件 51 PE_DC bin相同 52 PE_INV bin相同 53 PE_FPGA bin相同 54 PE_DC bin 不匹配 55 PE_INV bin 不匹配 56 PE_FPGA bin 不匹配 57 PE_DC 版本比较错误 58 PE_INV 版本比较错误 59 PE_版本比较错误 60 PE_DC CRC错误 61 PE_INV CRC错误 62 PE_PPGA CRC错误 63 PE_DC 禁止错误 64 PE_INV 禁止错误 65 PE_PPGA 禁止错误 66 PE_DC bin打开 67 PE_INV bin打开错误 68 PE_PPGA bin打开错误 69 PE_DC 读版本错误 70 PE_INV 读版本错误 71 PE_FPGA 读版本错误 101 BMS_main bin相同 102 BMS_TH bin相同 103 BMS_BL bin相同 104 BMS_main 不匹配 105 BMS_TH bin不匹配 106 BMS_BL bin不匹配 107 BMS_main 版本比较错误 108 BMS_TH bin 版本比较错误 109 BMS_BL bin 版本比较错误 110 BMS_main crc 错误 111 BMS_TH bin  crc 错误 112 BMS_BL bin  crc错误 113 BMS_main 禁止错误 114 BMS_TH bin  禁止错误 115 BMS_BL bin  禁止错误 116 BMS_main 打开错误 117 BMS_TH   bin打开错误 118 BMS_BL   bin打开错误 119 BMS_main 读版本错误 120 BMS_TH   读版本错误 121 BMS_BL   读版本错误 151 SL bin相同 152 SL bin不匹配 153 SL 版本比较错误 154 SL 版本CRC错误 155 SL 禁止错误 156 SL bin打开错误 157 SL 读版本错误 201 bin文件错误 202 读取版本失败 203电表线程无响应 204电表发送失败 205电表发送文件超时 206电表版本对比失败 |
        Kwargs for T514:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 514/513 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=514 | rx_time_window=300 | filter_mode=and |
        | ${status} | Upgrade Step Info Upload Get From Msg Get Check| result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_step_info_upload_get_from_device_get_check, cmd_type_s2c: self.upgrade_step_info_upload_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
