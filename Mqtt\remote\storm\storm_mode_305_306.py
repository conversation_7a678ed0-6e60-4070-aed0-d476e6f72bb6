from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 风暴模式

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 305

cmd_type_c2s = 306

attr_map_s2c = {

    'strom': ('storm_mode', 'int'),
    'startTime': ('start_time', 'string'),
    'endTime': ('end_time', 'string'),

}

attr_map_c2s = {

    **attr_map_common_result,

    'strom': ('storm_mode', 'int'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class StormMode(object):

    def storm_mode_set(self, *args, **kwargs):
        """  storm mode set-T305/306

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
        Kwargs:
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | start_time | string | 开始时间 |
            | end_time | string | 结束时间 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Storm Mode Set | storm_mode=1 |
        | ${status} = | Storm Mode Set | result | storm_mode | storm_mode=1 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('storm_mode', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def storm_mode_set_result_check(self, _response, **kwargs):
        """  storm mode set result check-T306

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Storm Mode Set | storm_mode=1 |
        | ${status} = | Storm Mode Set Result Check | ${response} | result=0  | strom=1 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def storm_mode_from_aws_get(self, *args, **kwargs):
        """  storm mode set from AWS,the message get-T305

        Args：
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | start_time | string | 开始时间 |
            | end_time | string | 结束时间 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Storm Mode From AWS Get | storm_mode |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def storm_mode_from_aws_get_check(self, **kwargs):
        """  storm mode set from AWS,the message get check-T305

        Kwargs:
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | start_time | string | 开始时间 |
            | end_time | string | 结束时间 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |   Storm Mode From AWS Get Check | storm_mode=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.storm_mode_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def storm_mode_from_device_get(self, *args, **kwargs):
        """   storm mode set response from device,the message get-T306

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Storm Mode From Device Get | result | storm_mode |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def storm_mode_from_device_get_check(self, **kwargs):
        """  storm mode set response from device,the message get check-T306

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Storm Mode From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.storm_mode_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def storm_mode_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T305/306

        Args for T305:
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | start_time | string | 开始时间 |
            | end_time | string | 结束时间 |
        Args for T306：
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 305/306 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=305 | rx_time_window=300 |
        | ${status} | Storm Mode From Msg Get | storm_mode | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.storm_mode_from_device_get, cmd_type_s2c: self.storm_mode_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def storm_mode_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T305/306

        Kwargs for T305:
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
            | start_time | string | 开始时间 |
            | end_time | string | 结束时间 |
        Kwargs for TT306：
            | result | int | 结果，0:成功，1:失败 |
            | storm_mode | int | 预警模式，0：取消预警，1：风暴预警，2：停电预警  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 305/306 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=306 | rx_time_window=300 | filter_mode=and |
        | ${status} | Storm Mode From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.storm_mode_from_device_get_check, cmd_type_s2c: self.storm_mode_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
