from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 能量流图

cmd_type_c2s = 1301

cmd_type_s2c = 1302

attr_map_c2s = {

    **attr_map_common_opt,

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'mode': ('', 'int'),
    'name': ('', 'string'),
    'electricity_type': ('', 'int'),

    'run_status': ('', 'int'),
    'slaver_stat': ('slave_status', 'int'),
    'elecnet_state': ('grid_status', 'int'),

    'fhpSn': ('device_sn', 'list'),
    'fhpPower': ('fhp_power', "list"),
    'infi_status': ('fhp_status', 'list'),

    'p_uti': ('grid_power', 'float'),
    'p_sun': ('solar_power', 'float'),
    'p_gen': ('generator_power', 'float'),
    'p_fhp': ('fhp_generate_power', 'float'),
    'p_load': ('user_power', 'float'),

    'kwh_uti_in': ('kwh_grid_in', 'float'),
    'kwh_uti_out': ('kwh_grid_out', 'float'),
    'kwh_sun': ('kwh_solar', 'float'),
    'kwh_gen': ('kwh_generator', 'float'),
    'kwh_fhp_di': ('kwh_fhp_discharging', 'float'),
    'kwh_fhp_chg': ('kwh_fhp_charging', 'float'),
    'kwh_load': ('kwh_user', 'float'),

    'soc': ('', 'float'),
    'fhpSoc': ('fhp_soc', "list"),

    't_amb': ('env_temperature', 'float'),
    'main_sw': ('main_switch', 'list'),
    'pro_load': ('programmed_payload_status', 'list'),
    'do': ('dry_contact_relay_output_status', 'list'),
    'di': ('dry_contact_relay_input_status', 'list'),


    'ent': ('current_event_status', 'int'),  # new
    'sharp': ('', 'list'),  # new
    'peak': ('', 'list'),  # new
    'flat': ('', 'list'),  # new
    'valley': ('', 'list'),  # new

    'genStat': ('generator_status', 'int'),
    'kwhSolarLoad': ('kwh_solar_load', 'int'),
    'kwhGridLoad': ('kwh_grid_load', 'int'),
    'kwhFhpLoad': ('kwh_fhp_load', 'int'),
    'kwhGenLoad': ('kwh_generator_load', 'int'),

    'batOutGrid': ('kwh_apower_feed_in_dayily', 'float'),
    'soOutGrid': ('kwh_pv_feed_in_dayily', 'float'),
    'soChBat': ('kwh_pv_charged_dayily', 'float'),
    'gridChBat': ('kwh_grid_charged_dayily', 'float'),
    'genChBat': ('kwh_generator_charged_dayily', 'float'),
    'sinHTemp': ('highest_sys_single_unit_temp', 'float'),
    'sinLTemp': ('lowest_sys_single_unit_temp', 'float'),

    'batSoh': ('bat_soh', 'list'),  # new
    'batCyc': ('bat_cycle_number', 'list'),  # new
    'ch': ('total_charging', 'list'),  # new
    'disch': ('total_dischargging', 'list'),  # new

    'v2lModeEnable': ('v2l_mode_enable', 'int'),
    'v2lRunState': ('v2l_run_state', 'int'),

    'mpptAllPower': ('total_power_mppt', 'float'),  # optional
    'mpptAactPower': ('active_power_mppt', 'list'),  # optional
    'mpptSta': ('mppt_status', 'list'),  # optional

    'proximalSolarWh': ('kwh_near_end_solar_daily', 'float'),  # optional
    'remoteSolar1Wh': ('kwh_remote_solar1_daily', 'float'),  # optional
    'remoteSolar2Wh': ('kwh_remote_solar2_daily', 'float'),  # optional
    'remoteSolar3Wh': ('kwh_remote_solar3_daily', 'float'),  # optional

    'meterkitPvWh': ('kwh_meter_kit_pv_daily', 'float'),  # optional
    'secondaryPvWh': ('kwh_second_pv_daily', 'float'),  # optional
    'mpptWh': ('kwh_mppt', 'float'),  # optional

    'apbox20Pv': ('apbox20_pv', 'list'),  # optional
    'apbox20PvIndex': ('apbox20_pv_index', 'list'),  # optional
    'apbox20PvStat': ('apbox20_pv_relay_status', 'list'),  # optional
    'apbox20PvPower': ('apbox20_pv_power', 'list'),  # optional
    'apbox20PvWh': ('kwh_apbox20_pv_daily', 'list'),  # optional
    'mPanPv1Power': ('main_pan_pv1_power', 'float'),  # optional
    'mPanPv1Wh': ('kwh_main_pan_pv1_power', 'float'),  # optional
    'mPanPv2Power': ('main_pan_pv2_power', 'float'),  # optional
    'mPanPv2Wh': ('kwh_main_pan_pv2_power', 'float'),  # optional


}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalFlowChart(Base):

    def local_energy_info_get(self, *args, **kwargs):
        """  energy info get set-T1301
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败 |
            | opt | int | 操作类型，0：查询 |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |
            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_power | float list | FHP电池有功功率，单位：kw |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | env_temperature | float | 环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | programmed_payload_status | int list | 3个编程负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | current_event_status | int | 当前告警状态，0：正常，1：告警 |
            | sharp | float list | 尖用电量,单位：kwh |
            | peak | float list | 峰峰用电量,单位：kwh |
            | flat | float list | 平用电量,单位：kwh |
            | valley | float list | 谷用电量,单位：kwh |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int |发电机供载电量，单位：kwh |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | bat_soh | int list | 电池SOH，单位：0.1% |
            | bat_cycle_number | int list | 电池循环次数 |
            | total_charging | int list | 总充电量，单位：wh |
            | total_dischargging | int list | 总放电量，单位：wh |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Energy Info Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_energy_info_get_result_check(self, _response, **kwargs):
        """  energy info get set result check-T1302

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败 |
            | opt | int | 操作类型，0：查询 |
            | mode |  int | EMS工作模式 0:初始状态, 2：自发自用，3：TOU（平衡），4：TOU（经济），5：仅备电（并网状态），6：风暴等待模式，7：电池备电中（离网），8：发电机备电中（离网），9：VPP调度，10:紧急停止，11：安全模式（故障中），12：电池维护，13：调试，16：升级状态，17：停机维护，18：禁止进入电网，20：BB&NEM，21：BB&CSS，22：BB&CGS+，23：远程关机，24：预留，25：aPower手动关机 26：主动离网，27：V2L，28：newhome |  |
            | name | string | 参数表名称，自发自用或TOU |
            | electricity_type | int | 电费类型，1：分时电价，2：阶梯电价，3：固定电价，4：分时&阶梯电价，5：BB套餐，6：peak demand |
            | run_status | int | EMS运行状态 0：待机，1：充电，2：放电，3：故障 |
            | slave_status | int | EMS slave运行状态 0：初始化，1：参数获取，2：监听，3：待机，4：市电并网，5：离网，6：离网油机运行，7：故障 |
            | device_sn | list |
            | grid_status | int | 市电状态,0：正常，1：故障 |
            | device_sn | string list | SN数组 |
            | fhp_power | float list | FHP电池有功功率，单位：kw |
            | fhp_status | int list | 子设备状态，0：正常，1：告警，2：故障，3：离线，4：关机 |
            | grid_power | float | 市电功率，单位：kw，供电为正，馈电为负 |
            | solar_power | float | 太阳能功率，单位：kw，供电为正，馈电为负 |
            | generator_power | float | 发电机发电功率，单位：kw |
            | fhp_generate_power | float | FHP发电功率，单位：kw，放电为正，充电为负 |
            | user_power | float | 用户负载功率，单位：kw |
            | kwh_grid_in | float | 市电口输入电量，单位：kwh  |
            | kwh_grid_out | float | 市电口馈入电网电量，单位：kwh  |
            | kwh_solar | float | 太阳能供电电量，单位：kwh  |
            | kwh_generator | float | 发电机供电电量，单位：kwh  |
            | kwh_fhp_discharging | float | FHP放电电量，单位：kwh  |
            | kwh_fhp_charging | float | FHP充电电量，单位：kwh  |
            | kwh_user | float | 用户负载用电量，单位：kwh  |
            | soc | float | 电池SOC，单位：% |
            | fhp_soc | list | FHP电池SOC，单位：% |
            | env_temperature | float | 环境温度，单位：摄氏度 |
            | main_switch | int list | 主开关状态,[a1,a2,a3],a1:备电主开关，a2：发电机主开关，a3：光伏主开关 | 0: 开关端口，1：开关闭合 |
            | programmed_payload_status | int list | 3个编程负载状态 | 0：断开，1：闭合 |
            | dry_contact_relay_output_status | int list | 4个干节点继电器输出状态 | 0：断开，1：闭合 |
            | dry_contact_relay_input_status | int list | 4个输入干节点状态 | 0：断开，1：闭合 |
            | current_event_status | int | 当前告警状态，0：正常，1：告警 |
            | sharp | float list | 尖用电量,单位：kwh |
            | peak | float list | 峰峰用电量,单位：kwh |
            | flat | float list | 平用电量,单位：kwh |
            | valley | float list | 谷用电量,单位：kwh |
            | generator_status | int | 0:不使能，1：停机，2：启动，3：运行，4：退出，5：故障，6：演习中 |
            | kwh_solar_load | int | 太阳能供载电量，单位：wh |
            | kwh_grid_load | int | 电网供载电量，单位：kwh |
            | kwh_fhp_load | int | FHP供载电量，单位：kwh |
            | kwh_generator_load | int |发电机供载电量，单位：kwh |
            | kwh_apower_feed_in_dayily | float | 电池馈网电量日统计，单位：kwh |
            | kwh_pv_feed_in_dayily | float | 光伏馈网电量日统计，单位：kwh |
            | kwh_pv_charged_dayily | float | 光伏充电电量日统计，单位：kwh |
            | kwh_grid_charged_dayily | float | 电网充电电量日统计，单位：kwh |
            | kwh_generator_charged_dayily | float | 油机充电电量日统计，单位：kwh |
            | highest_sys_single_unit_temp | float | 系统最高单体文档，单位：0.1摄氏度 |
            | lowest_sys_single_unit_temp | float |  系统最低单体文档，单位：0.1摄氏度 |
            | bat_soh | int list | 电池SOH，单位：0.1% |
            | bat_cycle_number | int list | 电池循环次数 |
            | total_charging | int list | 总充电量，单位：wh |
            | total_dischargging | int list | 总放电量，单位：wh |
            | v2l_mode_enable | int | 紧急电源模 式，0：关闭，1：打开 |
            | v2l_run_state | int | V2L运行状态，0：不使能，1：停机，2：启动，3：运行，4：退出，5：故障 |
            | total_power_mppt | float | MPPT光伏总功率，单位：kw |
            | active_power_mppt | float list | MPPT光伏有功功率，单位：kw |
            | mppt_status | int list | MPPT工作状态，0：初始，1：升级，2：待机，3：启机，4：运行，5：故障
            | kwh_near_end_solar_daily | float | 近端光伏日电量，单位：kwh |
            | kwh_remote_solar1_daily | float | 远端光伏1日电量，单位：kwh |
            | kwh_remote_solar2_daily | float | 远端光伏2日电量，单位：kwh |
            | kwh_remote_solar3_daily | float | 远端光伏日电3量，单位：kwh |
            | kwh_meter_kit_pv_daily | float | 市电侧Meterkit日电量，单位：kwh |
            | kwh_second_pv_daily | float | 市电侧NEM+光伏日电量，单位：kwh |
            | kwh_mppt | float | 所有MPPT日电量，单位：kwh |
            | apbox20_pv | json list | aPbox 2.0 光伏数据 |
            | apbox20_pv_index | int list | aPbox 2.0 光伏下标 |
            | apbox20_pv_relay_status | int list | aPbox 2.0 光伏继电器状态 |
            | apbox20_pv_power | float list | aPbox 2.0 光伏功率，单位：kw |
            | kwh_apbox20_pv_daily | float list | aPbox 2.0 光伏日电量，单位：kwh |
            | main_pan_pv1_power | float | 主配电盘光伏1功率，单位：kw |
            | kwh_main_pan_pv1_power | float | 主配电盘光伏1日电量，单位：kwh |
            | main_pan_pv2_power | float | 主配电盘光伏2功率，单位：kw |
            | kwh_main_pan_pv2_power | float | 主配电盘光伏2日电量，单位：kwh |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Energy Info Get |
        | ${status} = | Energy Info Get  Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
