from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备功率查询上报

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 237

cmd_type_s2c = 203

attr_map_s2c = {

    **attr_map_common_opt,
    'refreshData': ('refresh_data', 'int'),
}

attr_map_c2s = {

    'pGridL1': ('power_grid_L1', 'int'),
    'pGridL2': ('power_grid_L2', 'int'),

    'pSplitGridL1': ('power_split_grid_L1', 'int'),
    'pSplitGridL2': ('power_split_grid_L2', 'int'),
    'pMainGridL1': ('power_main_grid_L1', 'int'),
    'pMainGridL2': ('power_main_grid_L2', 'int'),
    'pBranchGridL1': ('power_branch_grid_L1', 'int'),
    'pBranchGridL2': ('power_branch_grid_L2', 'int'),
    'pFhp': ('power_fhp', 'int'),
    'pLoad': ('power_load', 'int'),
    'pGen': ('power_generator', 'int'),

    'pAgatePv': ('power_agate_pv', 'int'),
    'pAgateSplitPv': ('power_agate_split_pv', 'int'),
    'pRemoteSolar1': ('power_remote_pv1', 'int'),
    'pRemoteSolar2': ('power_remote_pv2', 'int'),
    'pSecondSolar': ('power_second_pv', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class PowerDataReport(object):

    def device_power_data_report(self, *args, **kwargs):
        """  the device power data report-T203-7/T237

        Args：
            | power_grid_L1 | int | 市电功率L1，单位：w  |
            | power_grid_L2 | int | 市电功率L2，单位：w  |
            | power_split_grid_L1 |  int | 子配电盘功率L1，单位：w  |
            | power_split_grid_L2 |  int | 子配电盘功率L2，单位：w  |
            | power_main_grid_L1 |  int | 主配电盘功率L1，单位：w  |
            | power_main_grid_L2 |  int | 主配电盘功率L2，单位：w  |
            | power_branch_grid_L1 |  int | 部分分路功率L1，单位：w  |
            | power_branch_grid_L2 |  int | 部分分路功率L2，单位：w  |
            | power_fhp |  int | FHP电池功率，单位：w  |
            | power_load |  int | 负载功率，单位：w  |
            | power_generator |  int | 发电机功率，单位：w  |
            | power_agate_pv |  int | 近端光伏功率，单位：w  |
            | power_agate_split_pv |  int | 近端扩展光伏功率，单位：w  |
            | power_remote_pv1 |  int | 远端光伏功率L1，单位：w  |
            | power_remote_pv2 |  int | 远端光伏功率L2，单位：w  |
            | power_second_pv |  int | 第二辅助电表功率，单位：w  |
        Kwargs:
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refreshData | int | 固定为7 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Power Data Report | power_grid_L1 | power_grid_L2 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")
        kwargs.setdefault('opt', 2)
        kwargs.setdefault('refresh_data', 7)
        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def device_power_data_from_aws_get(self, *args, **kwargs):
        """  the device power data from AWS,the message get-T203-7

        Args：
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refreshData | int | 固定为7 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Power Data From AWS Get | opt | refresh_data |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_power_data_from_aws_get_check(self, **kwargs):
        """  the device power data from AWS,the message get check-T203-7

        Kwargs:
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refreshData | int | 固定为7 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Power Data From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_power_data_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_power_data_from_device_get(self, *args, **kwargs):
        """  the device power data from device,the message get-T237

        Args：
            | power_grid_L1 | int | 市电功率L1，单位：w  |
            | power_grid_L2 | int | 市电功率L2，单位：w  |
            | power_split_grid_L1 |  int | 子配电盘功率L1，单位：w  |
            | power_split_grid_L2 |  int | 子配电盘功率L2，单位：w  |
            | power_main_grid_L1 |  int | 主配电盘功率L1，单位：w  |
            | power_main_grid_L2 |  int | 主配电盘功率L2，单位：w  |
            | power_branch_grid_L1 |  int | 部分分路功率L1，单位：w  |
            | power_branch_grid_L2 |  int | 部分分路功率L2，单位：w  |
            | power_fhp |  int | FHP电池功率，单位：w  |
            | power_load |  int | 负载功率，单位：w  |
            | power_generator |  int | 发电机功率，单位：w  |
            | power_agate_pv |  int | 近端光伏功率，单位：w  |
            | power_agate_split_pv |  int | 近端扩展光伏功率，单位：w  |
            | power_remote_pv1 |  int | 远端光伏功率L1，单位：w  |
            | power_remote_pv2 |  int | 远端光伏功率L2，单位：w  |
            | power_second_pv |  int | 第二辅助电表功率，单位：w  |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Power Data From Device Get | reason |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_power_data_from_device_get_check(self, **kwargs):
        """  the device power data from device,the message get check-T237

        Kwargs:
            | power_grid_L1 | int | 市电功率L1，单位：w  |
            | power_grid_L2 | int | 市电功率L2，单位：w  |
            | power_split_grid_L1 |  int | 子配电盘功率L1，单位：w  |
            | power_split_grid_L2 |  int | 子配电盘功率L2，单位：w  |
            | power_main_grid_L1 |  int | 主配电盘功率L1，单位：w  |
            | power_main_grid_L2 |  int | 主配电盘功率L2，单位：w  |
            | power_branch_grid_L1 |  int | 部分分路功率L1，单位：w  |
            | power_branch_grid_L2 |  int | 部分分路功率L2，单位：w  |
            | power_fhp |  int | FHP电池功率，单位：w  |
            | power_load |  int | 负载功率，单位：w  |
            | power_generator |  int | 发电机功率，单位：w  |
            | power_agate_pv |  int | 近端光伏功率，单位：w  |
            | power_agate_split_pv |  int | 近端扩展光伏功率，单位：w  |
            | power_remote_pv1 |  int | 远端光伏功率L1，单位：w  |
            | power_remote_pv2 |  int | 远端光伏功率L2，单位：w  |
            | power_second_pv |  int | 第二辅助电表功率，单位：w  |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Power Data From Device Get Check | reason=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_power_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_power_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T203-7/237

        Args for T203-7:
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refreshData | int | 固定为7 |
        Args for T237：
            | power_grid_L1 | int | 市电功率L1，单位：w  |
            | power_grid_L2 | int | 市电功率L2，单位：w  |
            | power_split_grid_L1 |  int | 子配电盘功率L1，单位：w  |
            | power_split_grid_L2 |  int | 子配电盘功率L2，单位：w  |
            | power_main_grid_L1 |  int | 主配电盘功率L1，单位：w  |
            | power_main_grid_L2 |  int | 主配电盘功率L2，单位：w  |
            | power_branch_grid_L1 |  int | 部分分路功率L1，单位：w  |
            | power_branch_grid_L2 |  int | 部分分路功率L2，单位：w  |
            | power_fhp |  int | FHP电池功率，单位：w  |
            | power_load |  int | 负载功率，单位：w  |
            | power_generator |  int | 发电机功率，单位：w  |
            | power_agate_pv |  int | 近端光伏功率，单位：w  |
            | power_agate_split_pv |  int | 近端扩展光伏功率，单位：w  |
            | power_remote_pv1 |  int | 远端光伏功率L1，单位：w  |
            | power_remote_pv2 |  int | 远端光伏功率L2，单位：w  |
            | power_second_pv |  int | 第二辅助电表功率，单位：w  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 203/204 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=237 | rx_time_window=300 |
        | ${status} | Device Power Data From Msg Get | power_grid_L1 | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_power_data_from_device_get, cmd_type_s2c: self.device_power_data_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_power_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T203-7/237

        Kwargs for T203-7:
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refreshData | int | 固定为7 |
        Kwargs for T237：
            | power_grid_L1 | int | 市电功率L1，单位：w  |
            | power_grid_L2 | int | 市电功率L2，单位：w  |
            | power_split_grid_L1 |  int | 子配电盘功率L1，单位：w  |
            | power_split_grid_L2 |  int | 子配电盘功率L2，单位：w  |
            | power_main_grid_L1 |  int | 主配电盘功率L1，单位：w  |
            | power_main_grid_L2 |  int | 主配电盘功率L2，单位：w  |
            | power_branch_grid_L1 |  int | 部分分路功率L1，单位：w  |
            | power_branch_grid_L2 |  int | 部分分路功率L2，单位：w  |
            | power_fhp |  int | FHP电池功率，单位：w  |
            | power_load |  int | 负载功率，单位：w  |
            | power_generator |  int | 发电机功率，单位：w  |
            | power_agate_pv |  int | 近端光伏功率，单位：w  |
            | power_agate_split_pv |  int | 近端扩展光伏功率，单位：w  |
            | power_remote_pv1 |  int | 远端光伏功率L1，单位：w  |
            | power_remote_pv2 |  int | 远端光伏功率L2，单位：w  |
            | power_second_pv |  int | 第二辅助电表功率，单位：w  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 203/204 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=237 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Power Data From Msg Get Check | power_grid_L1=100 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_power_data_from_device_get_check, cmd_type_s2c: self.device_power_data_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
