from appium import webdriver
from appium.options.common import AppiumOptions
from .mixin import (SwipeMixin, SysMixin, NetworkMixin, AppMixin, LocationMixin, ActionMixin, ContextMixin, ActivityMixin, ImageCompare)
from .location import Location
from .utils import Utils
from ..util import os_command, logger
from ..constant import (PLATFORM, LANG, APP_NAME)


class Driver(SwipeMixin, SysMixin, NetworkMixin, AppMixin, LocationMixin, ActionMixin, ContextMixin, ActivityMixin, ImageCompare, Location, Utils):

    def __init__(self):

        super().__init__()

    def connect(self, platform_name='Android', platform_version="12", device_name="23f5a34c", screenshot_path='./', webdriver_server_address='http://localhost:4723/wd/hub', implicitly_wait_time=5, **kwargs):
        """ Connect Mobile Phone

        Kwargs:
            | platform_name |  string | Android(默认)/iOS,手机平台 |
            | platform_version | string | 12(默认),手机操作系统版本 |
            | device_name | string | 手机设备名 |
            | debug | bool | true/false(默认),库是否开调试功能 |
            | screenshot_path | string | './'（默认),手机截图文件的保存路径， |
            | app_package | string | app的包名，默认为None, 安卓平台为com.franklinwh.franklinwh2 |
            | app_activity | string | app的activity,默认为None,安卓平台为com.franklinwh.franklinwh2.MainActivity |
            | udid | string | 手机的udid，默认为None |
            | full_reset | bool | true/false(默认)，app是否需要全复位,相当于卸载用户数据 |
            | session_override | bool | true/false(默认),会话是否需要覆盖 |
            | unicode_keyboard | bool | true(默认)/false，unicode键盘输入法是否需要使能 |
            | new_command_timeout | int | 60000(单位：毫秒，默认)，命令超时时间 |
            | no_sign | bool | true(默认)/false,是否需要重新签名 |
            | noReset |  bool | true/false(默认),是否复位app |
            | app |  string | app名(默认为None)，带文件路径，用于安装app到手机上 |`
            | webdriver_server_address | string | appium server地址，默认为http://localhost:4723/wd/hub，本机地址 |
            | implicitly_wait_time | float | 全局隐式等待时间，默认为5秒 |
            | lang | string | English(默认）/Chinese |
        Return:
            | object | 连接对象 |
        Examples:
            | ${app} | Connect | platform_name=Android | platform_version=12 | device_name=23f5a34c | screenshot_path=D:/ |
        """
        logger.info(f'the user parameters:platform_name:{platform_name},'
                    f'platform_version:{platform_version},'
                    f'device_name:{device_name},'
                    f'screenshot_path:{screenshot_path},'
                    f'webdriver_server_address:{webdriver_server_address},'
                    f'implicitly_wait_time:{implicitly_wait_time},'
                    f'kwargs:{kwargs}'
                    )

        self.install_mode = kwargs.pop('install_mode', False)
        self.app_package = kwargs.pop('app_package', APP_NAME)
        self.app_activity = kwargs.pop('app_activity', None)
        self.udid = kwargs.pop('udid', None)

        self.platform_name = platform_name

        PLATFORM.write(self.platform_name)

        self.lang = kwargs.pop('lang', 'English')

        LANG.write(self.lang)

        self.model = kwargs.pop('model', None)

        if self.model is None:

            try:
                self.model = self._get_phone_model()

            except Exception as e:

                logger.info(f'Detect the exception:{e}')

                self.model = 'Samsung'

        logger.info(f'the current connect mobile phone is:{self.model}')

        full_reset = kwargs.pop('full_reset', False)
        no_reset = kwargs.pop('noReset', True)

        session_override = kwargs.pop('session_override', False)

        self.implicitly_wait_time = implicitly_wait_time

        # support Chinese
        unicode_keyboard = kwargs.pop('unicode_keyboard', True)

        # restore system keyboard input method
        reset_keyboard = kwargs.pop('reset_keyboard', True)

        new_command_timeout = kwargs.pop('new_command_timeout', 60000)

        # no resignature...
        no_sign = kwargs.pop('no_sign', True)

        # options...
        app = kwargs.pop('app', None)
        webview = kwargs.pop('autoWebview', False)
        webview = kwargs.pop('ensureWebviewsHavePages', True)

        self.screenshot_path = screenshot_path

        self.debug = kwargs.pop('debug', False)

        self.package_name = self.app_package

        # Uiautomator2

        desired_caps = {'platformName': platform_name,
                        'platformVersion': platform_version,
                        'deviceName': device_name,
                        'noReset': no_reset,
                        'appWaitDuration': 1000,
                        'autoAcceptAlerts': True,
                        'fullReset': full_reset,
                        'sessionOverride': session_override,
                        'unicodeKeyboard': unicode_keyboard,
                        'resetKeyboard': reset_keyboard,
                        'newCommandTimeout': new_command_timeout,
                        'noSign': no_sign,
                        'dontStopAppOnReset': False,
                        'skipDeviceInitialization': True,
                        }

        if self.platform_name == 'Android':

            desired_caps.update(automationName='UIAutomator2')

        elif self.platform_name == 'iOS':

            self.bundleID = kwargs.pop('app_bundleID', APP_NAME)

            desired_caps.update(bundleID=self.bundleID, udid=self.udid, automationName="XCUITest")

        if not self.install_mode:

            # will not launch APP in debug mode

            if not self.debug:

                if self.platform_name == 'Android':

                    if self.app_package is not None and self.app_activity is not None:

                        logger.info(f'OK,launch the APP!')

                        desired_caps.update(appPackage=self.app_package, appActivity=self.app_activity)

        logger.info(f'the desired_caps:{desired_caps}')

        options = AppiumOptions().load_capabilities(desired_caps)

        self._check_app_running_state_and_close()

        self.driver = webdriver.Remote(webdriver_server_address, options=options, strict_ssl=False)

        installed_status = self._get_app_installed_status(platform_name)

        if installed_status:

            logger.info(f'OK, already installed the package:{self.app_package}')

        else:

            logger.info(f'The package:{self.app_package} is not installed!')

            if app is not None:

                if not self.debug:

                    self.app_install(app, uninstall=uninstall)

        orient_status = self.get_orientation_mode()

        lock_status = self.is_locked()

        self.size = self.driver.get_window_size()

        network_conn = None

        if platform_name in "Android":

            network_conn = self.get_network_connection()

        device_time = self.get_sys_time()

        logger.info(f"the current mobile phone status:\n"
                    f"the screen lock_status:{lock_status}\n"
                    f"the screen orientation status:{orient_status}\n"
                    f"the screen size:{self.size}\n"
                    f"the network_connection:{network_conn}\n"
                    f"the device time:{device_time}"
                    )

        self.driver.implicitly_wait(implicitly_wait_time)

        return self

    def _get_app_installed_status(self, platform_name):

        if self.app_package is None:

            self.app_package = APP_NAME

        return self.driver.is_app_installed(self.app_package)

    def _get_phone_model(self):

        if self.platform_name == "Android":

            cmd = "adb devices -l"

            res, err = os_command(cmd)

            if err:

                raise ValueError("Fail to get phone model!")

            s_string = 'model:'

            s_index = res.index(s_string) + len(s_string)

            e_index = res.index(' device:')

            _model = res[s_index:e_index]

            logger.info(f'The current mobile phone is:{_model}')

            model = None

            if 'Mi_' in _model:

                model = 'Xiaomi'

            elif 'SM_' in _model:

                model = 'Samsung'

            # LG,Huawei,Oppo,etc.

        else:
            model = 'Apple'

        return model

    def get_window_size(self):
        """ get screen window size

        Kwargs:
            | None  |
        Return:
            | dict | 字典带着keys:widith,height |
        Examples:
            | ${result} | get_window_size |
        """
        return self.driver.get_window_size()

    @property
    def window_width(self):

        return self.size['width']

    @property
    def window_height(self):

        return self.size['height']

    def disconnect(self, **kwargs):
        """ Disconnect Mobile Phone

        Kwargs:
            | uninstall | False(缺省），为True：退出前，先卸载已安装的app |
        Return:
            | None |
        Examples:
            | Disconnect |
        """
        self.app_quit(**kwargs)

    def _check_app_running_state_and_close(self):

        if self.platform_name == "Android":

            cmd = f'adb shell "ps | grep {self.app_package}"'

            res, _ = os_command(cmd)

            if res:

                logger.info(f'OK, found the running APP,killing it...')

                cmd = f"""adb shell am force-stop {self.app_package}"""

                os_command(cmd)

                logger.info(f'OK,closed the running APP!')
