from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# PE系统参数

cmd_type_c2s = 1703

cmd_type_s2c = 1704

attr_map_c2s = {

    **attr_map_common_opt,

    'id': ('', 'int'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'batVol': ('bat_voltage', 'float'),
    'gridVol1': ('grid_voltage1', 'float'),
    'gridVol2': ('grid_voltage2', 'float'),
    'inverterVolt1': ('inverter_voltage1', 'float'),
    'inverterVolt2': ('inverter_voltage2', 'float'),
    'positiveBusVolt': ('bus_pos_voltage', 'float'),
    'negativeBusVolt': ('bus_neg_voltage', 'float'),
    'middleBusVolt': ('bus_mid_voltage', 'float'),
    'gridFreq': ('grid_freq', 'float'),
    'loadCurr1': ('load_current1', 'float'),
    'loadCurr2': ('load_current1', 'float'),
    'runMode': ('run_mode', 'int'),
    'inverterCurr1': ('inverter_current1', 'float'),
    'inverterCurr2': ('inverter_current2', 'float'),
    'buckboostCurr': ('buckboost_current1', 'float'),
    'inverterStatus': ('inverter_status', 'int'),
    'DCDCStatus': ('dcdc_status', 'int'),
    'gridLineVol': ('grid_line_voltage', 'float'),
    'invLineVol': ('inverter_line_voltage', 'float'),
    'samBatVol': ('sample_bat_votage', 'float'),

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalPeParameter(Base):

    def local_pe_parameter_set(self, *args, **kwargs):
        """  local PE parameter set-T1703
        Args：
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败 |
            | bat_voltage | float | 电池电压，单位：V |
            | grid_voltage1 | float |  电网电压L1，单位：V |
            | grid_voltage2 | float |  电网电压L2，单位：V |
            | inverter_voltage1 | float | 逆变电压L1，单位：V |
            | inverter_voltage2 | float | 逆变电压L2，单位：V |
            | bus_pos_voltage | float | 正母线电压，单位：V |
            | bus_neg_voltage | float | 负母线电压，单位：V |
            | bus_mid_voltage | float | 中间母线电压，单位：V |
            | grid_frequency | float | 频率制式，单位：Hz |
            | load_current1 | float | 负载电流L1，单位：A |
            | load_current1 | float | 负载电流L2，单位：A |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inverter_current1 | float | 逆变电流L1，单位：A |
            | inverter_current2 | float | 逆变电流L2，单位：A |
            | buckboost_current1 | float | buckboost电流，单位：A |
            | inverter_status | int | INV运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dcdc_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） SUN/SUP（apower20使用以下状态）0：初始化状态 1：待机状态 2：电池缓冲中间母线状态 3：母线缓冲中间母线状态 4：离网工作状态 5：并网工作状态 6：故障关机状态 7：调试状态 |
            | grid_line_voltage | float | PE电网线电压，单位：V |
            | inverter_line_voltage | float | PE逆变线电压，单位：V |
            | sample_bat_votage | float | 采样电池电压，单位：0.1V |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | bat_voltage | float | 电池电压，单位：V |
            | grid_voltage1 | float |  电网电压L1，单位：V |
            | grid_voltage2 | float |  电网电压L2，单位：V |
            | inverter_voltage1 | float | 逆变电压L1，单位：V |
            | inverter_voltage2 | float | 逆变电压L2，单位：V |
            | bus_pos_voltage | float | 正母线电压，单位：V |
            | bus_neg_voltage | float | 负母线电压，单位：V |
            | bus_mid_voltage | float | 中间母线电压，单位：V |
            | grid_freq | float | 市电频率，单位：Hz |
            | load_current1 | float | 负载电流L1，单位：A |
            | load_current1 | float | 负载电流L2，单位：A |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inverter_current1 | float | 逆变电流L1，单位：A |
            | inverter_current2 | float | 逆变电流L2，单位：A |
            | buckboost_current1 | float | buckboost电流，单位：A |
            | inverter_status | int | INV运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dcdc_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） SUN/SUP（apower20使用以下状态）0：初始化状态 1：待机状态 2：电池缓冲中间母线状态 3：母线缓冲中间母线状态 4：离网工作状态 5：并网工作状态 6：故障关机状态 7：调试状态 |
            | grid_line_voltage | float | PE电网线电压，单位：V |
            | inverter_line_voltage | float | PE逆变线电压，单位：V |
            | sample_bat_votage | float | 采样电池电压，单位：0.1V |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local PE Parameter Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_pe_parameter_set_result_check(self, _response, **kwargs):
        """  local PE parameter set result check-T1704

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败 |
            | bat_voltage | float | 电池电压，单位：V |
            | grid_voltage1 | float |  电网电压L1，单位：V |
            | grid_voltage2 | float |  电网电压L2，单位：V |
            | inverter_voltage1 | float | 逆变电压L1，单位：V |
            | inverter_voltage2 | float | 逆变电压L2，单位：V |
            | bus_pos_voltage | float | 正母线电压，单位：V |
            | bus_neg_voltage | float | 负母线电压，单位：V |
            | bus_mid_voltage | float | 中间母线电压，单位：V |
            | grid_freq | float | 市电频率，单位：Hz |
            | load_current1 | float | 负载电流L1，单位：A |
            | load_current1 | float | 负载电流L2，单位：A |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inverter_current1 | float | 逆变电流L1，单位：A |
            | inverter_current2 | float | 逆变电流L2，单位：A |
            | buckboost_current1 | float | buckboost电流，单位：A |
            | inverter_status | int | INV运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dcdc_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） SUN/SUP（apower20使用以下状态）0：初始化状态 1：待机状态 2：电池缓冲中间母线状态 3：母线缓冲中间母线状态 4：离网工作状态 5：并网工作状态 6：故障关机状态 7：调试状态 |
            | grid_line_voltage | float | PE电网线电压，单位：V |
            | inverter_line_voltage | float | PE逆变线电压，单位：V |
            | sample_bat_votage | float | 采样电池电压，单位：0.1V |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local PE Parameter Set |
        | ${status} = | Local PE Parameter Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
