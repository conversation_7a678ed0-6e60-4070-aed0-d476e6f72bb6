import os
import pandas as pd
import cv2
from time import time, sleep
import os
from robot.api import logger
from ..pages.locator import SHEET, FILE
from ..constant import (PLATFORM, LANG)
from pathlib import Path

FILE_PATH = Path(__file__).parent.parent

TEMPLATE_PIC_PATH = FILE_PATH / f'pages/template'

TEMP_PIC_PATH = f'{TEMPLATE_PIC_PATH}/temp'


class Utils(object):

    def get_pictured_based_value(self, element, template_pic=None, check_type='bool', return_value_if_true='true', return_value_if_false='false'):

        file_name = 'temp.png'

        try:

            target_pic = self.save_screenshot(file_path=TEMP_PIC_PATH, file_name=file_name, element=element)

            logger.debug(f'the target picture:{target_pic}')

            template_pic = f'{TEMPLATE_PIC_PATH}{os.sep}{template_pic}'

            logger.debug(f'the template picture:{template_pic}')

            result = self.compare_picture(target_pic, template_pic, template=cv2.TM_SQDIFF_NORMED, threshold=0.01)

        finally:

            if os.path.isfile(target_pic):

                os.remove(target_pic)

        if check_type == 'bool':

            return return_value_if_true if result else return_value_if_false

    def set_seekbar(self, ele, expected_value=None, direction='scroll_up', key='content-desc', scroll_ratio=0.2):

        logger.info(f'the user parameters:set_seekbar:ele:{ele},expected_value:{expected_value},direction:{direction}'
                    f'key:{key},scroll_ratio:{scroll_ratio}')

        self.get_element_coordinate_and_action(ele, location='center', action='click')

        value = self.get_element_attribute(ele, key=key)

        logger.info(f'the current value:{value}')

        while value != str(expected_value):

            self.get_element_coordinate_and_action(ele, location='center', action=direction, scroll_ratio=scroll_ratio)

            value = self.get_element_attribute(ele, key=key)

            sleep(0.1)

            logger.info(f'the current value:{value}')

        logger.info(f'OK,found the matched value!')

    def set_value_in_seekbar(self, element, expected_value=None, direction='scroll_up', mapping_dict=None, target_attribute=None, _dict=None):

        logger.info(f'the user parameters in set_value_in_seekbar:element:{element},expected_value:{expected_value},'
                    f'direction:{direction},mapping_dict:{mapping_dict},target_attribute:{target_attribute},_dict:{_dict}')

        if _dict is not None:

            if target_attribute in _dict:

                if expected_value is None:

                    _expected_value = _dict.pop(target_attribute)

                else:

                    _expected_value = expected_value

                if mapping_dict:

                    _expected_value = mapping_dict[_expected_value]

                element.click()

                value = "locator_general_seekbar"

                ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

                self.set_seekbar(ele, expected_value=_expected_value, direction=direction)

                self._system_confirm_locator()

    def set_value_in_drop_down_list(self, target_element, target_value=None, mapping_dict=None, _dict=None, target_element_click=True, scroll=True, lower_case=True, dict_check=False, confirm_dialog=False, confirm=True, confirm_timeout=None, confirm_error_ignore=True, custom=False, method='xpath', custom_attribe='Customize', edit_text="android.widget.EditText"):
        """ general method for drop down list WEB element
        """
        logger.info(f'the user parameters in set_value_in_drop_down_list:target_element:{target_element},target_value:{target_value}'
                    f'mapping_dict:{mapping_dict},_dict:{_dict},scroll:{scroll},lower_case:{lower_case},method:{method},confirm_error_ignore:{confirm_error_ignore}'
                    f'custom:{custom}')
        if _dict is not None:

            _service_value = _dict.pop(target_value, None)

        else:
            _service_value = target_value

        custom_flag = False

        value = None

        if _service_value is not None:

            if lower_case:

                target_value = _service_value.lower()

            if target_element_click:

                target_element.click()

            if mapping_dict is not None:

                if _service_value in mapping_dict:

                    value = mapping_dict[_service_value]

                elif custom:

                    value = custom_attribe

                    custom_flag = True
            else:
                value = target_value

            locator = (method, value)

            self._get_locator_and_find_element(locator, ele_find_func=self.find_element, click=True)

            if custom_flag:

                locator = ('cls_name', edit_text)

                self.find_element(*locator, first_click=True, clear=True, send_keys=target_value)

            if confirm_dialog:

                if confirm:

                    value = 'Confirm'

                else:

                    value = 'Cancel'

                locator = ('acc_id', value)

                if confirm_timeout is not None:

                    self._get_locator_and_find_element(locator, ele_find_func=self.find_element, click=True, throw_exception=not confirm_error_ignore, timeout=confirm_timeout)

                else:

                    self._get_locator_and_find_element(locator, ele_find_func=self.find_element, throw_exception=not confirm_error_ignore, click=True)

        else:

            if dict_check:

                raise ValueError(f'Sorry, could not find the {target_value} in {_dict}!')

    def _config_select_element_status(self, locator, locator_func=None, element_type='button', checked_type='checked', expected_value="enabled", auto_scroll=True):
        """ general method for WEB element status like button with checked/enabled/selected/displayed
        """
        logger.info(f'the user parameters in _config_select_element_status:locator:{locator},element_type:{element_type}'
                    f',checked_type:{checked_type},expected_value:{expected_value},auto_scroll:{auto_scroll}')

        if not isinstance(locator, tuple):

            locator = locator_func(locator)

        ele = self.get_locator_and_operate_element(locator, locator_fun=locator_func, ele_find_func=self.find_element, auto_scroll=auto_scroll)

        logger.info(f'ok, got the ele:{ele}')

        if checked_type == 'checked':
            # checkable support multi-choice
            # focused
            # clickable
            # scrollable

            cur_status = ele.get_attribute("checked")

            logger.info(f"the current status:{cur_status}")

        logger.info(f"the current enable status: {ele.is_enabled()}")  # grey or not,usable,for input/select elements

        logger.info(f"the current select status: {ele.is_selected()}")   # multiple option

        logger.info(f"the current display status: {ele.is_displayed()}")  # show up or hidden,html only,visibility=hidden or display=none

        exp_status = "true" if expected_value.lower() == 'enabled' else "false"

        if cur_status != exp_status:

            if checked_type == 'checked':

                ele.click()

            ele = self.find_element(*locator)

            cur_new_status = ele.get_attribute("checked")

            logger.info(f"the current new status:{cur_status}")

            if cur_new_status != exp_status:

                raise ValueError(f"Fail to config to the expected value:{exp_status}!")

            logger.info(f"OK,successfully config the locator:{locator} to the expected value:{expected_value}!")

        else:
            logger.info(f"OK,no need to config the locator:{locator} because it's already the expected value:{expected_value}!")

    def _set_value_in_drop_pop_element(self, locator, locator_fun=None, value=None, confirm=True, auto_scroll=True):

        self.get_locator_and_operate_element(locator, locator_fun=locator_fun, auto_scroll=auto_scroll, click=True)

        _locator = "locator_general_edittext"

        self.get_locator_and_operate_element(_locator, locator_fun=locator_fun, first_click=True, clear=True, send_keys=value)

        if confirm:

            self._system_confirm_locator()

        else:

            self._system_cancel_locator()

    def _get_attribute_value_with_locator_general(self, attr_list=None, locator_prefix=None, locator_fun=None, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=None):
        """ general attribute get method for grid profile parameters,etc.
        """
        for i in attr_list:

            _value = self._get_attribute_value_by_locator(f'{locator_prefix}_{i}', locator_fun=locator_fun, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name=attr_name, auto_scroll=True)

            value = _value.split(split_character)[split_index]

            ret_dict.update({i: value})

        logger.info(f'the ret:{ret_dict}')

    def _go_to_sub_page(self, value=None, method='acc_id', locator=None, timeout=None, will_do_loading_detect=True, throw_exception=True):

        logger.info(f"the user parameters:value:{value},method:{method},"
                    f"locator:{locator}")

        locator_comm = (method, value) if locator is None else locator

        logger.info(f"the locator:{locator_comm}")

        if timeout is not None:

            self.find_element(*locator_comm, click=True, timeout=timeout, will_do_loading_detect=will_do_loading_detect, throw_exception=throw_exception)

        else:

            self.find_element(*locator_comm, click=True, will_do_loading_detect=will_do_loading_detect, throw_exception=throw_exception)

    def _go_to_page(self, locator=None, timeout=None, click=True):

        logger.debug(f"the user parameters:locator:{locator},"
                     f"timeout:{timeout},click:{click}")

        if timeout is not None:

            ele = self.find_element(*locator, click=click, timeout=timeout)

        else:

            ele = self.find_element(*locator, click=click)

        return ele

    def _detect_element_by_scroll(self, locator_comm, max_number=5, click=True):

        logger.info(f"the user parameters:locator_comm:{locator_comm},"
                    f"max_number:{max_number},click:{click}"
                    )

        found_flag = False

        number = 0

        while (not found_flag) and (number < max_number):

            ele = self.find_element(*locator_comm, throw_exception=False, timeout=0.2)

            logger.debug(f"the ele:{ele}")

            if ele is None:

                self.swipe_up()

                number += 1

            else:
                logger.info(f"OK, got the element:{ele}!")

                found_flag = True

                if click:

                    ele.click()

                return ele

        if not found_flag:

            raise ValueError(f"Fail to find the element for {locator_comm}")

    def _general_locator_operate(self, locator=None, **kwargs):

        _locator = get_locator(locator)

        self._go_to_sub_page(locator=_locator, **kwargs)

    def _general_backward_upper_page(self, locator="locator_general_up_1"):

        self._general_locator_operate(locator=locator)

    def _general_backward_upper_page_type2(self, locator="locator_general_up_2"):

        self._general_locator_operate(locator=locator)

    def _general_configure_page(self, locator="locator_general_configure", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_next_locator(self, locator="locator_general_next", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_save_locator(self, locator="locator_general_save", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_detect_locator(self, locator="locator_general_detect", timeout=1):

        self._general_locator_operate(locator=locator)

    def _system_connect_locator(self, locator="locator_general_connect", timeout=1):

        self._general_locator_operate(locator=locator)

    def _system_edit_locator(self, locator="locator_general_edit"):

        self._general_locator_operate(locator=locator)

    def _system_confirm_locator(self, locator="locator_general_confirm", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_cancel_locator(self, locator="locator_general_cancel", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_complete_locator(self, locator="locator_general_complete", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def _system_select_locator(self, locator="locator_general_select", **kwargs):

        self._general_locator_operate(locator=locator, **kwargs)

    def confirm_cancel_diag_handling(self, value=True):

        if value:

            self._system_confirm_locator()

        else:
            self._system_cancel_locator()

    def _system_wait_to_show(self, locator="locator_general_complete", timeout=None, default_max_timeout=120, click=False):
        """ general waiting method to expect to see the specified WEB element to show up
        """
        _locator = get_locator(locator)

        found_flag = False

        ele = None

        start = time()

        if timeout is not None:

            if timeout > default_max_timeout:

                default_max_timeout = timeout

        while not found_flag:

            if timeout is not None:

                ele = self.find_element(*_locator, throw_exception=False, timeout=timeout)

            else:

                ele = self.find_element(*_locator, throw_exception=False)

            if ele is not None:

                found_flag = True

                break

            end = time()

            delta = end - start

            if delta > default_max_timeout:

                raise ValueError(f'FAIL to show the element with locator:{locator} in {default_max_timeout} seconds!')

        if click:

            ele.click()

        return ele

    def _handle_user_kwargs(self, kwargs=None, par_list=None):
        """ general user kwargs parameters to filter out
        """
        logger.info(f'the user parameters:kwargs:{kwargs},par_list:{par_list}')

        par_list_result = []

        for i in par_list:

            if i not in kwargs:

                par_list_result.append(i)

        result_list = [i is not None for i in par_list_result]

        ret = any(result_list)

        logger.debug(f'ret:{ret}')

        return ret

    def _check_user_kwargs(self, kwargs=None, par_list=None):
        """ use user general user kwargs parameters to screen
        """
        logger.info(f'the user parameters:kwargs:{kwargs},par_list:{par_list}')

        for i in par_list:

            if i in kwargs:

                return True


class Read_excel_file(object):

    def __init__(self, file=None, sheet_name=None, key='Locator'):
        self.lang = None
        self.platform = None
        self.key = key
        self.file = file
        self.sheet_name = sheet_name

        self.obj = pd.read_excel(self.file, sheet_name=self.sheet_name)

    def get_locator(self, element=None, default_value=None):

        if not self.platform:

            self.platform = PLATFORM.getvalue()

        if not self.lang:

            self.lang = LANG.getvalue()

        logger.info(f'the user parameters in get_locator:element:{element},default_value:{default_value},platform:{self.platform},lang:{self.lang}')

        c_df = self.obj.set_index(self.key)

        m = c_df.loc[element]

        ret_value = [m.iloc[0], m.iloc[1]]

        if default_value is not None:

            ret_value[1] = default_value

        if self.platform == 'Android':

            if self.lang == 'Chinese':

                if not pd.isna(m.iloc[2]):

                    ret_value[1] = m.iloc[2]
        else:

            ret_value = [m.iloc[3], m.iloc[4]]

            if pd.isna(m.iloc[3]):

                ret_value[0] = m.iloc[0]

            if pd.isna(m.iloc[4]):

                ret_value[1] = m.iloc[1]

            if self.lang == 'Chinese':

                ret_value[1] = m.iloc[5]

                if pd.isna(m.iloc[5]):

                    if pd.isna(m.iloc[2]):

                        ret_value[1] = m.iloc[1]

                    else:
                        ret_value[1] = m.iloc[2]

        if '\\n' in ret_value[1]:

            ret_value[1] = ret_value[1].replace('\\n', '\n')

        logger.debug(f'OK,return the locator:{ret_value}')

        return tuple(ret_value)


def get_locator(locator, instance=None, default_value=None, default_search=True):
    """ get locator from excel files
    """
    logger.info(f'the user parameters:locator:{locator},instance:{instance} '
                f'default_value:{default_value},default_search:{default_search}'
                )
    if instance is None:

        instance_fallback = Read_excel_file(file=FILE, sheet_name=SHEET)

        return instance_fallback.get_locator(locator, default_value=default_value)

    else:

        try:

            return instance.get_locator(locator, default_value=default_value)

        except Exception as e:

            instance_fallback = Read_excel_file(file=FILE, sheet_name=SHEET)

            return instance_fallback.get_locator(locator, default_value=default_value)
