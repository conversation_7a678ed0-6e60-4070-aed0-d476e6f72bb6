from time import sleep
from robot.api import logger

URL_ABS = 'https://production.franklinwh.com'


upgrade_result_dict ={
                0: "待升级",
                1: "升级中",
                2: "升级成功",
                3: "升级失败",
                4: "升级超时",
                5: "升级终止",
                6: "升级初始化",
    }
class Manage(object):

    def get_system_version_info(self, *args):

        uri = 'common/sysSoftware'

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', headers=self.headers)

    def get_system_current_menu(self):

        uri = 'api-user/role/listCurrentResource'

        data = {'userId': "1"}

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{uri}', data=data,  headers=self.headers)


    def get_system_site_list(self):

        uri = 'manage/selectCitesList'

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', headers=self.headers)

    def get_company_list(self):

        uri = 'api-user/company/listCompany'

        logger.info(f'the current headers:{self.headers}')

        data = {'name': '',
                'userId': 1,
                'type': '',
                'countryId': '',
                'pageSize': 50,
                'pageNo': 1,
                }

        return self.get(url=f'{uri}', data=data, headers=self.headers, )

    def get_company_device_number(self, start_time=2025):

        uri = 'api-statistics/installersData/listCompanyDeviceNum'

        data = {'countryId': 2,
                'provinceId': '',
                'cityId': '',
                'type': 3,
                'financierId': '',
                'relationStatus': '',
                'startTime': int(start_time),
                'pageSize': 10,
                'pageNo': 1,
                }

        return self.get(url=f'{uri}', data=data, headers=self.headers)

    def get_system_user_list(self):

        uri = 'api-user/user/listUser'

        logger.info(f'the current headers:{self.headers}')

        #return self.get(url_abs=URL_ABS, url=f'{uri}', headers=self.headers)

        return self.get(url=f'{uri}', headers=self.headers)

    def get_system_role_list(self):

        uri = 'api-user/role/listRole'

        data = {'name': '',
                'userId': 1,
                'type': '',
                'countryId': '',
                'pageSize': 10,
                'pageNo': 1,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{uri}', data=data, headers=self.headers)

    def get_financier_and_distributor(self):

        uri = 'manage/getFinancierAndDistributor'

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}',  headers=self.headers)

    def get_msg(self):

        uri = 'terminal'

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', headers=self.headers)

    def get_firmware_info(self,firmware_name=None):

        uri = 'manage/selectIotFirmuareList'

        ret = dict()

        data = {'name': firmware_name,
                'status': '',
                'releaseEnv': '',
                'remark': '',
                'pageSize': 10,
                'pageNo': 1,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def generate_upgrade_order(self, device=None, firmware_name=None):

        uri = 'manage/insertFirmwareOrder'

        firmware_dict = dict()

        if isinstance(device, list):

            _device = device
            
        else:
            
            _device = [device]

        json = {'gatewayIds': _device,
                'upgradeTime': '',
                'upgradeType': 0,
                'userId': 1,
                }
        
        firmware_info = self.get_firmware_info(firmware_name=firmware_name)

        tmp_dict = firmware_info['result'][0]

        firmware_dict.update(firmwareId=tmp_dict['id'], type=tmp_dict['type'], releaseEnv=tmp_dict['releaseEnv'])

        json.update(**firmware_dict)

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.upgrade}/{uri}', json=json, headers=self.headers)

    def get_upgrade_order_info(self, device=None):

        uri = 'manage/selectIotFirmwareOrderList'

        json = {'optType': 1,
                'gatewayId': device,
                "name": '',
                "result": '',
                "status": '',
                'startTime': '',
                'endTime': '',
                'pageSize': "10",
                'pageNum': "1",
                }

        logger.info(f'the current headers:{self.headers}')

        order_info = self.post(url=f'{self.gateway}/{uri}', data=json, data_format='form', headers=self.headers)

        tmp_dict = order_info['result'][0]

        internal_order, user_order = tmp_dict['id'], tmp_dict['orderNo']

        logger.console(f'the upgrade order:{user_order}')

        return internal_order, user_order

    def get_upgrade_oder_status(self, order_id=None, until_completed=True, query_period=7):

        def _query_status():

            info = self.post(url=f'{self.gateway}/{uri}', data=json, data_format='form', headers=self.headers)

            return info['result'][0]

        uri = 'manage/selectIotFirmwareOrderDetailList'

        query_period = float(query_period)

        json = {'optType': 1,
                'orderId': order_id,
                "serials": '',
                "result": '',
                'pageSize': "10",
                'pageNum': "1",
                }

        logger.info(f'the current headers:{self.headers}')

        _dict = _query_status()
        
        if _dict['orderId'] == int(order_id):
            
            if not until_completed:

                return _dict
            
            else:

                while _dict['result'] in [0, 1, 6]:

                    logger.console(f"The current upgrade status:{upgrade_result_dict[_dict['result']]}")
                    
                    sleep(query_period)

                    _dict = _query_status()

                logger.console(f"The current upgrade status:{upgrade_result_dict[_dict['result']]}")

                logger.console(f"OK,the upgrade progress is completed!")

        return _dict
