from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict

# aPower2.0信息获取

cmd_type_c2s = 1307

cmd_type_s2c = 1308

attr_map_c2s = {

    'update': ('', 'int'),

}

attr_map_s2c = {

    'ratedPwr': ('rated_power', 'int'),
    'ratedPwrBuf': ('rated_power_list', 'list'),
    'rateBatCap': ('rated_battery_list', 'list'),

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalAPower20Info(Base):

    def local_apower20_info_get(self, *args, **kwargs):
        """  local apower2.0 info get-T1307
        Args：
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Kwargs:
            | update | int | 1 :召唤apower2.0相关信息 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Apower20 Info Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_location_set_result_check(self, _response, **kwargs):
        """  local apower2.0 info get result check-T1308

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Apower20 Info Get |
        | ${status} = | Local Apower20 Info Get Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
