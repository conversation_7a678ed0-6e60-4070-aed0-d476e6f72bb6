from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 网络通道检测结果查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 363

cmd_type_c2s = 364

attr_map_s2c = {

    **attr_map_common_opt,

    'networkChannel': ('network_channel', 'int'),

}

attr_map_c2s = {

    **attr_map_s2c,

    **attr_map_common,

    'testResult': ('test_result', 'int'),
    'internetSpeed': ('Internet_Speed', 'string'),
    'internetPacketLossRate': ('Internet_Packet_Loss_Rate', 'string'),
    'signalStrength': ('signal_strength', 'int'),
    'signalValue': ('signal_value', 'int'),
    'signalDetails': ('signal_details', 'string'),
    'LANPacketLossRate': ('LAN_Packet_Loss_Rate', 'string'),
    'connectStatus': ('connect_status', 'int'),
}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class NetworkDetectionResultGet(object):

    def network_channel_detection_result_get(self, *args, **kwargs):
        """  network channel detection result get-T363/364

        Args：
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Channel Detection Result Get |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def network_channel_detection_result_get_check(self, _response, **kwargs):
        """ network channel detection result check-T364

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Network Channel Detection Result Get | opt=0 |
        | ${status} = | Network Channel Detection Result Get Check | ${response} | current_network=2  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def network_channel_detection_result_get_from_aws_get(self, *args, **kwargs):
        """  network channel detection result get from AWS,the message get-T363

        Args：
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Channel Detection Result Get From AWS Set | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def network_channel_detection_result_get_from_aws_get_check(self, **kwargs):
        """  network channel detection result get from AWS,the message get check-T363

        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Channel Detection Result Get From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_channel_detection_result_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_channel_detection_result_get_from_device_get(self, *args, **kwargs):
        """   network channel detection result get response from device,the message get-T364

        Args：
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Channel Detection Result Get From Device Get | network_channel |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def network_channel_detection_result_get_from_device_get_check(self, **kwargs):
        """  network channel detection result get response from device,the message get check-T364

        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Network Channel Detection Result Get From Device Get Check | current_network=2 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_channel_detection_result_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_channel_detection_result_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T363/364

        Args for T363:
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Args for T364：
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 363/364 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=363 | rx_time_window=300 |
        | ${status} | Network Channel Detection Result Get From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_channel_detection_result_get_from_device_get, cmd_type_s2c: self.network_channel_detection_result_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def network_channel_detection_result_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T363/364

        Kwargs for T363:
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Kwargs for T364:
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 363/364 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=364 | rx_time_window=300 | filter_mode=and |
        | ${status} | Network Channel Detection Result Get From Msg Get Check | network_channel=2 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_channel_detection_result_get_from_device_get_check, cmd_type_s2c: self.network_channel_detection_result_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
