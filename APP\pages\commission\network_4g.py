from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class G4(Base):

    def go_to_commission_network_setting_4g_page(self):
        """ Go to system->commission->network setting->4G page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Network Setting 4G Page |
        """
        locator_name = "locator_network_setting_4g"

        eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        ele = eles[-1]

        ele.click()

    def exit_commission_network_setting_4g_page(self):
        """ exit system->commission->network setting->4G page and go back to the system->commission->network setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Network Setting 4G Page |
        """
        try:
            self._general_backward_upper_page()

        except Exception as e:

            logger.info(f'Got the exception:{e}')

            locator = "locator_network_setting_exit"

            self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def get_commission_network_4g_parameters(self, *args, **kwargs):
        """ get commission->Network->4G parameters

        args:
            | signal | int or string | 当4G模块未工作时为'' |
            | mac | string |  | MAC address |
            | DNS | string | IPv4 address |
            | gateway | string | IPv4 address |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission network 4G Parameters  |
            | ${result}	| Get Commission network 4G Parameters |  mac	| DNS | gateway | signal |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        self._get_4g_parameters(ret_dict)

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_4g_parameters(self, ret_dict):

        locator_name = "locator_network_setting_4g_general"

        values = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=False, ctrl_empty_strip=False, ctrl_lower_case=False)

        _values = values.split('\n')

        if 'NIC' in _values[1]:

            signal = ''

        else:

            signal = _values[-4]

        ret_dict.update({"DNS": _values[-1], "MAC": _values[-3], "signal": signal})
