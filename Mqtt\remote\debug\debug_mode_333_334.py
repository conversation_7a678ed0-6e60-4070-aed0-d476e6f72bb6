from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 调试模式

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 333

cmd_type_c2s = 334

attr_map_s2c = {

    **attr_map_common_opt,

    'enable': ('debug_mode_enabled', 'int'),
    'powerOn': ('power_on', 'int'),
    'powerOff': ('power_off', 'int'),
    'power': ('', 'int'),
    'dcPvMaxPwr': ('pv_max_power', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DebugMode(object):

    def debug_mode_set(self, *args, **kwargs):
        """  Debug mode set-T333/334

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs:
            | opt | int | 操作类型，0:查询(默认)，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Mode Set | opt=0 |
        | ${status} = | Debug Mode Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def debug_mode_set_result_check(self, _response, **kwargs):
        """  Debug mode set result check-T334

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Debug Mode Set | opt=1 | debug_mode_enabled=1 |
        | ${status} = | Debug Mode Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def debug_mode_set_from_aws_get(self, *args, **kwargs):
        """  Debug mode set from AWS,the message get-T333

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Mode Set From AWS Get | debug_mode_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def debug_mode_set_from_aws_get_check(self, **kwargs):
        """  Debug mode set from AWS,the message get check-T333

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Debug Mode Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.debug_mode_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def debug_mode_set_from_device_get(self, *args, **kwargs):
        """   Debug mode set response from device,the message get-T334

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Mode Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def debug_mode_set_from_device_get_check(self, **kwargs):
        """  Debug mode set response from device,the message get check-T334

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Debug Mode Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.debug_mode_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def debug_mode_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T333/334

        Args for T333:
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Args for T334：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 333/334 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=333 | rx_time_window=300 |
        | ${status} | Debug Mode Set From Msg Get | debug_mode_enabled | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.debug_mode_set_from_device_get, cmd_type_s2c: self.debug_mode_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def debug_mode_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T333/334

        Kwargs for T333:
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs for T334：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | debug_mode_enabled | int | 调试模式，0：去使能，1：使能 |
            | power_on | int | 开机，1：生效 |
            | power_off | int | 关机，1：生效 |
            | power | int | 充放电功率，单位：w |
            | pv_max_power | int | apower光伏最大输出功率，单位：w，-1：不限制 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 333/334 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=334 | rx_time_window=300 | filter_mode=and |
        | ${status} | Debug Mode Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.debug_mode_set_from_device_get_check, cmd_type_s2c: self.debug_mode_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
