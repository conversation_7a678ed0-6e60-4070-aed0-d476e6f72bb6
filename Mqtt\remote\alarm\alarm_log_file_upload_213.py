from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 告警日志文件上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 213

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'notice': ('', 'int'),
    'alarmLogFile': ('alarm_log_file', 'string'),
    'sign': ('', 'string'),
    'alarmLogDate': ('alarm_log_date', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AlarmLogFileUpload(object):

    def alarm_log_file_from_device_get(self, *args, **kwargs):
        """  alarm log file from device,the message get-T213

        Args：
            | notice | int | 1：通知更新告警日志文件数据  |
            | alarm_log_file |  string | <=128Bytes,告警日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | alarm_log_date | string | yyyy-mm-dd,告警日志文件文件生成日期 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Log File From Device Get | alarm_log_file | sign | alarm_log_date |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_log_file_from_device_get_check(self, **kwargs):
        """  alarm log file from device,the message get check-T213

        Kwargs:
            | notice | int | 1：通知更新告警日志文件数据  |
            | alarm_log_file |  string | <=128Bytes,告警日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | alarm_log_date | string | yyyy-mm-dd,告警日志文件文件生成日期 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Log File From Device Get Check | notice=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_log_file_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_log_file_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T213

        Args for T213：
            | notice | int | 1：通知更新告警日志文件数据  |
            | alarm_log_file |  string | <=128Bytes,告警日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | alarm_log_date | string | yyyy-mm-dd,告警日志文件文件生成日期 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 213 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=213 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Vendor Data From Msg Get | notic | msg=${packets} |
        | ${status} | Real Time Vendor Data From Msg Get | notice | sign | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=213 | rx_time_window=300 |
        | ${status} | Alarm Log File From Msg Get | notice | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_log_file_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def alarm_log_file_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T213

        Kwargs for T213：
            | notice | int | 1：通知更新告警日志文件数据  |
            | alarm_log_file |  string | <=128Bytes,告警日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | alarm_log_date | string | yyyy-mm-dd,告警日志文件文件生成日期 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 213 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=213 | rx_time_window=300 | filter_mode=and |
        | ${status} | Alarm Log File From Msg Get Check | notice=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_log_file_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
