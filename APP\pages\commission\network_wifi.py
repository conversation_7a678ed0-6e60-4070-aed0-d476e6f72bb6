from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Wifi(Base):

    def go_to_commission_network_setting_wifi_page(self, timeout=None, current_status=False):
        """ Go to system->commission->network setting->Wifi page

        Kwargs:
            | timeout | 单位：秒，等待时间 |
        Return:
            | None |
        Examples:
            | Go To Commission Network Setting Wifi Page |
        """

        if current_status:

            value = "locator_network_setting_wifi2"    # when current network connect status is wifi the xpath that enter the wifi page is different
        else:
            value = "locator_network_setting_wifi"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_network_setting_wifi_page(self):
        """ exit system->commission->network setting->Wifi page and go back to the system->commission->network setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Network Setting Wifi Page |
        """
        current_wifi = self.get_commission_network_wifi_parameters('wifi_enabled')

        logger.info(f'the current wifi status:{current_wifi}')

        if not current_wifi:

            value = "locator_network_setting_wifi_exit_2"

        else:

            value = "locator_network_setting_wifi_exit_1"

        locator = get_locator(value)

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def config_commission_network_wifi_parameters(self, **kwargs):
        """ config commission->Network->Wifi parameters

        Kwargs:
            | wifi_enabled | bool |
            | auto_connect | bool |
            | manual_connect | bool |
            | IP | string | IPv4 address |
            | DNS | string | IPv4 address |
            | gateway | string | IPv4 address |
        Return:
            | None |
        Examples:
            | Config Commission network Wifi Parameters |  wifi_enabled=false |
            | Config Commission network Wifi Parameters |  auto_connect=true |
            | Config Commission network Wifi Parameters |  manual_connect=true | IP=**************	| DNS=******* | gateway=*************
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        self._set_network_parameters(**kwargs)

    def _set_network_parameters(self, **kwargs):

        logger.info(f"the user parameters:kwargs:{kwargs}")

        wifi_enabled = kwargs.pop('wifi_enabled', None)

        if wifi_enabled is not None:

            locator_name = "locator_network_setting_ethernet_id"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            if value == 'false' and wifi_enabled == 'true':

                # enable WIFI firstly

                x_ration = 941 / 1080
                y_ration = 555 / 2120

                self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)

                self._system_confirm_locator()

                logger.info(f'try to find the confirm...............')

                sleep(40)

                logger.info(f'check wifi page again...............')

                # go into wifi page again

                if kwargs:

                    self.go_to_commission_network_setting_wifi_page(timeout=300)

            elif value == 'true' and wifi_enabled == 'false':

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        ethernet_enabled = kwargs.pop('ethernet_enabled', None)

        if ethernet_enabled is not None:

            locator_name = "locator_network_setting_ethernet_id"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            if value == 'false' and ethernet_enabled == 'true':

                # enable Eth firstly

                x_ration = 941 / 1080
                y_ration = 600 / 2120

                self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)

                self._system_confirm_locator()

                logger.info(f'check eth page again...............')

                # go into wifi page again

                if kwargs:

                    self.go_to_commission_network_setting_ethernet_page(timeout=300)

            elif value == 'true' and ethernet_enabled == 'false':

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        auto_connect = kwargs.pop('auto_connect', None)

        if auto_connect is not None:

            locator_name = "locator_network_setting_ethernet_auto_connect"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            logger.info(f'the auto_connect:{value}')

            if value != auto_connect:

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        manual_connect = kwargs.pop('manual_connect', None)

        if manual_connect is not None:

            locator_name = "locator_network_setting_ethernet_manual_connect"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            logger.info(f'the manual_connect:{value}')

            if value != manual_connect:

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self.swipe_up()

        IP = kwargs.pop('IP', None)

        DNS = kwargs.pop('DNS', None)

        gateway = kwargs.pop('gateway', None)

        network_list = [IP, DNS, gateway]

        result_list = [i is not None for i in network_list]

        if any(result_list):

            locator_name = "locator_network_setting_ethernet_general"

            eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

            if IP is not None:
                eles[0].click()
                eles[0].clear()
                eles[0].send_keys(IP)

            if DNS is not None:
                eles[1].click()
                eles[1].clear()
                eles[1].send_keys(DNS)

            if gateway is not None:
                eles[2].click()
                eles[2].clear()
                eles[2].send_keys(gateway)

        self._system_confirm_locator()

        self._system_confirm_locator()

    def get_commission_network_wifi_parameters(self, *args, **kwargs):
        """ get commission->Network->Wifi parameters

        args:
            | wifi_enabled | bool |
            | current_connection |  string | user's wifi AP name or '--'(未连接) |
            | auto_connect | bool |
            | manual_connect | bool |
            | mac | string |  | MAC address |
            | IP | string | IPv4 address |
            | DNS | string | IPv4 address |
            | gateway | string | IPv4 address |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission network Wifi Parameters  |
            | ${result}	| Get Commission network Wifi Parameters | wifi_enabled |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        self._get_wifi_parameters(ret_dict)

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_wifi_parameters(self, ret_dict):

        locator_name = "locator_network_setting_ethernet_id"

        value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

        ret_dict.update({"wifi_enabled": value})

        if value == 'true':

            signal = ''

            ret_dict.update({"signal": signal})   # currently NA for Wifi Signal

            locator_name = "locator_network_setting_wifi_current_connection"

            _value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=False, ctrl_empty_strip=True, replace_empty_to_dash=True, ctrl_lower_case=False)

            value = _value.split('\n')

            _len = len(value)

            if _len > 2:

                value = value[2]

            else:
                value = '--'

            ret_dict.update({"current_connection": value})

            locator_name = "locator_network_setting_ethernet_auto_connect"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            ret_dict.update({"auto_connect": value})

            locator_name = "locator_network_setting_ethernet_manual_connect"

            manual_value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            ret_dict.update({"manual_connect": manual_value})

            self.swipe_up()

            if manual_value == 'true':

                locator_name = "locator_network_setting_ethernet_mac"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='text', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True, ctrl_lower_case=False)

                ret_dict.update({"MAC": value})

                locator_name = "locator_network_setting_ethernet_general"

                eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                eles_dict = {'IP': eles[0],
                             'DNS': eles[1],
                             'gateway': eles[2]}
            else:
                locator_name = "locator_network_setting_ethernet_general_2"

                eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                eles = eles[-4:]

                eles_dict = {'MAC': eles[0],
                             'IP': eles[1],
                             'DNS': eles[2],
                             'gateway': eles[3]}

            r_eles_dict = reverse_dict(eles_dict)

            dict_len = len(eles_dict)

            for index, i in enumerate(eles):

                logger.debug(f"""the value is :{self.get_element_attribute(i, "content-desc")}""")

                _value = self.get_element_attribute(i, "text")

                if dict_len == 4 and index == 0:

                    value = _value.split('\n')[-1].replace(' ', '-')

                else:

                    value = _value.split('\n')[-1].lower().replace(' ', '-')

                ret_dict.update({r_eles_dict[eles[index]]: value})

    def enter_config(self):
        logger.info("nothings")
        locator_comm = ('acc_id', "Configure")

        self.find_element(*locator_comm, click=True)

    def _commission_network_wifi_agate_connect(self, wifi_hotspot=None, wifi_password=None, retry=2, network_mode_change=False):

        logger.info(f'the user kwargs in _commission_network_wifi_agate_connect：wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password},retry:{retry},network_mode_change:{network_mode_change}'
                    )

        locator_connect = get_locator("locator_general_connect")

        ele = self.find_element(*locator_connect, throw_exception=False, timeout=2)
        logger.debug(f'got the connect button element:{ele}')

        if ele:

            logger.info('OK, got the connect button')

            ele.click()

            sleep(1)

        if not network_mode_change:

            # for the 2nd commission, system has connected from system commission page and should redirect to the aPower connecting page...

            self.skip_network_configuration = False

            ele = self.find_element(*locator_connect, throw_exception=False, timeout=2)

            logger.info(f'got the new connect button element:{ele}')

            if ele:

                logger.debug('OK, got the Next button')

                self.skip_network_configuration = True

                ele.click()

            else:
                logger.info('not found the Next button')

                found_ap = None

                for i in range(retry):

                    found_ap = self._connect_agate_wifi(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, wifi_refresh=False)

                    if found_ap:

                        break

                if found_ap is None:

                    raise ValueError(f"Fail to find and connect to WiFi AP:{wifi_hotspot}!")

                # go back,should check the AP is connected here???..............

                if self.model == 'Xiaomi':
                    locator = ('id', "com.android.settings:id/up")

                    self.find_element(*locator, click=True)

                elif self.model == 'Samsung':

                    # pop up 'internet may be not available' window
                    locator = ('id', "com.android.settings:id/keep_btn")

                    self.find_element(*locator, click=True, throw_exception=False, timeout=10)

                    locator = ('acc_id', "Navigate up")

                    for i in range(2):

                        self.find_element(*locator, click=True, throw_exception=False)

                    logger.info('OK,go up twice!')

                    sleep(10)   # need to optimze

                    # Another window pop up unexpectedly.

                    locator = ('xpath', '//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView')

                    self.find_element(*locator, click=True, throw_exception=False, timeout=2)

                    logger.info('OK,go up again!')

                    #  sleep(10)

                    # switch context
                    self.app_activate()
        else:

            found_ap = None

            for i in range(retry):

                found_ap = self._connect_agate_wifi(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, wifi_refresh=False)

                if found_ap:

                    break

            if found_ap is None:

                raise ValueError(f"Fail to find and connect to WiFi AP:{wifi_hotspot}!")

            # go back,should check the AP is connected here???..............

            locator = ('id', "com.android.settings:id/up")

            self.find_element(*locator, click=True)

    def commission_network_wifi_agate_disconnect(self, wifi_hotspot=None, wifi_password=None, retry=2, wifi_disconnect=False, network_mode_change=False):
        """ Disconnect local Wifi connection and go back to Internet connection

        Kwargs:
            | wifi_disconnect | bool | False(缺省),True:断开当前的aGate热点连接  |
            | network_mode_change | bool | False(缺省,用于commission中断开aGate热点),True(用于系统页面中本地切换到远程连接) |
            | wifi_hotspot | string | 用户wifi路由器hotspot name  |
            | wifi_password | string | 用户wifi路由器hotspot password |
        Return:
            | None |
        Examples:
            | Commission Network Wifi Agate Disconnect |  wifi_hotspot=FWH-Office | wifi_password=20191011 |
        """
        logger.info(f'the user kwargs in commission_network_wifi_agate_disconnect：wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password},retry:{retry},wifi_disconnect：{wifi_disconnect}，network_mode_change:{network_mode_change}'
                    )
        if wifi_hotspot is not None and not wifi_disconnect:

            try:
                self._commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=retry)

            except Exception as e:

                logger.info(f'no password is required')

            # go back to upper page

            # locator = ('xpath', """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView""")

            # self.find_element(*locator, click=True)

        if wifi_disconnect:

            # disconnect local Wifi or shutdown it,need to test...
            ...

        # should have next step to do...

        if not network_mode_change:

            if self.model == 'Xiaomi':

                locator = ('acc_id', "Next")

                self.find_element(*locator, click=True)

            elif self.model == 'Samsung':
                self.go_to_commission_connecting_apowers_page()

    def commission_network_wifi_agate_connect(self, wifi_hotspot=None, wifi_password=None, retry=2, network_mode_change=False, source='new'):
        """ Go to Direct Connect Page under Commission->Network Setting Page-Step1-aGate AP part

        Kwargs:
            | wifi_hotspot | string | aGate hotspot name  |
            | wifi_password | string | aGate hotspot password |

        Return:
            | None |
        Examples:
            | Commission Network Wifi Agate Connect |  wifi_hotspot=ULG_TEST | wifi_password=12345678 |
        """
        logger.info(f'the user kwargs in commission_network_wifi_agate_connect：wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password},retry:{retry},network_mode_change:{network_mode_change}'
                    )
        self._commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=retry, network_mode_change=network_mode_change)

        if self.model == 'Xiaomi':

            locator = ('xpath', """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView""")

            self.find_element(*locator, click=True)

        elif self.model == 'Samsung':

            # locator = ('acc_id', "Navigate up")

            # self.find_element(*locator, throw_exception=False, click=True)

            # switch context

            if source != 'new':

                locator = ('xpath', """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView""")

                self.find_element(*locator, throw_exception=False, click=True)

            self.app_activate()

            # ensure the agate is connected here...

            """
            if source == 'new':

                locator = ('acc_id', f"{wifi_hotspot}\nConnected")

                self.find_element(*locator, timeout=40)
            """

            # another scenario for first commission& second commission

            # self.go_to_commission_network_setting_page()  # 第二次commission

        if not network_mode_change:

            if self.model == 'Xiaomi':

                # go back to Network Settings page
                locator = ('xpath', """//android.view.View[@content-desc="Network Settings"]""")

                self.find_element(*locator, click=True)

                # check Wi-Fi connect status

                locator = ('acc_id', f"{wifi_hotspot}\nConnected")

                if self.find_element(*locator, timeout=60):

                    logger.info(f"OK,successfully connect to WiFi:{wifi_hotspot}!")

            # should have next step to do...
            locator = ('acc_id', "Next")
            self.find_element(*locator, throw_exception=False, click=True)

            logger.info("waiting for the AP page to show")
            # choose WIFI AP
            self.find_element(*locator, throw_exception=False, click=True)

    def commission_network_wifi_internet_connect(self, wifi_hotspot="FWH-Office", wifi_password="FWH20191011", first_commission_flag=True):
        """ Go to Direct Connect Page under Commission->Network Setting Page-Step1-User AP part

        Kwargs:
            | wifi_hotspot | string | user AP hotspot name  |
            | wifi_password | string | user AP password |
        Return:
            | None |
        Examples:
            | Commission Network Wifi Internet Connect |  wifi_hotspot=ULG_TEST | wifi_password=12345678 |
        """
        logger.info(f'the user kwargs in commission_network_wifi_internet_connect：wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password}'
                    )
        if not first_commission_flag:

            self.skip_network_configuration = False     # 2.25 for the 2th commission to connect the internet wifi

        if not self.skip_network_configuration:

            # do Wi-Fi enable/disable checkand try to enable WIFI if it's disabled

            locator = ('acc_id', "Enable Wifi")

            self.find_element(*locator, click=True, throw_exception=False, timeout=1)

            # proceed the next if Bluebooth is enabled to commission
            locator = get_locator('locator_general_next')

            self.find_element(*locator, click=True, throw_exception=False, timeout=1)

            if self._connect_wifi_network(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, wifi_refresh=True, mode='internet'):

                logger.info(f"OK,successfully connected to {wifi_hotspot}!")

            else:
                self.save_screenshot()

                raise ValueError(f"Fail to connected to {wifi_hotspot}!")

            # "Help/Reconfiguration/Skip when signal is too weak and connection fails"

            locator = ('acc_id', "Skip")

            self.find_element(*locator, click=True, throw_exception=False, timeout=2)

            # must-have step
            locator = ('acc_id', "Complete")

            ele = self.find_element(*locator, click=True, throw_exception=False, timeout=60)

            if ele is None:

                locator = ('acc_id', "Skip")

                self.find_element(*locator, click=True, throw_exception=False, timeout=2)

            # go to connecting aPower page...
            # must-have step

            # WiFi/4G signal status...

            """
            xpath
            //android.view.View[@content-desc="Wifi\nNormal signal"]
            //android.view.View[@content-desc="4G\nNormal signal"]

            """
            # go to the next page
            if first_commission_flag:      # for the first commission
                locator = ('acc_id', "Next")

                self.find_element(*locator, click=True, timeout=120)

    def _connect_agate_wifi(self, wifi_hotspot="FWH-Office", wifi_password="FWH20191011", wifi_refresh=False, search_loop_number=8, timeout=120):
        logger.info(f'the user kwargs in _connect_agate_wifi：wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password},'
                    f'wifi_refresh:{wifi_refresh},'
                    f'search_loop_number:{search_loop_number}'
                    )

        swipe_up_number = 0

        found_AP = None

        found_AP_in_saved = None

        # scenario1 saved in AP history list...

        ele, found_target = self._login_agate_with_saved_wifi_network(wifi_hotspot=wifi_hotspot)

        logger.info(f'Got the ele:{ele},target:{found_target}')

        if ele:

            if found_target:

                # ele is ok,found_target= True, the AP is in the saved history!

                logger.info(f"the AP is in scenario1: in the saved WiFi AP history list!")

                return ele

            else:

                # ele is ok,found_target=False, the AP is in the first page

                # scenario2: the target AP in the first page of AP list

                """
                if self.model == 'Xiaomi':
                    locator = ('xpath', f"//android.widget.CheckedTextView[@resource-id="android:id/title" and @text="{wifi_hotspot}"]")

                elif self.model == 'Samsung':
                    locator = ('xpath', f"//android.widget.TextView[@resource-id="android:id/title" and @text="{wifi_hotspot}"]")

                ele = self.find_element(*locator, click=True, throw_exception=False, timeout=2)

                if ele:

                    logger.info(f"the AP is in scenario2: in the 1st page AP list!")

                    found_AP = ele

                    logger.info(f'the found wifi:{ele}')
                """
                found_AP = ele

                found_AP.click()

                logger.info(f'the found wifi:{ele}')

                # scenario3: the target AP in the history AP list,but need to refresh

                if wifi_refresh:

                    locator = ('id', "com.android.settings:id/refresh_anim")

                    self.find_element(*locator, click=True)

                    sleep(10)

        # scenario4: the target AP is in the following pages of AP list...

        ap_search_end = False

        if not found_AP:

            logger.info(f"the AP is in scenario4: in the following page AP list!")

            if self.model == 'Xiaomi':

                locator = ('xpath', """//android.widget.CheckedTextView[@resource-id="android:id/title"]""")

                locator_ap_list_end = ('xpath', """//android.widget.TextView[@resource-id="android:id/title" and @text="添加网络"]""")

            elif self.model == 'Samsung':

                locator = ('xpath', """//android.widget.TextView[@resource-id="com.android.settings:id/title"]""")

                locator_ap_list_end = ('xpath', """//android.widget.TextView[@resource-id="com.android.settings:id/title" and @text="Add network"]""")

            # self.swipe_up()

            # swipe_up_number += 1

            for i in range(search_loop_number):

                logger.debug(f"the internal search loop number:{i}")

                start = time()

                while not ap_search_end:

                    try:

                        pre_elements = self.find_element(*locator, throw_exception=False, multiple=True)

                        if not pre_elements:
                            # APP pops location privacy request here...

                            logger.info(f'OK,try to check Location access request from APP...')

                            location_locator = "locator_location_access_request"

                            self._get_locator_and_find_element(location_locator, locator_fun=get_locator, ele_find_func=self.find_element, throw_exception=False, click=True)

                            eles = self.find_element(*locator, throw_exception=False, multiple=True)

                            logger.info(f'OK,got the elements:{eles}')

                        else:
                            eles = pre_elements

                        if eles is not None:

                            ap_name_list = [x.text for x in eles]

                        else:

                            ap_name_list = list()

                        logger.debug(f"the current AP name list:{ap_name_list}")

                        if wifi_hotspot in ap_name_list:

                            logger.info(f"OK,found the target AP:{wifi_hotspot} in {ap_name_list}!")

                            index = ap_name_list.index(wifi_hotspot)

                            found_AP = eles[index]

                            found_AP.click()

                            ap_search_end = True

                            break

                        else:
                            logger.info(f'Not found the AP:{wifi_hotspot} in {ap_name_list}')

                            ele_end = self.find_element(*locator_ap_list_end, throw_exception=False, timeout=2)

                            if ele_end:

                                logger.info("OK,has reached to the bottom of the AP list!Go back to the top of the AP list...")

                                if self.model == 'Samsung':

                                    locator = ('id', "com.android.settings:id/connected_network_category")

                                elif self.model == 'Xiaomi':

                                    locator = ('xpath', """//android.widget.TextView[@resource-id="android:id/title" and @text="WLAN"]""")

                                ele_top = None

                                _start = time()

                                while not ele_top:

                                    if self.model == 'Xiaomi':

                                        self.swipe_down(duration=100)

                                    elif self.model == 'Samsung':

                                        self.swipe_down(start_y=0.4, end_y=0.6, duration=100)

                                    ele_top = self.find_element(*locator, throw_exception=False, timeout=0.5)

                                    _end = time()

                                    _delta = _end - _start

                                    if _delta > 60:

                                        raise ValueError(f'Fail to detect the target AP:{wifi_hotspot}')

                                logger.info("OK,has reached to the top of the AP list!continue to check AP again...")

                                # double check scenario1(saved in AP history list) again

                                ele_1, found_AP_in_saved = self._login_agate_with_saved_wifi_network(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password)

                                if ele_1 and found_AP_in_saved:

                                    logger.info(f"the AP is in scenario1: the saved WiFi AP history list!!")

                                    ap_search_end = True

                                    return ele_1

                                swipe_up_number = 0

                            else:

                                self.swipe_up()

                                swipe_up_number += 1

                                logger.debug(f'the current swipe up number:{swipe_up_number}')

                    #  except (NoSuchElementException, StaleElementReferenceException):
                    except Exception as e:

                        logger.info(f"FAIL to found the AP:{wifi_hotspot} due to {e}in the loop number:{i}!")

                if found_AP:

                    break

                end = time()

                if (end - start) > timeout:

                    raise ValueError(f'Fail to search the target AP:{wifi_hotspot}!')

        if found_AP:

            logger.info(f'OK,found the AP!')

            if not found_AP_in_saved:

                self._login_agate_wifi_network(wifi_password=wifi_password)

        return found_AP

    def _login_agate_with_saved_wifi_network(self, wifi_hotspot=None, wifi_password=None, keyword="Saved"):

        logger.info(f'the user kwargs in _login_agate_with_saved_wifi_network:wifi_hotspot:{wifi_hotspot},keyword:{keyword}')

        if self.model == 'Xiaomi':

            locator = ('xpath', f"""//android.widget.LinearLayout[contains(@content-desc,"{wifi_hotspot}")]""")

            keyword = "Saved"

        elif self.model == 'Samsung':

            locator = ('xpath', f"""//android.widget.TextView[@resource-id="com.android.settings:id/title" and @text="{wifi_hotspot}"]""")

            keyword = 'Connected'

        ele = self.find_element(*locator, throw_exception=False, timeout=2)

        logger.debug(f'the found element:{ele}')

        found_AP_in_saved = False

        if ele:

            if self.model == 'Xiaomi':

                content = self.get_element_attribute(ele, key="content-desc")

                logger.debug(f'the content:{content}')

                if content is None:

                    return (ele, False)

                if keyword in content:

                    found_AP_in_saved = True

                    ele.click()

                    logger.info(f'found the AP:{wifi_hotspot} in the AP history list')

                elif '点击分享' in content:

                    found_AP_in_saved = True

                    logger.info(f'OK,found the AP:{wifi_hotspot} in the AP history list')

                else:

                    sleep(3)

            elif self.model == 'Samsung':

                locator = ('xpath', f"""//android.widget.TextView[@resource-id="com.android.settings:id/title" and @text="{wifi_hotspot}"]""")

                ele = self.find_element(*locator, throw_exception=False, timeout=2)

                if ele:

                    ele.click()

                    found_AP_in_saved = True

                    logger.info(f'found the AP:{wifi_hotspot} in the first page')

                sleep(3)

                # check password input?

                locator = ('id', "com.android.settings:id/textinput_placeholder")

                ele2 = self.find_element(*locator, throw_exception=False)

                if ele2:

                    ele2.click()

                    locator = ('id', "com.android.settings:id/edittext")

                    self.find_element(*locator, send_keys=wifi_password, timeout=10)

                    locator = ('id', "android:id/content")

                    self.find_element(*locator, click=True)

                    # pop up 'internet may be not available' window
                    locator = ('id', "com.android.settings:id/keep_btn")

                    self.find_element(*locator, click=True, throw_exception=False, timeout=3)

                    locator = ('acc_id', "Navigate up")

                    for i in range(2):

                        self.find_element(*locator, click=True)

                    # switch context
                    self.app_activate()

                """
                else:

                    locator = ('xpath',"//android.widget.TextView[@resource-id="com.android.settings:id/summary"])

                    ele = self.find_element(*locator, throw_exception=False, timeout=2)

                    content = self.get_element_attribute(ele, key="text")

                    logger.debug(f'the content:{content}')

                    if content is None:

                        return (ele, False)

                    if "Connected without internet" in content or "quqality" in content:

                        ele.click()

                        found_AP_in_saved = True

                        logger.info(f'found the AP:{wifi_hotspot} in the AP history list')

                    # elif "Connected / Auto reconnect turned off" in content:
                """

        logger.debug(f'the ele:{ele},the found_AP_in_saved flag:{found_AP_in_saved}')

        return (ele, found_AP_in_saved)

    def _login_agate_wifi_network(self, wifi_password=None):

        logger.info(f'the user kwargs in _login_agate_wifi_network:wifi_password:{wifi_password}')

        logger.info("OK,try to input WiFi password")

        if self.model == 'Xiaomi':

            locator = ('cls_name', "android.widget.EditText")

            pre_elements = self.find_element(*locator, throw_exception=False)

            if not pre_elements:
                # APP pops "No Internet connect for switch " warning here...

                logger.info(f'OK,try to check no internet and request to switch from mobile phone...')

                internet_locator = "locator_swith_wifi_due_to_no_internet_request"

                self._get_locator_and_find_element(internet_locator, locator_fun=get_locator, ele_find_func=self.find_element, throw_exception=False, click=True)

            self.find_element(*locator, send_keys=wifi_password, timeout=10)

            locator = ('id', "android:id/button1")

            self.find_element(*locator, click=True)

        elif self.model == 'Samsung':

            locator = ('xpath', """//android.widget.TextView[@resource-id="com.android.settings:id/summary" and @text="Connected without internet"]""")

            already_logined_flag = self.find_element(*locator, throw_exception=False, timeout=3)

            if already_logined_flag is None:

                locator = ('id', "com.android.settings:id/textinput_placeholder")

                self.find_element(*locator, click=True)

                locator = ('id', "com.android.settings:id/edittext")

                self.find_element(*locator, send_keys=wifi_password, timeout=10)

                locator = ('id', "android:id/content")

                self.find_element(*locator, click=True)

                # pop up 'internet may be not available' window
                locator = ('id', "com.android.settings:id/keep_btn")

                self.find_element(*locator, click=True, throw_exception=False, timeout=3)

            # go back to the upper layer

            logger.info('OK, go up to the top page of Setting page')

            locator = ('acc_id', "Navigate up")

            for i in range(2):

                self.find_element(*locator, click=True)

        # switch context to go back to APP
        self.app_activate()

        logger.info("OK,already input the WIFI password!")

        logger.info("OK,the WiFi AP list searching is done!")

    def get_wifi_refresh_time(self):

        delta = self._waiting_for_user_wifi_list_refresh(time_return=True)

        return delta

    def _connect_wifi_network(self, wifi_hotspot="FWH-Office", wifi_password="FWH20191011", wifi_refresh=False, search_loop_number=5, mode='internet'):

        logger.info(f'the user kwargs in _connect_wifi_network:wifi_hotspot:{wifi_hotspot},'
                    f'wifi_password:{wifi_password},wifi_refresh:{wifi_refresh},search_loop_number:{search_loop_number}')

        swipe_up_number = 0

        found_AP = False

        self._waiting_for_user_wifi_list_refresh()

        # 当wifi从互联网连接切换到非internet agate时，可能会跳出‘保持连接’或‘切换WLAN选项’让用户选择

        if self.model == 'Xiaomi':

            locator = "com.android.settings:id/wifi_negative"

            ele = self.find_element("id", locator, capture_screen=False, throw_exception=False, timeout=5, click=True)

        elif self.model == 'Samsung':

            # pop up 'internet may be not available' window
            locator = ('id', "com.android.settings:id/keep_btn")

            self.find_element(*locator, capture_screen=False, click=True, throw_exception=False, timeout=5)

        locator = ('acc_id', f"{wifi_hotspot}")

        ele = self.find_element(*locator, click=True, throw_exception=False, timeout=2)

        logger.info(f'the scenario1 result:{ele}')

        ap_search_end = False

        if ele is None:

            # scenario: the target AP is in the following pages of AP list...

            logger.info("swipe to search the target AP!")

            if self.model == 'Xiaomi' or mode == 'internet':

                locator = ('xpath', "//android.view.View[@content-desc]")

            elif self.model == 'Samsung' and mode != 'internet':

                locator = ('xpath', """//androidx.recyclerview.widget.RecyclerView[@resource-id="com.android.settings:id/twlist"]/android.widget.LinearLayout""")

            locator_ap_list_end = ('acc_id', "Add network")

            self.swipe_up()

            swipe_up_number += 1

            for i in range(search_loop_number):

                logger.debug(f"the internal search loop number:{i}")

                while not ap_search_end:

                    try:

                        if self.model == 'Xiaomi':

                            sleep(5)

                        eles = self.find_element(*locator, throw_exception=False, multiple=True)

                        if eles is not None:

                            if self.model == 'Xiaomi' or mode == 'internet':

                                attr = 'content-desc'

                            elif self.model == 'Samsung' and mode != 'internet':

                                attr = 'text'

                            ap_name_list = [self.get_element_attribute(x, key=attr) for x in eles]

                        else:

                            ap_name_list = list()

                        logger.debug(f"""the current AP name list:{ap_name_list}!""")

                        # j.tag_name
                        if wifi_hotspot in ap_name_list:

                            logger.info(f"OK,found the target AP:{wifi_hotspot}!")

                            index = ap_name_list.index(wifi_hotspot)

                            found_AP = eles[index]

                            found_AP.click()

                            break

                        # check the bottom of the AP list

                        ele = self.find_element(*locator_ap_list_end, throw_exception=False, timeout=2)

                        if ele:

                            logger.info("OK,has reached to the bottom of the AP list!Go back to the top of the AP list...")

                            locator = ('acc_id', "Available networks")

                            ele_top = None

                            _start = time()

                            while not ele_top:

                                self.swipe_down()

                                ele_top = self.find_element(*locator, throw_exception=False, timeout=0.5)

                                _end = time()

                                _delta = _end - _start

                                if _delta > 60:

                                    raise ValueError(f'Fail to detect the target AP:{wifi_hotspot}')

                            logger.info("OK,has reached to the top of the AP list!continue to check AP again...")

                            swipe_up_number = 0

                        else:

                            self.swipe_up()

                            swipe_up_number += 1

                            logger.debug(f'the current swipe up number:{swipe_up_number}')

                    except (NoSuchElementException, StaleElementReferenceException):

                        logger.info(f"FAIL to found the AP:{wifi_hotspot} in the loop number:{i}!")

                if found_AP:

                    break
        else:

            found_AP = True

        if found_AP:

            self._login_user_wifi_network(wifi_password=wifi_password)

        return found_AP

    def _waiting_for_user_wifi_list_refresh(self, time_return=False, refresh_threshold=40):

        logger.info(f'the user kwargs in _waiting_for_user_wifi_list_refresh')

        locator = ('cls_name', "android.widget.ScrollView")

        t_start = time()

        ele = self.find_element(*locator, throw_exception=False, timeout=60)

        t_end = time()

        logger.info(f"the WiFi list refresh time:{t_end - t_start:.4}seconds")

        if (t_end - t_start) > int(refresh_threshold):

            logger.info(f"timeout....detected!")
            self.save_screenshot(file_path='D:/', file_name='aaa.png')

        if time_return:

            return t_end - t_start

        if ele:

            logger.info("OK,the WiFi AP list is shown well now!")

            return ele

    def _login_user_wifi_network(self, wifi_password=None):
        logger.info(f'the user kwargs in _login_user_wifi_network,wifi_password：{wifi_password}')

        logger.info("OK,try to input WiFi password")

        locator = ('xpath', "//android.widget.EditText")

        ele_1 = self.find_element(*locator, send_keys=wifi_password, click=True)

        locator = ('acc_id', "Next")

        ele_2 = self.find_element(*locator, click=True)

        # if password is incorrect, need to re-enter again here
        locator = ('acc_id', "Re-enter")
        ele = self.find_element(*locator, throw_exception=False, click=True, timeout=2)

        if ele:
            logger.info(f"Fail to login the WIFI:{wifi_hotspot} with password:{wifi_password}!")
            ele_1.clear()
            ele_1.send_keys(wifi_password)

        logger.info("OK,already input the WIFI password!")

        logger.info("OK,the WiFi AP list searching is done!")
