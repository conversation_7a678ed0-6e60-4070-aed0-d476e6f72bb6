from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_type, attr_map_common_type_result

# 云平台更新设备参数下行数据/应答云平台更新设备参数上行数据

app_name = "auth"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_c2s = 110

cmd_type_s2c = 109

attr_map_c2s = {

    **attr_map_common_type_result,

}

attr_map_s2c = {

    **attr_map_common_type,

    'distributor': ('', 'string'),  # optional
    'installer': ('', 'string'),
    'maint': ('maintainer', 'string'),  # optional

    'usr_account': ('user_account', 'string'),  # optional
    'reg_date': ('registration_date', 'string'),  # optional
    'GPS': ('', 'list'),

    'timeZone': ('timezone', 'float'),
    'timezoneStr': ('timezone_in_string', 'int'),
    'DST': ('dst', 'int'),
    'zoneinfo': ('zone_info', 'string'),
    'postcode': ('post_code', 'string'),
    'countryCode': ('country_code', 'string'),  # reserved
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class CloundRefresh(object):

    def cloud_parameter_set(self, *args, **kwargs):
        """  the device parameters set-T109/110
        Args:
            | None |
        Kwargs:
            | type | int | 0:edit,1:bonding,2:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date,not used anymore |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string | string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | post_code | string | post code |
            | country_code | string | country code(reserved) |
        Return:
            | None |
        Examples：
        | Cloud Parameter Set | timezone=8 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('type', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def cloud_parameter_from_aws_get(self, *args, **kwargs):
        """  the device parameters which are updated by AWS cloud get-T109

        Args：
            | type | int | 0:edit,1:bonding,2:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date,not used anymore |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string | string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | post_code | string | post code |
            | country_code | string | country code(reserved) |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Cloud Parameter From Aws Get | timezone | GPS |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def cloud_parameter_from_aws_get_check(self, **kwargs):
        """  the device parameters which are updated by AWS cloud get check-T109

        Kwargs:
            | type | int | 0:edit,1:bonding,2:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date,not used anymore |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string | string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | post_code | string | post code |
            | country_code | string | country code(reserved) |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Cloud Parameter From Aws Get Check | type=0 | timezone=-8 | dst=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.cloud_parameter_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def cloud_parameter_from_device_get(self, *args, **kwargs):
        """  the device responses to AWS cloud,the message get-T110

        Args：
            | type | int | 0：编辑；1：绑定；2：解绑  |
            | result |  int | 0:成功,1:失败  |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Cloud Parameter From Device Get | type | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def cloud_parameter_from_device_get_check(self, **kwargs):
        """  the device responses to AWS cloud,the message get check-T110

        Kwargs:
            | type | int | 0：编辑；1：绑定；2：解绑  |
            | result | int | 0:成功,1:失败  |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Cloud Parameter From Device Get Check | result=0 | type=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.cloud_parameter_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def cloud_parameter_info_from_msg_get(self, *args, msg=None, cmd_type=109, **kwargs):
        """  the monitored messages between device<->AWS cloud-T109/110

        Args for T109：
            | type | int | 0:edit,1:bonding,2:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date,not used anymore |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string | string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | post_code | string | post code |
            | country_code | string | country code(reserved) |
        Args for T110：
            | type | int | 0：编辑；1：绑定；2：解绑  |
            | result |  int | 0:成功,1:失败  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 109 or 110 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=109/110 | rx_time_window=300 | filter_mode=and |
        | ${status} | Cloud Parameter Info From Msg Get | type | installer | msg=${packets} |
        | ${status} | Cloud Parameter Info From Msg Get | type | installer | msg=${packets} | cmd_type=109 | ret_format=dict |
        | ${status} | Cloud Parameter Info From Msg Get | installer | msg=${packets} | cmd_type=109 |
        | ${status} | Cloud Parameter Info From Msg Get | installer | msg=${packets} | cmd_type=109 | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=109 | rx_time_window=300 |
        | ${status} | Cloud Parameter Info From Msg Get | result | msg=${packets}[0] | cmd_type=110 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.cloud_parameter_from_device_get, cmd_type_s2c: self.cloud_parameter_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def cloud_parameter_info_from_msg_get_check(self, msg=None, cmd_type=109, **kwargs):
        """  the monitored messages get check between device<->AWS cloud-T109/110

        Kwargs for T109：
            | type | int | 0:edit,1:bonding,2:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date,not used anymore |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string | string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | post_code | string | post code |
            | country_code | string | country code(reserved) |
        Kwargs for T110：
            | type | int | 0：编辑；1：绑定；2：解绑  |
            | result |  int | 0:成功,1:失败  |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 109 or 110 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=109/110 | rx_time_window=300 | filter_mode=and |
        | ${status} | Cloud Parameter Info From Msg Get | type=0 | installer=ABC | msg=${packets} | cmd_type=109 |
        | ${status} | Cloud Parameter Info From Msg Get | result=0 | msg=${packets} | cmd_type=110 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.cloud_parameter_from_device_get_check, cmd_type_s2c: self.cloud_parameter_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
