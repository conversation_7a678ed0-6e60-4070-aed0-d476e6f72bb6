from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 光伏参数配置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 325

cmd_type_c2s = 326

attr_map_s2c = {

    **attr_map_common_opt,

    'remoteSolarEn': ('remote_pv_enabled', 'int'),
    'remoteSolarMode': ('remote_pv_mode', 'int'),
    'solarRatedPower': ('remote_pv_rated_power', 'int'),
    'installProximalsolar': ('near_pv_installed', 'int'),

    'installPV1port': ('install_pv1_port', 'int'),
    'installPV2port': ('install_pv2_port', 'int'),

    'PV1RatedPower': ('pv1_rated_power', 'int'),
    'PV2RatedPower': ('pv2_rated_power', 'int'),
    'loadSolarAmount': ('load_side_pv_number', 'int'),
    'loadSolar1RatedPower': ('load_side_pv1_rated_power', 'int'),
    'loadSolar2RatedPower': ('load_side_pv2_rated_power', 'int'),

    'mainsSolarRatedPower': ('grid_side_pv_rated_power', 'int'),
    'beforePVMeterRatedPower': ('before_meter_pv_rated_power', 'int'),
    'NEMPVRatedPower': ('NEM_pv_rated_power', 'int'),
    'protectTime': ('protect_time', 'int'),


    'reSolarSoc': ('recover_pv_soc', 'int'),

    'solarPower': ('total_pv_power', 'int'),
    'solarRelayStat': ('near_pv_relay_status', 'int'),
    'loadRelay1Stat': ('load_side_pv1_relay_status', 'int'),
    'loadRelay2Stat': ('load_side_pv2_relay_status', 'int'),

    'solarPowerGen': ('pv_generated_energy', 'int'),
    'grid_feed_max': ('grid_max_feed', 'int'),

    'DSPNEMPVEnb': ('NEM_pv_dsp_meter_enabled', 'int'),
    'DSPNEMPVRatePwr': ('NEM_pv_with_dsp_meter_rated_power', 'int'),
    'aGatePvExportEn': ('agate_pv_export_enabled', 'int'),

    'loadSideApboxFeedEn': ('load_side_apbox_feed_enabled', 'int'),
    'maxExportPower': ('max_export_power', 'int'),
    'pvSplitCtEn': ('pv_split_ct_enabled', 'int'),

    'pvSplitCtRatePwr': ('pv_split_ct_rated_power', 'int'),
    'gridSplitCtEn': ('grid_split_ct_enabled', 'int'),
    'splitCtGridPv': ('split_ct_grid_pv_enabled', 'int'),


    'threePhPvEnb': ('three_phase_pv_enabled', 'int'),
    'mpptInstallSta': ('mppt_installed_status', 'list'),

    'mPanPv1En': ('main_panel_pv1_enabled', 'int'),
    'mPanPv1RatedPower': ('main_panel_pv1_rated_power', 'int'),

    'mPanPv2En': ('main_panel_pv1_enabled', 'int'),
    'mPanPv2RatedPower': ('main_panel_pv2_rated_power', 'int'),

    'apbox20Pv': ('apbox2.0_pv_parameters', 'list'),
    'mpptPara': ('mppt_parameters', 'list'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class PvSet(object):

    def pv_set(self, *args, **kwargs):
        """  pv set-T325/326

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | pv Set | opt=0 |
        | ${status} = | pv Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def pv_set_result_check(self, _response, **kwargs):
        """  pv set result check-T326

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | pv Set | opt=1 | pv_enabled=1 |
        | ${status} = | pv Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def pv_set_from_aws_get(self, *args, **kwargs):
        """  pv set from AWS,the message get-T325

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   pv Set From AWS Get | pv_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def pv_set_from_aws_get_check(self, **kwargs):
        """  pv set from AWS,the message get check-T325

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | pv Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.pv_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def pv_set_from_device_get(self, *args, **kwargs):
        """   pv set response from device,the message get-T326

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | pv Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def pv_set_from_device_get_check(self, **kwargs):
        """  pv set response from device,the message get check-T326

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  pv Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.pv_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def pv_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T325/326

        Args for T325:
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Args for T326：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 325/326 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=325 | rx_time_window=300 |
        | ${status} | pv Set From Msg Get | total_pv_power | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.pv_set_from_device_get, cmd_type_s2c: self.pv_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def pv_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T325/326

        Kwargs for T325:
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs for T326：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制,已废弃不用 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能(仅适用于澳洲1.2硬件版本)，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 325/326 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=326 | rx_time_window=300 | filter_mode=and |
        | ${status} | pv Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.pv_set_from_device_get_check, cmd_type_s2c: self.pv_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
