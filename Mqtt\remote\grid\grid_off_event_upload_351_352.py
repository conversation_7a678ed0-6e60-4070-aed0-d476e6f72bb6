from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 主动离网事件推送

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 352

cmd_type_c2s = 351

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    'status': ('', 'string'),
    'timestamp': ('', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class GridOffEventPush(object):

    def grid_off_event_push_from_aws_get(self, *args, **kwargs):
        """  grid off event push from AWS,the message get-T352

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Grid Off Event Push From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def grid_off_event_push_from_aws_get_check(self, **kwargs):
        """  grid off event push  from AWS,the message get check-T352

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Grid Off Event Push From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.grid_off_event_push_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def grid_off_event_push_from_grid_get(self, *args, **kwargs):
        """   grid off event push from grid,the message get-T351

        Args:
            | status | string | 状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ |
            | timestamp | int | 发生时间 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Grid Off Event Push From grid Get | status |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def grid_off_event_push_from_grid_get_check(self, **kwargs):
        """  grid off event push from grid,the message get check-T351

        Kwargs:
            | status | string | 状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ |
            | timestamp | int | 发生时间 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Grid Off Event Push From grid Get Check | status=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.grid_off_event_push_from_grid_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def grid_off_event_push_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T351/352

        Args for T351:
            | status | string | 状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ |
            | timestamp | int | 发生时间 |
        Args for T352：
            | result | int | 结果，1:成功 2:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 351/352 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=352 | rx_time_window=300 |
        | ${status} | Grid Off Event Push From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.grid_off_event_push_from_grid_get, cmd_type_s2c: self.grid_off_event_push_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def grid_off_event_push_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T351/352

        Kwargs for T351:
            | status | string | 状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ |
            | timestamp | int | 发生时间 |
        Kwargs for T352：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 351/352 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=351 | rx_time_window=300 | filter_mode=and |
        | ${status} | Grid Off Event Push From Msg Get Check | status=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.grid_off_event_push_from_grid_get_check, cmd_type_s2c: self.grid_off_event_push_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
