from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 设备控制指令设置和查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 311

cmd_type_c2s = 312

attr_map_s2c = {

    **attr_map_common_opt,

    'order': ('', 'string'),
    'custom': ('site_name', 'string'),

    'modeChoose': ('mode', 'int'),
    'runingMode': ('running_mode', 'int'),
    'stopMode': ('stop_mode', 'int'),
    'stromEn': ('storm_enabled', 'int'),

    'selfMinSoc': ('self_min_soc', 'float'),
    'touMinSoc': ('tou_min_soc', 'float'),
    'backupMaxSoc': ('backup_max_soc', 'float'),
    'genStartSoc': ('generator_start_soc', 'float'),
    'genStopSoc': ('generator_stop_soc', 'float'),
    'BBBackupSoc': ('BB_backup_soc', 'int'),

    'SwMerge': ('payload_switch_merge_enabled', 'int'),

    'Sw1Name': ('payload_switch1_name', 'string'),
    'Sw1MsgType': ('payload_switch1_message_type', 'int'),
    'Sw1SocLowSet': ('payload_switch1_lowest_threshold', 'int'),
    'Sw1Mode': ('payload_switch1_mode', 'int'),
    'Sw1ProLoad': ('payload_switch1_programmed_status', 'int'),
    'Sw1AtuoEn': ('payload_switch1_auto_enabled', 'int'),
    'Sw1Freq': ('payload_switch1_freq', 'int'),
    'Sw1TimeEn': ('payload_switch1_timer_enabled', 'list'),
    'Sw1Time': ('payload_switch1_time', 'list'),
    'Sw1TimeSet': ('payload_switch1_time_set', 'list'),

    'Sw2Name': ('payload_switch2_name', 'string'),
    'Sw2MsgType': ('payload_switch2_message_type', 'int'),
    'Sw2SocLowSet': ('payload_switch2_lowest_threshold', 'int'),
    'Sw2Mode': ('payload_switch2_mode', 'int'),
    'Sw2ProLoad': ('payload_switch2_programmed_status', 'int'),
    'Sw2AtuoEn': ('payload_switch2_auto_enabled', 'int'),
    'Sw2Freq': ('payload_switch2_freq', 'int'),
    'Sw2TimeEn': ('payload_switch2_timer_enabled', 'list'),
    'Sw2Time': ('payload_switch2_time', 'list'),
    'Sw2TimeSet': ('payload_switch2_time_set', 'list'),

    'Sw3Name': ('payload_switch3_name', 'string'),
    'Sw3MsgType': ('payload_switch3_message_type', 'int'),
    'Sw3SocLowSet': ('payload_switch3_lowest_threshold', 'int'),
    'Sw3Mode': ('payload_switch3_mode', 'int'),
    'Sw3ProLoad': ('payload_switch3_programmed_status', 'int'),
    'Sw3AtuoEn': ('payload_switch3_auto_enabled', 'int'),
    'Sw3Freq': ('payload_switch3_freq', 'int'),
    'Sw3TimeEn': ('payload_switch3_timer_enabled', 'list'),
    'Sw3Time': ('payload_switch3_time', 'list'),
    'Sw3TimeSet': ('payload_switch3_time_set', 'list'),

    'gridVoltCheck': ('generator_control_type', 'int'),
    'genStat': ('generator_status', 'int'),
    'CarSwConsSupEnable': ('car_constant_charging_enabled', 'int'),
    'CarSwConsSupEnerge': ('car_constant_charging_energy', 'int'),
    'CarSwConsSupStartTime': ('car_constant_charging_start_time', 'list'),
    'GridChargeEn': ('gride_charging_enabled', 'int'),
}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceControl(object):

    def device_control_set(self, *args, **kwargs):
        """  device control set-T311/312

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs:
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Control Set | generator_control_type=1 |
        | ${status} = | Device Control Set | result | generator_control_type | generator_control_type=2 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._set(*args, **kwargs)

    def device_control_set_result_check(self, _response, **kwargs):
        """  device control set result check-T312

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Device Control Set | generator_control_type=1 |
        | ${status} = | Device Control Set Result Check | ${response} | result=0  | generator_control_type=1 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def device_control_set_from_aws_get(self, *args, **kwargs):
        """  device control set from AWS,the message get-T311

        Args：
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Device Control Set From AWS Get | generator_control_type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_control_set_from_aws_get_check(self, **kwargs):
        """  device control set from AWS,the message get check-T311

        Kwargs:
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |   Device Control Set From AWS Get Check | generator_control_type=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_control_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_control_set_from_device_get(self, *args, **kwargs):
        """   device control set response from device,the message get-T312

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Control Set From Device Get | result | generator_status |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_control_set_from_device_get_check(self, **kwargs):
        """  device control set response from device,the message get check-T312

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Control Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_control_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_control_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T311/312

        Args for T311:
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Args for T312：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 311/312 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=311 | rx_time_window=300 |
        | ${status} | Device Control Set From Msg Get | generator_status | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_control_set_from_device_get, cmd_type_s2c: self.device_control_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_control_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T311/312

        Kwargs for T311:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs for TT312：
            | opt | int | 0:查询，1:设置 |
            | order | string | 升级工单号 |
            | site_name | string | <=64B, 网关名称 |
            | mode | int | 模式， 1：备电；2：自发自用；3：TOU；4：Vpp；5：BB&NEM；6：BB&CSS；7：BB&CGS |
            | running_mode | int | 运行模式，0：手动待机状态；1：备电模式；2：自发自用；3：TOU(高级)；4：VPP；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | stop_mode | int | 停机维护模式，0：默认模式；1：进入模式；2：退出模式 |
            | storm_enabled | int | 风暴备电使能，0：禁止，1：使能 |
            | self_min_soc  | float | 自发自用模式保留soc | 单位：% |
            | tou_min_soc | float | TOU模式保留soc | 单位：% |
            | backup_max_soc | float | 备电充电最高soc | 单位：% |
            | generator_start_soc | float | 启动发电机soc | 单位：% |
            | generator_stop_soc | float | 发电机充电停止soc | 单位：% |
            | BB_backup_soc | int | BB放电预留soc | 单位：% |
            | payload_switch_merge_enabled | int | 智能负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 智能负载开关1名称 |
            | payload_switch1_message_type | int | 智能负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | int | 智能负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | int | 智能负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | int | 智能负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | int | 智能负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | int | 智能负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 智能负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 智能负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 智能负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 智能负载开关2名称 |
            | payload_switch2_message_type | int | 智能负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | int | 智能负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | int | 智能负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | int | 智能负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | int | 智能负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | int | 智能负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 智能负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 智能负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 智能负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 智能负载开关3名称 |
            | payload_switch3_message_type | int | 智能负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | int | 智能负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | int | 智能负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | int | 智能负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | int | 智能负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | int | 智能负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 智能负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 智能负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 智能负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | gride_charging_enabled | int | 电网充电使能，0：不允许电网充电；1：允许电网充电 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 311/312 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=312 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Control Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_control_set_from_device_get_check, cmd_type_s2c: self.device_control_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
