from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 设备日志上传的结果通知

app_name = "debug"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 614

cmd_type_c2s = 613

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    **attr_map_common_result,

    'sign': ('', 'string'),
    'fileName': ('file_name', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class ModuleLogUploadResult(object):

    def module_log_upload_result_from_aws_get(self, *args, **kwargs):
        """  module log upload result from AWS,the message get-T614

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Module Log Upload Result From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def module_log_upload_result_from_aws_get_check(self, **kwargs):
        """  module log upload result from AWS,the message get check-T614

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Module Log Upload Result From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.module_log_upload_result_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def module_log_upload_result_from_device_get(self, *args, **kwargs):
        """   module log upload result from device,the message get-T613

        Args:
            | result | int | 结果，0：上传成功；1：上传失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sign | string | 文件MD5签名，32Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Module Log Upload Result From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def module_log_upload_result_from_device_get_check(self, **kwargs):
        """  module log upload result from device,the message get check-T613

        Kwargs:
            | result | int | 结果，0：上传成功；1：上传失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sign | string | 文件MD5签名，32Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Module Log Upload Result From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.module_log_upload_result_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def module_log_upload_result_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T613/614

        Kwargs for T613：
            | result | int | 结果，0：上传成功；1：上传失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sign | string | 文件MD5签名，32Bytes |
        Kwargs for T614:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 614/613 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=613 | rx_time_window=300 |
        | ${status} | Module Log Upload Result From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.module_log_upload_result_from_device_get, cmd_type_s2c: self.module_log_upload_result_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def module_log_upload_result_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T613/614

        Kwargs for T613：
            | result | int | 结果，0：上传成功；1：上传失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sign | string | 文件MD5签名，32Bytes |
        Kwargs for T614:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 614/613 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=614 | rx_time_window=300 | filter_mode=and |
        | ${status} | Module Log Upload Result From Msg Get Check| result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.module_log_upload_result_from_device_get_check, cmd_type_s2c: self.module_log_upload_result_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
