from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result, attr_map_common_type

# 备电日志上传

app_name = "energy"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 405

cmd_type_s2c = 406

attr_map_s2c = {

    **attr_map_common_result,
}

attr_map_c2s = {

    **attr_map_common_type,

    'startTime': ('start_time', 'string'),
    'endTime': ('end_time', 'string'),
    'energy': ('', 'float'),
    'lastTime': ('last_time', 'int'),
    'startSoc': ('start_soc', 'int'),
    'endSoc': ('end_soc', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class BackupPowerSupplyLogUpload(object):

    def backup_power_supply_log_upload_from_aws_get(self, *args, **kwargs):
        """  backup power supply log upload response from AWS,the message get-T406

        Args：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Backup Power Supply Log Upload From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def backup_power_supply_log_upload_from_aws_get_check(self, **kwargs):
        """  backup power supply log upload response from AWS,the message get check-T406

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Backup Power Supply Log Upload From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.backup_power_supply_log_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def backup_power_supply_log_upload_from_device_get(self, *args, **kwargs):
        """  backup power supply log upload from device,the message get-T405

        Args：
            | type | int | 备电类型，1:风暴模式备电，2：停电备电，3：手动备电 |
            | start_time | string | 备电开始时间 |
            | end_time | string | 备电结束时间 |
            | energy | float | 备电电量，单位：kwh |
            | last_time | int | 备电时长， 单位：s |
            | start_soc | int | 备电前soc，单位：% |
            | end_soc | int | 备电后soc，单位：% |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Backup Power Supply Log Upload From Device Get | alarm_snap_file |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def backup_power_supply_log_upload_from_device_get_check(self, **kwargs):
        """  backup power supply log upload from device,the message get check-T405

        Kwargs:
            | type | int | 备电类型，1:风暴模式备电，2：停电备电，3：手动备电 |
            | start_time | string | 备电开始时间 |
            | end_time | string | 备电结束时间 |
            | energy | float | 备电电量，单位：kwh |
            | last_time | int | 备电时长， 单位：s |
            | start_soc | int | 备电前soc，单位：% |
            | end_soc | int | 备电后soc，单位：% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Backup Power Supply Log Upload From Device Get Check | current_alarm_list=@{EMPTY} |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.backup_power_supply_log_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def backup_power_supply_log_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T405/406

        Args for T405:
            | type | int | 备电类型，1:风暴模式备电，2：停电备电，3：手动备电 |
            | start_time | string | 备电开始时间 |
            | end_time | string | 备电结束时间 |
            | energy | float | 备电电量，单位：kwh |
            | last_time | int | 备电时长， 单位：s |
            | start_soc | int | 备电前soc，单位：% |
            | end_soc | int | 备电后soc，单位：% |
        Args for T406：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 405/406 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=405 | rx_time_window=300 |
        | ${status} | Backup Power Supply Log Upload From Msg Get | start_time | end_time | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.backup_power_supply_log_upload_from_device_get, cmd_type_s2c: self.backup_power_supply_log_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def backup_power_supply_log_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T405/406

        Kwargs for T405:
            | type | int | 备电类型，1:风暴模式备电，2：停电备电，3：手动备电 |
            | start_time | string | 备电开始时间 |
            | end_time | string | 备电结束时间 |
            | energy | float | 备电电量，单位：kwh |
            | last_time | int | 备电时长， 单位：s |
            | start_soc | int | 备电前soc，单位：% |
            | end_soc | int | 备电后soc，单位：% |
        Kwargs for T406：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 405/406 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=406 | rx_time_window=300 | filter_mode=and |
        | ${status} | Backup Power Supply Log Upload From Msg Get Check | type=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.backup_power_supply_log_upload_from_device_get_check, cmd_type_s2c: self.backup_power_supply_log_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
