from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)
from copy import deepcopy

mapping_dict_yes_or_no = {'yes': 'Yes', 'no': 'No'}


class Sysparameter(Base):

    def go_to_commission_system_parameters_page(self):
        """ Go to system->commission->system parameters page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission System Parameters Page |
        """
        value = "locator_system_parameters"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_system_parameters_page(self, operate='next'):
        """ Go to system->commission->system parameters page and go back to the system->commission page  or next page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission System Parameters Page | operate=next |
            | Exit Commission System Parameters Page | operate=exit |
        """
        _operate = operate.lower()

        if operate == 'exit':

            self._general_backward_upper_page_type2()

        elif operate == 'next':

            self._system_next_locator()

    def go_to_commission_system_parameters_solar_page(self):
        """ Go to system->commission->system parameters->solar page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission System Parameters Solar Page |
        """
        value = "locator_system_pv"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def exit_commission_system_parameters_solar_page(self):
        """ Go to system->commission->system parameters->solar page and go back to the system->commission page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission System Parameters Solar Page |
        """
        self._general_backward_upper_page_type2()

    def commission_system_parameters(self, **kwargs):
        """ configure commission->system parameters

        Kwargs:
            | grid_connected | bool | yes/no |
            | backup_plan | string | whole-home-backup/partial-backup |
            | main_breaker | int | 100/125/150/175/200/ any value in 30~200,单位:A |
            | branch_breaker | int  | 100/125/150/175/200/ any value in 30~200,单位:A, only for partial-backup  |
            | apower_rated_output_power | string | 2.9kVA/5.8kVA/7.7kVA/9.6kVA/11.5kVA |
            | main_load_relay_installed | bool | yes/no |
            | service_voltage | string | 120/240v-single-phase,3-wire和120/208v-single-phase,3-wire |
            | agate_pv_installed | bool |
            | agate_solar_split_ct_installed | bool |
            | agate_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 agate_pv_installed为true有效 |
            | agate_pv_allowed_export_to_grid | bool | 仅当 agate_pv_installed为true有效 |
            | agate_pv_max_allowed_power_export_to_grid | float or string | 0.1~99999(单位：kW,精度：0.1) or no-power-limit,仅当agate_pv_allowed_export_to_grid为true有效 |
            | apbox_enabled | bool | aPbox是否已安装 |
            | apobx_access_mode | string | grid-side/load-side |
            | apbox_grid_side_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为grid-side有效 |
            | apbox_load_side_pv_number | int | 1/2 | 仅当 apobx_access_mode为Load-side有效 |
            | apbox_load_side_pv_1_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | apbox_load_side_pv_2_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | meter_kit_enabled | bool | Meter Kit是否已安装 |
            | meter_kit_rated_power | float | 0~40,单位：kW,精度：0.1 |
            | second_pv_meter_ct_installed | bool |
            | second_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 second_pv_meter_ct_installed为true有效 |
            | grid_meter_split_ct_installed | bool |
            | grid_split_ct_accessory | string | none/secondary-solar-meter-(ct) |
        Return:
            | None |
        Examples:
            | Config Commission System Parameters | grid_connected=yes | service_voltage=20/208v-single-phase,3-wire |
        """
        self.config_commission_system_parameters(**kwargs)

        # self.exit_commission_system_parameters_page(operate='next')

        locator = "locator_general_next"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # generator Vs. 240V

        locator = ('acc_id', 'Disconnect Generator')

        ele = self.find_element(*locator, throw_exception=False, timeout=1)

        if ele:

            ele.click()

    def config_commission_system_parameters(self, **kwargs):
        """ configure commission->system parameters

        Kwargs:
            | grid_connected | bool | yes/no |
            | backup_plan | string | whole-home-backup/partial-backup |
            | main_breaker | int | 100/125/150/175/200/ any value in 30~200,单位:A |
            | branch_breaker | int  | 100/125/150/175/200/ any value in 30~200,单位:A, only for partial-backup  |
            | apower_rated_output_power | string | 2.9kVA/5.8kVA/7.7kVA/9.6kVA/11.5kVA |
            | main_load_relay_installed | bool | yes/no |
            | service_voltage | string | 120/240v-single-phase,3-wire,120/208v-single-phase,3-wire |
            | agate_pv_installed | bool | enabled/disabled |
            | agate_solar_split_ct_installed | bool | enabled/disabled |
            | agate_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 agate_pv_installed为true有效 |
            | agate_pv_allowed_export_to_grid | bool | enabled/disabled | 仅当 agate_pv_installed为true有效 |
            | agate_pv_max_allowed_power_export_to_grid | float or string | 0.1~99999(单位：kW,精度：0.1) or no-power-limit,仅当agate_pv_allowed_export_to_grid为true有效 |
            | apbox_enabled | bool | aPbox是否已安装 |
            | apobx_access_mode | string | grid-side/load-side |
            | apbox_grid_side_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为grid-side有效 |
            | apbox_load_side_pv_number | int | 1/2 | 仅当 apobx_access_mode为Load-side有效 |
            | apbox_load_side_pv_1_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | apbox_load_side_pv_2_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | meter_kit_enabled | bool | enabled/disabled | Meter Kit是否已安装 |
            | meter_kit_rated_power | float | 0~40,单位：kW,精度：0.1 |
            | second_pv_meter_ct_installed | bool | enabled/disabled |
            | second_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 second_pv_meter_ct_installed为true有效 |
            | grid_meter_split_ct_installed | bool | enabled/disabled |
            | grid_split_ct_accessory | string | none/secondary-solar-meter-(ct) |
        Return:
            | None |
        Examples:
            | Config Commission System Parameters | grid_connected=yes | service_voltage=20/208v-single-phase,3-wire |
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        locator_name = "locator_system_general"

        if kwargs:

            eles_1 = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

            eles_1, eles_2 = eles_1[1:], eles_1[0]

        # Grid/Main Breaker/main load relay/service voltage

        if 'grid_connected' in kwargs or 'main_load_relay_installed' in kwargs:

            self.set_value_in_seekbar(eles_1[0], target_attribute='grid_connected', mapping_dict=mapping_dict_yes_or_no, _dict=kwargs)

            self.set_value_in_seekbar(eles_1[2], target_attribute='main_load_relay_installed', mapping_dict=mapping_dict_yes_or_no, _dict=kwargs)

        # Bakup plan

        if 'backup_plan' in kwargs:

            locator_name = "locator_system_backup_plan"

            ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

            mapping_dict = {'whole-home-backup': 'Whole-home Backup', 'partial-backup': 'Partial Backup'}

            self.set_value_in_seekbar(ele, target_attribute='backup_plan', mapping_dict=mapping_dict, _dict=kwargs)

        # Main Breaker

        if 'main_breaker' in kwargs:

            mapping_dict = {'100': '100 A',
                            '125': '125 A',
                            '150': '150 A',
                            '175': '175 A',
                            '200': '200 A',
                            }

            if kwargs['main_breaker'] in mapping_dict:

                self.set_value_in_drop_down_list(eles_1[1], target_value='main_breaker', mapping_dict=mapping_dict, _dict=kwargs, custom=False, confirm_dialog=True, method='acc_id')

            else:

                self.set_value_in_drop_down_list(eles_1[1], target_value='main_breaker', mapping_dict=mapping_dict, _dict=kwargs, custom=True, confirm_dialog=True, custom_attribe='//android.widget.ImageView[contains(@content-desc,"Customize")]', edit_text="android.widget.EditText")

        #  apower_rated_output_power for aPower2/S

        if 'apower_rated_output_power' in kwargs:

            locator_name = "Locator_apower_rated_output_power"

            ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

            self.get_element_coordinate_and_action(ele, location='center', action='click')

            mapping_dict = {'2.9kVA': '2.9 kVA (2.5 kW)',
                            '5.8kVA': '5.8 kVA (5.0 kW)',
                            '7.7kVA': '7.7 kVA (6.7 kW)',
                            '9.6kVA': '9.6 kVA (8.4 kW)',
                            '11.5kVA': '11.5 kVA (10.0 kW)',
                            }

            if kwargs['apower_rated_output_power'] in mapping_dict:

                self.set_value_in_drop_down_list(ele, target_value='apower_rated_output_power', target_element_click=False, mapping_dict=mapping_dict, _dict=kwargs, custom=False, confirm_dialog=True, method='acc_id')

        # Grid/Main Breaker/main load relay/service voltage

        if 'service_voltage' in kwargs:

            self.swipe_up()

            locator_name = "locator_system_general"

            eles_1 = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

            mapping_dict = {'120/240v-single-phase,3-wire': '120/240 V Single-phase, 3-wire', '120/208v-single-phase,3-wire': '120/208 V Single-phase, 3-wire'}

            self.set_value_in_seekbar(eles_1[-1], target_attribute='service_voltage', mapping_dict=mapping_dict, _dict=kwargs)

            self._system_confirm_locator(throw_exception=False, timeout=2)

        # PV

        self._config_pv(eles_2, **kwargs)

        # Other
        """
        locator = "locator_system_general_confirm"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        """
    def _config_pv(self, ele, **kwargs):

        # PV

        if kwargs:

            ele.click()

            if 'agate_pv_installed' in kwargs:

                locator_name = "locator_system_pv_get"

                _value = kwargs['agate_pv_installed']

                self._config_select_element_status(locator_name, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=_value)

            if 'agate_pv_rated_power' in kwargs:

                locator_name = "locator_system_agate_pv_rated_power"

                _value = kwargs['agate_pv_rated_power']

                self._set_value_in_drop_pop_element(locator_name, locator_fun=get_locator, value=_value, confirm=True)

            if 'second_pv_meter_ct_installed' in kwargs:

                locator_name = "locator_system_pv_secondary_pv_meter"

                _value = kwargs['second_pv_meter_ct_installed']

                self._config_select_element_status(locator_name, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=_value)

            if 'second_pv_rated_power' in kwargs:

                locator_name = "locator_system_second_pv_rated_power"

                _value = kwargs['second_pv_rated_power']

                self._set_value_in_drop_pop_element(locator_name, locator_fun=get_locator, value=_value, confirm=True)

            if 'agate_pv_allowed_export_to_grid' in kwargs:

                locator_name = "locator_system_pv_agate_allowed_export_to_grid_get"

                _value = kwargs['agate_pv_allowed_export_to_grid']

                self._config_select_element_status(locator_name, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=_value)

            if 'agate_pv_max_allowed_power_export_to_grid ' in kwargs:  # no debug????

                locator_name = "locator_system_agate_pv_allowed_export_to_grid"

            if 'agate_solar_split_ct_installed' in kwargs:

                locator_name = "locator_system_pv_agate_splitct"

                _value = kwargs['agate_solar_split_ct_installed']

                self._config_select_element_status(locator_name, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=_value)

            if 'agate_solar_split_ct_rated_power' in kwargs:

                locator_name = "locator_system_pv_agate_splitct_rated_power"

                _value = kwargs['agate_solar_split_ct_rated_power']

                self._set_value_in_drop_pop_element(locator_name, locator_fun=get_locator, value=_value, confirm=True)

            if 'agate_solar_split_ct_installed' in kwargs:

                locator_name = "locator_system_pv_agate_splitct"

                _value = kwargs['agate_solar_split_ct_installed']

                self._config_select_element_status(locator_name, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=_value)

            if 'grid_split_ct_accessory' in kwargs:  # no debug??

                pass

            # PV- Meter Kit check

            # aPbox

            if kwargs:

                locator = "locator_system_general_confirm"

                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def get_commission_system_parameters(self, *args, **kwargs):
        """ get commission->system parameters

        args:
            | grid_connected | bool | yes/no |
            | backup_plan | string | whole-home-backup/partial-backup |
            | main_breaker | int | 100/125/150/175/200/ any value in 30~200,单位:A |
            | branch_breaker | int  | 100/125/150/175/200/ any value in 30~200,单位:A, only for partial-backup  |
            | main_load_relay_installed | bool | yes/no |
            | service_voltage | string | 120/240v-single-phase,3-wire,120/208v-single-phase,3-wire |
            | agate_pv_installed | bool |
            | agate_solar_split_ct_installed | bool |
            | agate_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 agate_pv_installed为true有效 |
            | agate_pv_allowed_export_to_grid | bool | 仅当 agate_pv_installed为true有效 |
            | agate_pv_max_allowed_power_export_to_grid | float or string | 0.1~99999(单位：kW,精度：0.1) or no-power-limit,仅当agate_pv_allowed_export_to_grid为true有效 |
            | apbox_enabled | bool | aPbox是否已安装 |
            | apobx_access_mode | string | grid-side/load-side |
            | apbox_grid_side_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为grid-side有效 |
            | apbox_load_side_pv_number | int | 1/2 | 仅当 apobx_access_mode为Load-side有效 |
            | apbox_load_side_pv_1_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | apbox_load_side_pv_2_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | meter_kit_enabled | bool | Meter Kit是否已安装 |
            | meter_kit_rated_power | float | 0~40,单位：kW,精度：0.1 |
            | second_pv_meter_ct_installed | bool |
            | second_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 second_pv_meter_ct_installed为true有效 |
            | grid_meter_split_ct_installed | bool |
            | grid_split_ct_accessory | string | none/secondary-solar-meter-(ct) |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission System Parameters  |
            | ${result}	| Get Commission System Parameters  |  grid_connected	| backup_plan | main_breaker |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        non_pv_parameters = []

        pv_parameters = []

        _args = []

        if args:

            _args = deepcopy(list(args))

            for i in _args:

                if i in ['grid_connected', 'backup_plan', 'main_breaker', 'main_load_relay_installed', 'service_voltage']:

                    non_pv_parameters.append(i)

                else:

                    pv_parameters.append(i)

        ret_dict = {}

        if non_pv_parameters or (not _args):

            # Grid/Main Breaker/main load relay/service voltage

            self._get_general(ret_dict)

            # Bakup plan

            self._get_backup_plan(ret_dict)

        # PV
        pv_dict = {}

        if pv_parameters or (not _args):

            pv_dict = self.get_commission_system_pv_parameters()

        ret_dict = {**ret_dict, **pv_dict}

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def get_commission_system_pv_parameters(self, *args, **kwargs):
        """ get commission->system parameters->Solar

        args:
            | agate_pv_installed | bool |
            | agate_solar_split_ct_installed | bool | 仅当agate_pv_max_allowed_power_export_to_grid为no-power-limit有效 |
            | agate_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当agate_pv_installed为true有效 |
            | agate_solar_split_ct_rated_power | float | 0~40,单位：kW,精度：0.1,仅当agate_solar_split_ct_installed为true有效 |
            | agate_pv_allowed_export_to_grid | bool | 仅当 agate_pv_installed为true有效 |
            | agate_pv_max_allowed_power_export_to_grid | float or string | 0.1~99999(单位：kW,精度：0.1) or no-power-limit,仅当agate_pv_allowed_export_to_grid为true有效 |
            | apbox_enabled | bool | aPbox是否已安装 |
            | apobx_access_mode | string | grid-side/load-side/solar-meter-upstream |
            | apbox_grid_side_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为grid-side有效 |
            | apbox_load_side_pv_number | int | 1/2 | 仅当 apobx_access_mode为Load-side有效 |
            | apbox_load_side_pv_1_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | apbox_load_side_pv_2_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 apobx_access_mode为load-side有效 |
            | meter_kit_enabled | bool | Meter Kit是否已安装 |
            | meter_kit_rated_power | float | 0~40,单位：kW,精度：0.1 |
            | second_pv_meter_ct_installed | bool |
            | second_pv_rated_power | float | 0~40,单位：kW,精度：0.1,仅当 second_pv_meter_ct_installed为true有效 |
            | grid_meter_split_ct_installed | bool |
            | grid_split_ct_accessory | string | none/secondary-solar-meter-(ct) |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Return:
            | dict |
        Examples:
            | ${result}	| get_commission_location  |
            | ${result}	| get_commission_location  |  country	| ret_format=list |
            | ${result}	| get_commission_location  |  country	| ret_format=list |  ret_type=list |
            | ${result}	| get_commission_location  |  country	| ret_format=dict |
            | ${result}	| get_commission_location  |  country	| address   | ret_format=list |
            | ${result}	| get_commission_location  |  country	| address   | ret_format=dict |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        # PV-1

        pv_installed = self._get_pv_1(ret_dict)

        # PV- Meter Kit check

        meter_kit_flag = self._get_pv_meter_kit(ret_dict)

        # aPbox

        apbox_flag = self._get_apbox(ret_dict)

        # PV-2
        if pv_installed == 'true':

            self._get_pv_2(ret_dict)

        # Other:secondary PV Meter(CT)/secondary PV rated power/PV&Grid splitCT,etc.

        self._get_other(ret_dict)

        self.exit_commission_system_parameters_solar_page()

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_general(self, ret_dict):

        locator_name = "locator_system_general"

        eles_1 = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        eles_1 = eles_1[1:]

        eles_1_dict = {'grid_connected': eles_1[0],
                       'main_breaker': eles_1[1],
                       'main_load_relay_installed': eles_1[2],
                       'service_voltage': eles_1[3],

                       }
        r_eles_1_dict = reverse_dict(eles_1_dict)

        for index, i in enumerate(eles_1):

            logger.debug(f"""the value is :{self.get_element_attribute(i, "content-desc")}""")

            _value = self.get_element_attribute(i, "content-desc")

            value = _value.split('\n')[-1].lower().replace(' ', '-')

            ret_dict.update({r_eles_1_dict[eles_1[index]]: value})

    def _get_backup_plan(self, ret_dict):

        locator_name = "locator_system_backup_plan"

        value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

        ret_dict.update({"backup_plan": value})

    def _get_pv_1(self, ret_dict):

        # go into solar page

        locator_name = "locator_system_pv"

        self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        locator_name = "locator_system_pv_get"

        general_ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

        pv_installed = self.get_element_attribute(general_ele, "checked")

        ret_dict.update({"agate_pv_installed": pv_installed})

        if pv_installed == 'true':

            locator_name = "locator_system_agate_pv_rated_power"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True)

            value = value.split('kw')[0]

            ret_dict.update({"agate_pv_rated_power": value})

        return pv_installed

    def _get_pv_2(self, ret_dict):

        # aGate PV allow export to Grid and max power export to Grid

        locator_name = "locator_system_pv_agate_allowed_export_to_grid_get"

        general_ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, auto_scroll=True)

        agate_pv_allowed_export_to_grid = self.get_element_attribute(general_ele, "checked")

        ret_dict.update({"agate_pv_allowed_export_to_grid": agate_pv_allowed_export_to_grid})

        if agate_pv_allowed_export_to_grid == 'true':

            locator_name = "locator_system_agate_pv_allowed_export_to_grid"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            value = value.split('kw')[0]

            ret_dict.update({"agate_pv_max_allowed_power_export_to_grid": value})

    def _get_pv_meter_kit(self, ret_dict):

        meter_kit_flag = 'false'

        value = "locator_system_pv_meter_kit"

        locator = get_locator(value)

        ele = self.find_element(*locator, throw_exception=False, timeout=2)

        if ele:

            meter_kit_flag = 'true'

            locator_name = "locator_system_pv_meter_kit"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True)

            value = value.split('kw')[0]

            ret_dict.update({"meter_kit_rated_power": value})

        ret_dict.update({"meter_kit_enabled": meter_kit_flag})

        return meter_kit_flag

    def _get_apbox(self, ret_dict):

        apbox_flag = 'false'

        value = "locator_system_apbox"

        locator = get_locator(value)

        ele = self.find_element(*locator, throw_exception=False, timeout=3)

        if ele:

            apbox_flag = 'true'

            _value = self.get_element_attribute(ele, "content-desc")

            value = _value.split('\n')[-1].replace(' ', '-').lower()

            ret_dict.update({"apobx_access_mode": value})

            if value == 'load-side':

                locator_name = "locator_apbox_load_side_pv_number"

                apbox_load_side_pv_num = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                ret_dict.update({"apbox_load_side_pv_number": apbox_load_side_pv_num})

                locator_name = "locator_apbox_load_side_pv_1_rated_power"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                value = value.split('kw')[0]

                ret_dict.update({"apbox_load_side_pv_1_rated_power": value})

                if apbox_load_side_pv_num == '2':

                    locator_name = "locator_apbox_load_side_pv_2_rated_power"

                    value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                    value = value.split('kw')[0]

                    ret_dict.update({"apbox_load_side_pv_2_rated_power": value})

                locator_name = "locator_apbox_load_side_allow_to_grid"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                ret_dict.update({"apbox_load_side_pv_allow_export_to_grid": value})

            elif value == 'grid-side':

                locator_name = "locator_apbox_grid_side_pv_rated_power"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                value = value.split('kw')[0]

                ret_dict.update({"apbox_grid_side_pv_rated_power": value})

        ret_dict.update({"apbox_enabled": apbox_flag})

        return apbox_flag

    def _get_other(self, ret_dict):

        # Second PV Meter CT

        locator_name = "locator_system_pv_secondary_pv_meter"

        second_pv_meter_ct_installed = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

        ret_dict.update({"second_pv_meter_ct_installed": second_pv_meter_ct_installed})

        # aGate PV splitCT

        locator_name = "locator_system_pv_agate_splitct"

        agate_solar_split_ct_installed = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

        ret_dict.update({"agate_solar_split_ct_installed": agate_solar_split_ct_installed})

        if ret_dict.get("agate_pv_max_allowed_power_export_to_grid", None) == "no-power-limit":

            if agate_solar_split_ct_installed == 'true':

                locator_name = "locator_system_pv_agate_splitct_rated_power"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

                value = value.split('kw')[0]

                ret_dict.update({"agate_solar_split_ct_rated_power": value})

        # grid splitCT

        locator_name = "locator_system_grid_splitct"

        grid_meter_split_ct_installed = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

        ret_dict.update({"grid_meter_split_ct_installed": grid_meter_split_ct_installed})

        if second_pv_meter_ct_installed == 'true':

            locator_name = "locator_system_second_pv_rated_power"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, auto_scroll=True)

            value = value.split('kw')[0]

            ret_dict.update({"second_pv_rated_power": value})

        # Grid splitCT accessory

        if second_pv_meter_ct_installed == 'true' and grid_meter_split_ct_installed == 'true':

            locator_name = "locator_system_grid_split_ct_accessory"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True, auto_scroll=True)

            ret_dict.update({"grid_split_ct_accessory": value})
