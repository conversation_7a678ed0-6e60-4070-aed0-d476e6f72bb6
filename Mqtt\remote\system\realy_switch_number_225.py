from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 继电器切断次数数据上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 225

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {
    'agatePvRlyCutCnt': ('aGate_PV_cutoff_number', 'int'),
    'loadApbox1RlyCutCnt': ('aPbox1_cutoff_number', 'int'),
    'loadApbox2RlyCutCnt': ('aPbox2_cutoff_number', 'int'),
    'agatePvSubRlyCutCnt': ('aGate_subPV_cutoff_number', 'int'),
    'gridRlyCutCnt': ('grid_relay_cutoff_number', 'int'),
    'bsRlyCutCnt': ('black_start_relay_cutoff_number', 'int'),
    'smt1RlyCutCnt': ('smart_load1_relay_cutoff_number', 'int'),
    'smt2RlyCutCnt': ('smart_load2_relay_cutoff_number', 'int'),
    'smt3RlyCutCnt': ('smart_load3_relay_cutoff_number', 'int'),
    'genRlyCutCnt': ('generator_relay_cutoff_number', 'int'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class RelaySwitchNumber(object):

    def relay_switch_number_data_from_device_get(self, *args, **kwargs):
        """  relay switch number data from device,the message get-T225

        Args：
            | aGate_PV_cutoff_number | int | aGate光伏被切断次数 |
            | aPbox1_cutoff_number | int | aPbox1负载侧被切断次数 |
            | aPbox2_cutoff_number | int | aPbox2负载侧被切断次数 |
            | aGate_subPV_cutoff_number | int | aGate子光伏被切断次数 |
            | grid_relay_cutoff_number | int | 市电继电器被切断次数 |
            | black_start_relay_cutoff_number | int | 黑启动继电器被切断次数 |
            | smart_load1_relay_cutoff_number | int | 智能负载1继电器被切断次数 |
            | smart_load2_relay_cutoff_number | int | 智能负载2继电器被切断次数 |
            | smart_load3_relay_cutoff_number | int | 智能负载3继电器被切断次数 |
            | generator_relay_cutoff_number | int | 发电机继电器被切断次数 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Relay Switch Number Data From Device Get | aGate_PV_cutoff_number |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def relay_switch_number_data_from_device_get_check(self, **kwargs):
        """  relay switch number data from device,the message get check-T225

        Kwargs:
            | aGate_PV_cutoff_number | int | aGate光伏被切断次数 |
            | aPbox1_cutoff_number | int | aPbox1负载侧被切断次数 |
            | aPbox2_cutoff_number | int | aPbox2负载侧被切断次数 |
            | aGate_subPV_cutoff_number | int | aGate子光伏被切断次数 |
            | grid_relay_cutoff_number | int | 市电继电器被切断次数 |
            | black_start_relay_cutoff_number | int | 黑启动继电器被切断次数 |
            | smart_load1_relay_cutoff_number | int | 智能负载1继电器被切断次数 |
            | smart_load2_relay_cutoff_number | int | 智能负载2继电器被切断次数 |
            | smart_load3_relay_cutoff_number | int | 智能负载3继电器被切断次数 |
            | generator_relay_cutoff_number | int | 发电机继电器被切断次数 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Relay Switch Number Data From Device Get Check | aGate_PV_cutoff_number=75 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.relay_switch_number_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def relay_switch_number_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T225

        Args for T225：
            | aGate_PV_cutoff_number | int | aGate光伏被切断次数 |
            | aPbox1_cutoff_number | int | aPbox1负载侧被切断次数 |
            | aPbox2_cutoff_number | int | aPbox2负载侧被切断次数 |
            | aGate_subPV_cutoff_number | int | aGate子光伏被切断次数 |
            | grid_relay_cutoff_number | int | 市电继电器被切断次数 |
            | black_start_relay_cutoff_number | int | 黑启动继电器被切断次数 |
            | smart_load1_relay_cutoff_number | int | 智能负载1继电器被切断次数 |
            | smart_load2_relay_cutoff_number | int | 智能负载2继电器被切断次数 |
            | smart_load3_relay_cutoff_number | int | 智能负载3继电器被切断次数 |
            | generator_relay_cutoff_number | int | 发电机继电器被切断次数 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 225 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=225 | rx_time_window=320 | filter_mode=and |
        | ${status} | Relay Switch Number Data From Msg Get | aGate_PV_cutoff_number | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.relay_switch_number_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def relay_switch_number_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T225

        Kwargs for T225：
            | aGate_PV_cutoff_number | int | aGate光伏被切断次数 |
            | aPbox1_cutoff_number | int | aPbox1负载侧被切断次数 |
            | aPbox2_cutoff_number | int | aPbox2负载侧被切断次数 |
            | aGate_subPV_cutoff_number | int | aGate子光伏被切断次数 |
            | grid_relay_cutoff_number | int | 市电继电器被切断次数 |
            | black_start_relay_cutoff_number | int | 黑启动继电器被切断次数 |
            | smart_load1_relay_cutoff_number | int | 智能负载1继电器被切断次数 |
            | smart_load2_relay_cutoff_number | int | 智能负载2继电器被切断次数 |
            | smart_load3_relay_cutoff_number | int | 智能负载3继电器被切断次数 |
            | generator_relay_cutoff_number | int | 发电机继电器被切断次数 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 225 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=225 | rx_time_window=320 | filter_mode=and |
        | ${status} | Relay Switch Number Data From Msg Get Check | aGate_PV_cutoff_number=5 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.relay_switch_number_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
