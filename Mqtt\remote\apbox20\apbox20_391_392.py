from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# aPbox2.0 设置和查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 391

cmd_type_c2s = 392

attr_map_s2c = {

    **attr_map_common_opt,

    'apbox20Para': ('apbox2.0_parameter', 'list'),
}
"""
'apbox20Id': ('apbox2.0_id', 'int'),
'branchType': ('branch_type', 'list'),
'mainBreakerMaxCurr': ('main_breaker_max_current', 'int')
"""

attr_map_c2s = {

    **attr_map_common_result,
    **attr_map_s2c,

    'apbox20Num': ('apbox2.0_number', 'int'),

}
"""
'apbox20Temp': ('apbox2.0_temp', 'int'),
'apbox20BusbarTemp': ('apbox2.0_bus_bar_temp', 'int'),
'apbox20Stat': ('apbox2.0_status', 'int'),

'backupL1Volt': ('backup_L1_voltage', 'int'),
'backupL2Volt': ('backup_L2_voltage', 'int'),
'backupL1L2Volt': ('backup_L1L2_voltage', 'int'),
'backupL1Curr': ('backup_L1_current', 'int'),
'backupL2Curr': ('backup_L2_current', 'int'),
"""


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class Apbox20(object):

    def apbox20_set(self, *args, **kwargs):
        """  apbox20_set-T391/392

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_parameter:apbox20Id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branchType | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:mainBreakerMaxCurr | int | 主回路断路器最大电流 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Apbox20 Set |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def apbox20_set_result_check(self, _response, **kwargs):
        """  apbox20_set result check-T392

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Apbox20 Set |
        | ${status} = | Apbox20 Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def apbox20_set_from_aws_get(self, *args, **kwargs):
        """  apbox20_set from AWS,the message get-T391

        Args：
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_parameter:apbox20Id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branchType | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:mainBreakerMaxCurr | int | 主回路断路器最大电流 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Apbox20 Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def apbox20_set_from_aws_get_check(self, **kwargs):
        """  apbox20_set from AWS,the message get check-T391

        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_parameter:apbox20Id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branchType | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:mainBreakerMaxCurr | int | 主回路断路器最大电流 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Apbox20 Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.apbox20_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apbox20_set_from_device_get(self, *args, **kwargs):
        """   apbox20_set response from device,the message get-T392

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Apbox20 Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def apbox20_set_from_device_get_check(self, **kwargs):
        """  apbox20_set response from device,the message get check-T392

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Apbox20 Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.apbox20_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apbox20_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T391/392

        Args for T391:
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_parameter:apbox20Id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branchType | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:mainBreakerMaxCurr | int | 主回路断路器最大电流 |
        Args for T392：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 391/392 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=391 | rx_time_window=300 |
        | ${status} | Apbox20 Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apbox20_set_from_device_get, cmd_type_s2c: self.apbox20_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def apbox20_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T391/392

        Kwargs for T391:
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_parameter:apbox20Id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branchType | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:mainBreakerMaxCurr | int | 主回路断路器最大电流 |
        Kwargs for T392：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | apbox2.0_parameter | list | apbox2.0参数列表,apbox20Id/branchType/mainBreakerMaxCurr |
            | apbox2.0_number | int | aPbox2.0数量 |
            | apbox2.0_parameter:apbox2.0_id | int | aPbox2.0机号，1~4 |
            | apbox2.0_parameter:branch_type | int | 支路类型，支路1：0：智能负载(默认0)，支路2：0：智能负载，1：光伏，支路3：0：智能负载，1：光伏，支路4：0：智能负载，1：光伏，2：发电机，3：V2L |
            | apbox2.0_parameter:main_breaker_max_current | int | 主回路断路器最大电流 |
            | apbox2.0_parameter:apbox2.0_temp | int | 环境温度，单位：0.1摄氏度 |
            | apbox2.0_parameter:apbox2.0_bus_bar_temp | int | 铜排温度，单位：0. 1摄氏度 |
            | apbox2.0_parameter:apbox2.0_status | int | aobx2.0状态，0初始态、1升级态、2待机态、3运行态、4故障态 |
            | apbox2.0_parameter:backup_L1_voltage | int | 备电L1 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L2_voltage | int | 备电L2 电压，单位：0.1V |
            | apbox2.0_parameter:backup_L1_current | int | 备电L1 电流，单位：0.1A |
            | apbox2.0_parameter:backup_L2_current | int | 备电L2 电流，单位：0.1A |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 391/392 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=392 | rx_time_window=300 | filter_mode=and |
        | ${status} | Apbox20 Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apbox20_set_from_device_get_check, cmd_type_s2c: self.apbox20_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
