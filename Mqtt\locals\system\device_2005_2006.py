from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 蓝牙密码校验协议

cmd_type_c2s = 2005

cmd_type_s2c = 2006

attr_map_c2s = {

    **attr_map_common_opt,

    'password': ('', 'string'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_common_opt,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalBTPassword(Base):

    def local_bluetooth_password_validate(self, *args, **kwargs):
        """  local BT password validate-T2005
        Args：
            | result | int | 结果，0:密码校验成功，1:密码校验失败 |
            | opt | int | 操作，0:密码校验 |
            | result | int | 结果，0：密码校验成功；1：密码校验失败 |
        Kwargs:
            | opt | int | 操作，0:密码校验 |
            | password | string | 蓝牙密码 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Bluetooth Password Validate | password=123456 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_payload_switch_set_result_check(self, _response, **kwargs):
        """  local BT password validate result check-T2006

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:密码校验成功，1:密码校验失败 |
            | opt | int | 操作，0:密码校验 |
            | result | int | 结果，0：密码校验成功；1：密码校验失败 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Bluetooth Password Validate | password=123456 |
        | ${status} = | Local Bluetooth Password Validate Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
