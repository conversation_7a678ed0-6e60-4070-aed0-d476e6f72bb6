from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name
from ..constant import attr_map_common_type

# 设备实时数据上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 227

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {
    **attr_map_common_type,
    'fhpSn': ('fhp_sn', 'list'),
    'gridFreq': ('grid_freq', 'float'),

    #  IBG part
    'gridVol1': ('grid_voltage_L1', 'float'),
    'gridVol2': ('grid_voltage_L2', 'float'),
    'gridCurr1': ('grid_curret_L1', 'float'),
    'gridCurr2': ('grid_curret_L2', 'float'),
    'loadCurr1': ('load_curret_L1', 'float'),
    'loadCurr2': ('load_curret_L2', 'float'),
    'dspSetFreq': ('dsp_freq', 'float'),
    'gridRelayStat': ('grid_relay_status', 'int'),
    'oilRelayStat': ('generator_relay_status', 'int'),
    'solarRelayStat': ('solar_relay_status', 'int'),
    'loadRelay1Stat': ('load_relay1_status', 'int'),
    'loadRelay2Stat': ('load_relay2_status', 'int'),
    'evRelayStat': ('ev_relay_status', 'int'),
    'loadSolarRelay1Stat': ('load_solar_relay1_status', 'int'),
    'loadSolarRelay2Stat': ('load_solar_relay2_status', 'int'),
    'ibgRunStatus': ('ibg_run_status', 'int'),
    'name': ('', 'string'),
    'electricity_type': ('', 'int'),
    'dspRunStatus': ('dsp_run_status', 'int'),
    'gridLineVol': ('grid_line_voltage', 'int'),
    'gridRelay2': ('grid_relay2_status', 'int'),
    'pvRelay2': ('pv_relay2_status', 'int'),
    'blackStartRelay': ('black_start_relay_status', 'int'),
    'genVoltage': ('generator_voltage', 'int'),
    'BFPVApboxRelay': ('bfpv_apbox_relay', 'int'),

    # PE,BMS part

    'singleHighestVolt': ('single_highest_voltage', 'float'),
    'singleLowestVolt': ('single_lowest_voltage', 'float'),
    'singleHighestTemp': ('single_highest_temp', 'float'),
    'singleLowestTemp': ('single_lowest_temp', 'float'),
    'batVolt': ('bat_voltage', 'list'),
    'batTemp': ('bat_temp', 'list'),
    'batTotalVolt': ('bat_total_voltage', 'float'),
    'batCurr': ('bat_current', 'float'),
    'batSoc': ('bat_soc', 'float'),
    'batSoh': ('bat_soh', 'float'),
    'alarmLevel': ('alarm_level', 'int'),
    'gridVol1': ('grid_voltage_L1', 'float'),
    'gridVol2': ('grid_voltage_L2', 'float'),
    'invVol1': ('inv_voltage_L1', 'float'),
    'invVol2': ('inv_voltage_L2', 'float'),
    'positiveBusVolt': ('pos_bus_voltage', 'float'),
    'negativeBusVolt': ('neg_bus_voltage', 'float'),
    'midBusVolt': ('mid_bus_voltage', 'float'),
    'invCurr1': ('inv_current_L1', 'float'),
    'invCurr2': ('inv_current_L2', 'float'),
    'buckboostCurr': ('buckboost_current', 'float'),
    'pebatVolt': ('pe_bat_voltage', 'float'),
    'runMode': ('run_mode', 'int'),
    'inverterStatus': ('inv_run_status', 'int'),
    'DCDCStatus': ('dc_dc_run_status', 'int'),
    'outCur1': ('out_current_L1', 'float'),
    'outCur2': ('out_current_L2', 'float'),
    'actPwr1': ('act_power_L1', 'int'),
    'actPwr2': ('act_power_L2', 'int'),
    'reactPwr1': ('react_power_L1', 'int'),
    'reactPwr2': ('react_power_L2', 'int'),
    'devTemp': ('device_temp', 'float'),
    'maxVolPos': ('max_single_bms_voltage_sn', 'int'),
    'minVolPos': ('min_single_bms_voltage_sn', 'int'),
    'maxTempPos': ('max_single_bms_temp_sn', 'int'),
    'minTempPos': ('min_single_bms_temp_sn', 'int'),
    'bmsState': ('bms_state', 'int'),
    'mosState': ('mos_state', 'int'),
    'switchState': ('apower_switch_state', 'int'),
    'heatState': ('bms_heat_state', 'int'),
    'fanState': ('bms_fan_state', 'int'),
    'balanState': ('bms_balance_state', 'int'),
    'gridVoltAN': ('grid_phase_a_voltage', 'float'),
    'gridVoltBN': ('grid_phase_b_voltage', 'float'),
    'solarVoltAN': ('solar_phase_a_voltage', 'float'),
    'solarVoltBN': ('solar_phase_b_voltage', 'float'),
    'gridLineVol': ('grid_line_voltage', 'float'),
    'invLineVol': ('inv_line_voltage', 'float'),
    'samBatVol': ('bat_sample_voltage', 'float'),
    'peChEnergy': ('kwh_pe_charge', 'float'),
    'peDchEnergy': ('kwh_pe_discharge', 'float'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceRealTimeDataUpload(object):

    def real_time_device_data_from_device_get(self, *args, **kwargs):
        """  real time device data upload from device,the message get-T227

        Args：
            | type | int | 1:IBG数据 |
            | fhp_sn | string list | FHP序列号 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay_status | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
            | grid_freq | float | 市电频率，单位：Hz |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Real Time Device Data From Device Get | bat_soc |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_device_data_from_device_get_check(self, **kwargs):
        """  real time device data from device,the message get check-T227

        Kwargs:
            | type | int | 1:IBG数据 |
            | fhp_sn | string list | FHP序列号 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay_status | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
            | grid_freq | float | 市电频率，单位：Hz |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Real Time Device Data From Device Get Check | bat_soc=75 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_device_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_device_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T227

        Args for T227：
            | type | int | 1:IBG数据 |
            | fhp_sn | string list | FHP序列号 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay_status | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
            | grid_freq | float | 市电频率，单位：Hz |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 227 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=227 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time Device Data From Msg Get | bat_soc | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_device_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_device_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T227

        Kwargs for T227：
            | type | int | 1:IBG数据 |
            | fhp_sn | string list | FHP序列号 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay_status | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
            | grid_freq | float | 市电频率，单位：Hz |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 227 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=227 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time Device Data From Msg Get Check | bat_soc=56 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_device_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
