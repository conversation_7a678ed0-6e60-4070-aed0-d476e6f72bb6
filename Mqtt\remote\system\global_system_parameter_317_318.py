from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 全局参数配置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 317

cmd_type_c2s = 318

attr_map_s2c = {

    'optType': ('opt', 'int'),
    'paraType': ('parameter_type', 'int'),
    'paraList': ('', 'list'),
    'num': ('', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class GlobalSystem(object):

    def global_system_parameter_set(self, *args, **kwargs):
        """  global system parameter set-T317/318

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Global System Parameter Set | opt=0 |
        | ${status} = | Global System Parameter Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def global_system_parameter_set_result_check(self, _response, **kwargs):
        """  global system parameter set result check-T318

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Global System Parameter Set | opt=1 |
        | ${status} = | Global System Parameter Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def global_system_parameter_set_from_aws_get(self, *args, **kwargs):
        """  global system parameter set from AWS,the message get-T317

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Global System Parameter Set From AWS Get | parameter_type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def global_system_parameter_set_from_aws_get_check(self, **kwargs):
        """  global system parameter set from AWS,the message get check-T318

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Global System Parameter Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.global_system_parameter_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def global_system_parameter_set_from_device_get(self, *args, **kwargs):
        """   global system parameter set response from device,the message get-T318

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Global System Parameter Set From Device Get | result | paraList |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def global_system_parameter_set_from_device_get_check(self, **kwargs):
        """  global system parameter set response from device,the message get check-T318

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Global System Parameter Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.global_system_parameter_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def global_system_parameter_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T317/318

        Args for T317:
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Args for T318：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 317/318 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=317 | rx_time_window=300 |
        | ${status} | Global System Parameter Set From Msg Get | parameter_type | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.global_system_parameter_set_from_device_get, cmd_type_s2c: self.global_system_parameter_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def global_system_parameter_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T317/318

        Kwargs for T317:
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs for T318：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | parameter_type | int | 参数类型，1：系统参数 systemPara,2：系统设备参数 systemDevicePara,3：能量管理参数 energyManagePara,4：电气铭牌参数 electricNameplatePara,5：并网合规功能参数 gridConnFuntionPara,6：通讯设置参数 commSetPara,7：AWS服务器通讯设置参数 awsCommPara,8：用户信息参数 userInforPara,9：电池管理参数 batManagePara,10：智能负载开关参数 swPara,11：电动车充电参数elecCarChargePara,12：系统配置参数 sysConfigPara,（注：5是并网合规的所有类型总体配置，501~513是5的子类 单独配置每个类型参数）,501：并网合规类型参数 compTypePara,502：允许服务模式参数 permitServicePara,503：软启动爬升参数 softStartRampPara,504：意外孤岛模式参数 UIModePara,505：穿越与保护参数 rideThroughPara,506：恒功率因素模式参数 consPowerFactPara,507：恒功率无功功率模式参数 consReactPowerPara,508：电压有功功率参数 volActPowerPara,509：有功-无功功率参数 acReacPowerPara,510：电压无功功率参数 volReactPowerPara,511：频率下降参数 freqDroopPara,512：正常功率斜坡参数 normalRampPara,513：快检使能参数 quickCheckPara |
            | paraList | list | JSON参数列表 |
            | num  | int | 参数数量，设置/查询参数的数量 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 317/318 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=318 | rx_time_window=300 | filter_mode=and |
        | ${status} | Global System Parameter Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.global_system_parameter_set_from_device_get_check, cmd_type_s2c: self.global_system_parameter_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
