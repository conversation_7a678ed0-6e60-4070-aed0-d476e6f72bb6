from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 调试模式

app_name = "debug"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 601

cmd_type_c2s = 602

common_dict = {

    'dbId': ('db_id', 'int'),
    'dbPara': ('db_parameter', 'dict'),

}
attr_map_s2c = {

    **common_dict,
    'dbEqSn': ('string', 'string'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class Debug(object):

    def debug_set(self, *args, **kwargs):
        """  debug set-T601/602

        Args：
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
        Kwargs:
            | equipment_sn | string | 分别对应IBG编号和各子设备编号,空表示所有的设备 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 调试参数 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Set |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('equipment_sn', '')

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def debug_set_result_check(self, _response, **kwargs):
        """  debug set result check-T602

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Debug Set |
        | ${status} = | Debug Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def debug_set_from_aws_get(self, *args, **kwargs):
        """  debug set from AWS,the message get-T601

        Args：
            | equipment_sn | string | 分别对应IBG编号和各子设备编号,空表示所有的设备 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 调试参数 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Set From AWS Get | debug_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def debug_set_from_aws_get_check(self, **kwargs):
        """  debug set from AWS,the message get check-T601

        Kwargs:
            | equipment_sn | string | 分别对应IBG编号和各子设备编号,空表示所有的设备 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 调试参数 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Debug Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.debug_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def debug_set_from_device_get(self, *args, **kwargs):
        """   debug set response from device,the message get-T602

        Args：
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Debug Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def debug_set_from_device_get_check(self, **kwargs):
        """  debug set response from device,the message get check-T602

        Kwargs:
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Debug Set From Device Get Check | equipment_sn= |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.debug_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def debug_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T601/602

        Args for T601:
            | equipment_sn | string | 分别对应IBG编号和各子设备编号,空表示所有的设备 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 调试参数 |
        Args for T602：
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 601/602 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=601 | rx_time_window=300 |
        | ${status} | Debug Set From Msg Get | equipment_sn | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.debug_set_from_device_get, cmd_type_s2c: self.debug_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def debug_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T601/602

        Kwargs for T601:
            | equipment_sn | string | 分别对应IBG编号和各子设备编号,空表示所有的设备 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 调试参数 |
        Kwargs for T602：
            | result | int | 结果，0:成功，1:解析失败，参数不存在 |
            | db_id | int | 云平台生成 |
            | db_parameter | dict | 返回的调试参数数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 601/602 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=602 | rx_time_window=300 | filter_mode=and |
        | ${status} | Debug Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.debug_set_from_device_get_check, cmd_type_s2c: self.debug_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
