from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from time import sleep
from robot.api import logger
from .locator import get_locator


class Smartcircuits(Base):

    def go_to_circuits_merge_page(self, select=240):
        """ Go to system->settings->smart circuitss page->circuits merge page

        Kwargs:
                | None |
        Return:
                | None |
        Examples:
                | Go To Circuits Merge Page |
        """
        locator_comm = ('acc_id', "Circuits merge")

        self.find_element(*locator_comm, click=True)

        if select == 120:

            value = "locator_smart_circuit_1"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif select == 240:

            value = "locator_smart_circuit_2"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._system_confirm_locator()

    def exit_circuits_merge_page(self):
        """ Go to system->settings->smart circuitss page->circuits merge page and go back to the smart circuitss page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Circuits Merge Page |
        """
        self._general_backward_upper_page_type2()

    # handle the situation with seekbar
    def seek_bar_handle(self, locator, direction, distance=300, duration=1000):
        value = locator
        element = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        start_x = element.location['x'] + element.size['width'] // 2
        start_y = element.location['y'] + element.size['height'] // 2

        if direction == 'up':
            end_x = start_x
            end_y = start_y - distance
        elif direction == 'down':
            end_x = start_x
            end_y = start_y + distance
        elif direction == 'left':
            end_x = start_x - distance
            end_y = start_y
        elif direction == 'right':
            end_x = start_x + distance
            end_y = start_y
        else:
            raise ValueError("direction parameter must be 'up', 'down', 'left', 'right' 之一")

        self.driver.swipe(start_x, start_y, end_x, end_y, duration)

    def config_circuits_date_setting(self, **kwargs):
        """
        Kwargs:
            | timing_Supply | bool | True/False |
            | no_plan | bool | True/False |
            | date Setting| string | Once only/Everday/Cycle interval |
            | cycle interval| int | 10 |
            | execution time| string | 09/05/2024 |
            | add time periods | int | 1/2 |
            | time_period_start_time | string | 0000 |
            | time_period_end_time | string | 1559 |

        Return:
            | None |
        Examples:
            | config circuits date Page |

        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        confirm = True

        def handle_date_setting(self, setting_locator):
            value1 = 'locator_date_setting'
            self._get_locator_and_find_element(value1, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            value2 = setting_locator
            self._get_locator_and_find_element(value2, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            self.confirm_cancel_diag_handling(value=True)

        handle_date_setting(self, setting_locator='locator_DS_Everday')

        handle_date_setting(self, setting_locator='locator_DS_Once_only')

        handle_date_setting(self, setting_locator='locator_DS_Cycle_interval')

        # config cycle interval
        # value = 'locator_cycle_interval_seekbar'
        self.set_value_in_scroll_element(value='6', locator_fun=get_locator, target_locator="locator_cycle_interval_seekbar")

        # self.seek_bar_handle(value, direction='up', distance=350)

        # self.confirm_cancel_diag_handling(value=confirm)

        # set the execution time
        value = 'locator_execution_time'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # set the month day and year of execution time
        locators = [
            'locator_exe_time_month',
            'locator_exe_time_day',
            'locator_exe_time_year',
        ]

        for value in locators:
            self.seek_bar_handle(value, direction='up', distance=100)

        self.confirm_cancel_diag_handling(value=confirm)

        # Add the time supply period1, period2
        value = 'locator_timing_supply_add'
        for _ in range(2):  # 点击两次添加供电时段
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # 删除两个供电时段 period1 和 period2
        value = 'locator_delete_time_period1'
        for _ in range(2):  # 重复删除操作
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            locator_name = "locator_delete_schedule_confirm"
            self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, retry=2, will_do_loading_detect=True, click=True)

        # 再次添加两个供电时段 period1 和 period2
        value = 'locator_timing_supply_add'
        for _ in range(2):  # 再次点击两次添加供电时段
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # config the start and the end time of supply period1 and period2
        locator_time_period = ['locator_edit_time_period1', 'locator_edit_time_period2']
        locator_start_end_time = ['locator_start_time_hour', 'locator_start_time_min', 'locator_end_time_hour', 'locator_end_time_min']

        for value in locator_time_period:
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            for item in locator_start_end_time:
                self.seek_bar_handle(item, direction='up', distance=100)
            self.confirm_cancel_diag_handling(value=confirm)

    def config_smart_circuits_page(self, **kwargs):
        """ Go to system->settings->smart circuitss page
        Kwargs:
                | guide | bool | True/False |
                | select | int | 120/240 |
                | circuit_name | string | circuit 1/circuit 2/circuit 3|
                | soc_flag | bool | true/false |
                | soc_value | int | 20 |
                | timing_Supply | bool | True/False |
                | no_plan | bool | True/False |
                | date Setting| string | Once only/Everday/Cycle interval |
                | cycle interval| int | 10 |
                | execution time| string | 09/05/2024 |
                | add time periods | int | 1/2 |
                | time_period_start_time | string | 0000 |
                | time_period_end_time | string | 1559 |
        Return:
                | None |
        Examples:
                | config smart circuits Page | select=240 | GUIDE=False | Circuit_name=circuit 1
        """
        logger.info(f"the circuit parameters:kwargs:{kwargs}")

        # the first time config smart circuits the GUIDE set False and default False
        guide = kwargs.pop('guide', None)

        if guide == 'true':
            value = 'locator_smart_citcuit_help'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            value = 'locator_help_guide'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._system_next_locator()

        select = kwargs.pop('select', 120)

        if select == '120':

            value = "locator_smart_circuit_1"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif select == '240':

            value = "locator_smart_circuit_2"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._system_next_locator()

        # edit the smart circuits name
        circuit_name = kwargs.pop('circuit_name', None)

        if circuit_name is not None:

            value = 'locator_circuits_name'
            locator = get_locator(value)

            eles = self.find_element(*locator, multiple=True)

            eles[0].click()
            eles[0].clear()
            eles[0].send_keys(circuit_name)

        self._system_next_locator()

        # the SOC threshold
        soc_flag = kwargs.pop('soc_flag', None)

        if soc_flag is not None:

            locator_name = "locator_soc_threshold"

            status = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            if status == 'false' and soc_flag == 'true':

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            # set the SOC threshold
                value = "locator_soc_threshold_bar"
                self.seek_bar_handle(value, direction='left', distance=210)

            elif status == 'true' and soc_flag == 'false':

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._system_next_locator()

        # the schedule page
        no_plan = kwargs.pop('no_plan', False)

        timing_supply = True

        if no_plan:

            timing_supply = False

        if no_plan:

            value = "locator_circuits_schedule2"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif timing_supply:

            value = "locator_circuits_schedule1"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            self._system_next_locator()
            # set supply plan
            self.config_circuits_date_setting()

        self._system_confirm_locator()

    def get_smart_circuits_parameters(self, select=240):
        """get smart circuits->Circuit 1/Circuit 3 config parameters
        Kwargs:
            | select | int | 120/240 |
        Return:
            | dict | the current and power supply result |
        Examples:
            | ${result} | Get Smart Circuits Parameters | select=120 |
        """
        ret_dict = {}

        if select == 120:
            value = "locator_120_circuits"
        elif select == 240:
            for _ in range(3):

                self.swipe_up()

            value = "locator_240_circuits"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self.display_data(ret_dict)

        logger.info(f'OK,the smart circuits result:{ret_dict}')

        if not ret_dict:

            self.save_screenshot()

            raise ValueError('Fail to get parameter !')

        return ret_dict

    def display_data(self, ret_dict):

        value = "locator_circuits_para_page"

        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        content_desc = ele.get_attribute('content-desc')

        lines = content_desc.split('\n')

        for i in range(0, len(lines), 2):

            key = lines[i]

            value = lines[i + 1] if i + 1 < len(lines) else ""

            ret_dict[key] = value

    def turn_on_off_smart_circuits(self, select=240):

        circuit1_locators = [
            "locator_circuit_switch_top",
            "locator_circuit_120_on_schedule",
            "locator_circuit_switch_top",
            "locator_circuit_120_off_schedule"
        ]

        for locator in circuit1_locators:
            self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        for _ in range(3):
            self.swipe_up()

        circuit2_locators = [
            "locator_circuit_switch_bottom",
            "locator_240_circuits_on_off_confirm",
            "locator_circuit_switch_bottom",
            "locator_240_circuits_on_off_confirm"
        ]

        for locator in circuit2_locators:
            self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
