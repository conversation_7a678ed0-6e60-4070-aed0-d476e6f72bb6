from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result, attr_map_common_type

# 退出休眠/取消充电休眠

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 385

cmd_type_c2s = 386

attr_map_s2c = {

    **attr_map_common_opt,

    **attr_map_common_type,

}

attr_map_c2s = {

    **attr_map_common_opt,

    **attr_map_common_result,

    **attr_map_common_type,

    'status': ('', 'int'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceSleepingControl(object):

    def device_sleeping_control_set(self, *args, **kwargs):
        """  device sleeping control set-T385/386

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
        Kwargs:
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Sleeping Control Set |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def device_sleeping_control_set_result_check(self, _response, **kwargs):
        """  device sleeping control set result check-T386

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Device Sleeping Control Set | opt=1 |
        | ${status} = | Device Sleeping Control Set Result Check | ${response} | result=0  | opt=1 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def device_sleeping_control_set_from_aws_get(self, *args, **kwargs):
        """  device sleeping control set from AWS,the message get-T385

        Args：
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Sleeping Control Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_sleeping_control_set_from_aws_get_check(self, **kwargs):
        """  device sleeping control set from AWS,the message get check-T385

        Kwargs:
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Device Sleeping Control Set From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_sleeping_control_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_sleeping_control_set_from_device_get(self, *args, **kwargs):
        """   device sleeping control set response from device,the message get-T386

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Sleeping Control Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_sleeping_control_set_from_device_get_check(self, **kwargs):
        """  device sleeping control set response from device,the message get check-T386

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Device Sleeping Control Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_sleeping_control_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_sleeping_control_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T385/386

        Args for T385:
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
        Args for T386：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 323/324 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=385 | rx_time_window=300 |
        | ${status} | Device Sleeping Control Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_sleeping_control_set_from_device_get, cmd_type_s2c: self.device_sleeping_control_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_sleeping_control_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T385/386

        Kwargs for T385:
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
        Kwargs for T386：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询(待定，未实现)，1:设置 |
            | type | int | 休眠类型，1:设置充电到xx再进入休眠模式；2： 设置立刻休眠 |
            | status | int | 休眠状态，0：未休眠；1：休眠中；2：充电中待休眠 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 385/386 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=386 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Sleeping Control Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_sleeping_control_set_from_device_get_check, cmd_type_s2c: self.device_sleeping_control_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
