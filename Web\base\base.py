import random
import base64

from time import (sleep, time, strftime, localtime)
from robot.api import logger

from selenium import webdriver
from selenium.webdriver import ChromeOptions, FirefoxOptions
from selenium.webdriver.chrome.service import Service

from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support.select import Select

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains


from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager

from .elementfinder import ElementFinder
from .location import Location

from ..util import log_as_html


class Base(Location):

    def __init__(self):

        self._element_finder = ElementFinder(self)

    def find_element2(self, locator, parent=None):

        return self._element_finder.find(locator, parent=parent)

    def find_elements(self, locator, parent=None):

        return self._element_finder.find(locator, first_ony=False, required=False, parent=parent)

    def open(self, url="https://test.franklinwh.com/login", browser='Chrome', browser_mode=None, screenshot_path='./', implicitly_wait=8, executable_path=None, **kwargs):
        """ Open URL

        Kwargs:
            | url |  string | URL,https://test.franklinwh.com/login(默认) |
            | browser | string | Chrome(默认)/Firefox |
            | browser_mode | string | 浏览器模式,headless/None(默认） |
            | screenshot_path | string | './'（默认),截图文件的保存路径， |
            | debug | bool | true/false(默认),库是否开调试功能 |
            | implicitly_wait_time | float | 全局隐式等待时间，默认为8秒 |
            | executable_path | string | WEB driver文件的路径 |
        Return:
            | object | 连接对象 |
        Examples:
            | ${app} | open | http://www.123.com | screenshot_path=D:/ |
        """
        logger.info(f"the user kwargs is:url:{url},browser:{browser},browser_mode:{browser_mode},"
                    f"screenshot_path:{screenshot_path},executable_path:{executable_path},implicitly_wait:{implicitly_wait},kwargs:{kwargs}")

        self.implicitly_wait_time = implicitly_wait

        self.screenshot_path = screenshot_path

        options = None

        if browser == 'Chrome':

            options = ChromeOptions()

            try:

                service = Service(ChromeDriverManager().install())

            except Exception as e:

                service = None

            options.add_experimental_option('excludeSwitches', ['enable-automation'])

            if browser_mode == 'headless':

                options.add_argument("--headless")

            if executable_path is None:

                _dict = dict(options=options)

                if service is not None:

                    _dict.update(service=service)

            else:

                _dict = dict(service=ChromeService(executable_path))

                if service is not None:

                    _dict.update(options=options)

            self.driver = webdriver.Chrome(**_dict)

        elif browser == 'Firefox':

            options = FirefoxOptions()

            if browser_mode == 'headless':

                options.add_argument("--headless")

            if executable_path is None:

                self.driver = webdriver.Firefox(options=options)

        options.add_argument('--ignore-certificate-errors')

        self.driver.implicitly_wait(float(implicitly_wait))

        if url is not None:

            self.driver.get(url=url)

        return self

    def find(self, locator, method='By.NAME'):

        return self.driver.find_element(By.NAME, locator)

    def find_by_ID(self, locator):

        return self.driver.find_element(By.ID, locator)

    def find_by_CLASS_NAMEs(self, locator):

        return self.driver.find_elements(By.CLASS_NAME, locator)

    def find_by_CLASS_NAME(self, locator):

        return self.driver.find_element(By.CLASS_NAME, locator)

    def find_by_CSS(self, locator):

        return self.driver.find_element(By.CSS_SELECTOR, locator)

    def find_by_TAG(self, locator):

        return self.driver.find_element(By.TAG_NAME, locator)

    def find_by_TAGs(self, locator):

        return self.driver.find_elements(By.TAG_NAME, locator)

    def find_by_Xpath(self, locator):

        return self.driver.find_element(By.XPATH, locator)

    def find_by_Link_Text(self, locator):

        return self.driver.find_element(By.LINK_TEXT, locator).is_displayed()

    def fill(self, locator, text):
        try:
            locator.send_keys(text)

        except Exception as e:

            raise e

    def highlight(self, element):

        pass

    def ele_exists(self, locator):
        ele = self.find(locator)

        return True if ele else False

    def get_page_source(self):
        """ for debug, element location
        """
        return self.driver.page_source

    def capture_png(self):
        ts = strftime("%Y%m%d%H%M%S", localtime(time.time()))
        self.driver.save_screenshot(f"{ts}{random.randint(0, 1000)}.png")

    def click(self, locator):
        try:
            locator.click()
        except Exception as e:
            raise e

    def submit(self, locator):
        try:
            locator.submit()
        except Exception as e:
            raise e

    def clear(self, locator):
        try:
            locator.clear()
        except Exception as e:
            raise e

    def refresh(self):
        try:
            self.driver.refresh()
        except Exception as e:
            raise e

    def get_current_url(self, locator):
        try:
            url = self.driver.current_url
        except Exception as e:
            raise e

        return url

    def get_current_title(self, locator):
        try:
            title = self.driver.title
        except Exception as e:
            raise e

        return title

    def move_to_element(self, element):

        action = ActionChains(self.driver)

        action.move_to_element(element).click().perform()

    def execute_script(self, js, target=None):
        try:

            if target is None:

                self.driver.execute_script(js)

            else:

                self.driver.execute_script(js, target)

        except Exception as e:
            raise e

    def switch_to_window(self, target):
        try:
            handle = self.driver.window_handles

            self.driver.switch_to.window(handles[-1])
        except Exception as e:
            raise e

    def quit(self):
        self.driver.quit()

    def save_screenshot(self, file_path=None, file_name=None, element=None, picture_format='base64'):
        """ Save screenshot

        Kwargs:
            | file_path |  string | None(by default),将用当前系统保存的screenshot_path变量作为保存文件路径,如果screenshot_path变量未设置,则默认为'./' |
            | file_name | string | None(by default),将用当前系统时间生成默认的文件名 |
            | element | element object | None(by default), 对给定元素的图像进行保存|
            | picture_format | string | based64(by default)/png/file |
        Return:
            | string | 当picture_format 是 'png'或'base64' |
        Examples:
            | save_screenshot |
            | ${string} | save_screenshot | file_name=aaa.png |
            | ${ele} | find_element | acc_id | New |
            | save_screenshot | file_path=D:/  |
            | save_screenshot | file_path=D:/  | file_name=bbb.png |
            | save_screenshot | file_path=D:/  | file_name=new.png | element=${ele} |
            | save_screenshot | file_name=aaa.png  | picture_format=file |
            | ${file} | save_screenshot | picture_format=png |
        """
        logger.info(f'the user kwargs in save_screenshot,file_path:{file_path},file_name:{file_name},'
                    f'element:{element},picture_format:{picture_format}'
                    )

        if file_name is None:

            cur_time = strftime("%Y_%m_%d_%H_%M_%S", localtime(time()))

            _file_name = f'{cur_time}.png'

        else:
            _file_name = file_name

        if file_path is None:

            _file_path = self.screenshot_path + _file_name

        else:
            _file_path = file_path + _file_name

        logger.info(f'the screenshot is saved at {_file_path}')

        if element is not None:

            element.screenshot(_file_path)

            return _file_path

        else:

            if picture_format == 'base64':

                _string = self.driver.get_screenshot_as_base64()  # base64 string

                log_as_html(f'</td></tr><tr><td colspan="3">'
                            f'<img src="data:image/png;base64, {_string}" width="800px">')

                _file = base64.b64decode(_string)

                with open(_file_path, mode="wb") as f:

                    f.write(_file)

                return _string

            elif picture_format == 'png':

                _string = self.driver.get_screenshot_as_png()  # binary file

                with open(_file_path, mode="wb") as f:

                    f.write(_string)

                return _string

            else:

                self.driver.save_screenshot(_file_path)


"""

ip='https://test.franklinwh.com'

version_type='系统版本'
software_version='ibg_ud_V10R10B09D00_ems_0511_v1.zip'
#software_version='V10R10B08D00_ems_0516_v1.zip'


options=ChromeOptions()
options.add_argument('--ignore-certificate-errors')
options.add_experimental_option('excludeSwitches',['enable-automation'])

driver=webdriver.Chrome(options=options)
driver.maximize_window()
driver.implicitly_wait(8)

client=Core(driver)

client.driver.get(url=ip)
print(client.driver.title)

elements=client.find_by_CLASS_NAMEs("el-input__inner")
print(elements)

user_name,password=elements[0:2]
user_name.send_keys('SuperAdmin')
user_name.click()

password.send_keys('1234567890T')
password.click()


login_bt=client.find_by_CLASS_NAME("login-btn")

login_bt.click()


ele=client.find_by_CLASS_NAME('mon-icon-gujian')
ele.click()
sleep(2)

m=client.find_by_CLASS_NAMEs('el-menu-item')

for i in m:

    firm_list =['固件升级']

    if i.text in firm_list:
        print("text",i.text)
        i.click()
        sleep(3)


ele=client.find_by_Xpath("//input[contains(@placeholder,'aGate序列号、安装商公司、终端用户...')]")
ele.send_keys('Sam')

ele.click()


ele=client.find_by_Xpath("//span[contains(.,'搜索')]")
ele.click()
sleep(3)


ele=client.find_by_CLASS_NAME('el-checkbox__inner')
ele.click()
sleep(2)

ele=client.find_by_Xpath("//button[@class='btn']")

ele.click()

sleep(3)

ele=client.find_by_Xpath("//div//input[starts-with(@id,'el-id-')]")

ele_sum=ele.find_elements(By.XPATH,"//div//input[starts-with(@id,'el-id-')]")

ele=ele_sum[-2]

ele.click()


print(f"//span[contains(.,'{version_type}')]")
ele=client.find_by_Xpath(f"//span[contains(.,'{version_type}')]")

ele.click()

sleep(3)


ele=ele_sum[-1]

ele.click()

ele=client.find_by_Xpath(f"//li//span[contains(.,'{software_version}')]")

sleep(3)

ele.click()

sleep(3)


ele=client.find_by_CLASS_NAME('footer-box')
eles = ele.find_elements(By.XPATH,"//button")
ele=eles[-1]

#<span class="">确定</span>

#ele=eles[-2]

ele.click()
sleep(3)



m=client.find_by_CLASS_NAMEs('el-menu-item')

for i in m:

    firm_list =['固件升级']

    if i.text in firm_list:
        print("text",i.text)
        i.click()
        sleep(3)

ele=client.find_by_Xpath("//div[@id='tab-2']")

ele.click()

sleep(3)

ele=client.find_by_Xpath("//input[contains(@placeholder,'aGate序列号')]")
ele.send_keys('Sam')

ele.click()


ele=client.find_by_Xpath("//span[contains(.,'搜索')]")
ele.click()

sleep(3)

eles=client.find_by_CLASS_NAME('el-checkbox__inner')
print("OK5",ele)


m=eles.find_elements(By.XPATH,"//label")
m[1].click()

m=client.find_by_CLASS_NAMEs("handleBtn")
m[1].click()

"""
