from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备登录基本信息获取

cmd_type_c2s = 1101

cmd_type_s2c = 1102

attr_map_c2s = {

    **attr_map_common_opt,

    'minProtocolVer': ('min_protocol_version', 'string'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_common_opt,

    'IBG_SN': ('device', 'string'),
    'deviceType': ('device_type', 'int'),
    'protocolVer': ('protocol_version', 'string'),

    'IBG_VER': ('', 'string'),
    'MAIN_VER': ('', 'string'),    # newly added
    'AWS_VER': ('', 'string'),
    'SNVA_VER': ('', 'string'),    # newly added
    'APP_VER': ('', 'string'),
    'SL_VER': ('', 'string'),

    'FHP_NUM': ('', 'int'),
    'FHP_SN': ('', 'list'),
    'PE_SN': ('', 'list'),

    'FPGA_VER': ('', 'list'),
    'DCDC_VER': ('', 'list'),

    'INV_VER': ('', 'list'),

    'BMS_SN': ('', 'list'),
    'BMS_VER': ('', 'list'),

    'BL_VER': ('', 'list'),

    'TH_VER': ('', 'list'),

    'METER_VER': ('', 'string'),
    'SyHdVersion': ('system_hw_version', 'int'),  # different
    'MB_VER': ('modbus_version', 'string'),  # different

    'PE_HW_VER': ('', 'list'),    # newly added
    'BMS_MAIN_HW_VER': ('', 'list'),    # newly added

    'BMS_BL_HW_VER': ('', 'list'),    # newly added
    'BMS_TH_HW_VER': ('', 'list'),    # newly added
    'MB_VER': ('', 'int'),    # newly added

    'mpptAppVer': ('', 'list'),    # newly added
    'mpptBootVer': ('', 'list'),    # newly added
    'mpptAppBakVer': ('', 'list'),    # newly added
    'mpptBootBakVer': ('', 'list'),    # newly added
    'mpptAppBakSta': ('', 'list'),    # newly added
    'mpptBootBakSta': ('', 'list'),    # newly added

    'apbox20Num': ('', 'list'),    # newly added
    'apbox20Sn': ('', 'list'),    # newly added
    'apbox20HardwareVer': ('', 'list'),    # newly added
    'apbox20AppVer': ('', 'list'),    # newly added
    'apbox20BootVer': ('', 'list'),    # newly added
    'apbox20AppBakVer': ('', 'list'),    # newly added
    'apbox20BootBaktVer': ('', 'list'),    # newly added
    'apbox20AppBakSta': ('', 'list'),    # newly added
    'apbox20BootBakSta': ('', 'list'),    # newly added
    'msaModel': ('', 'int'),    # newly added

}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalLogin(Base):

    def local_login(self, *args, **kwargs):
        """  local device login-T1101/1102
        Args：
            | opt |  int | 0:查询,1:设置  |
            | result |  int | 0:成功,1：失败  |
            | reason |  int | 0：操作成功；1：获取信息失败；2：协议版本过低  |
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | system_hw_version | int | 100:1.0;101:1.1;102:1.2;103:1.3;104:1.4,默认0 |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |  # newly added? |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | int | Modbus软件版本号 |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs:
            | opt |  int | 0:查询,1:设置  |
            | min_protocol_version | 最小版本，默认为V1.10.01 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Login | device | protocol_version | device_version |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.setdefault('min_protocol_version', 'V1.10.01')

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_login_check(self, **kwargs):
        """  local device login check-T1101/1102
        Kwargs:
            | opt |  int | 0:查询,1:设置  |
            | result |  int | 0:成功,1：失败  |
            | reason |  int | 0：操作成功；1：获取信息失败；2：协议版本过低  |
            | device |  string | <=32Bytes, 设备序列号  |
            | device_type | int | 0:设备网关 |
            | protocol_version | string | x.y.z,<=32Bytes,协议版本号 |
            | system_hw_version | int | 100:1.0;101:1.1;102:1.2;103:1.3;104:1.4,默认0 |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |  # newly added? |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | int | Modbus软件版本号 |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${status} = | Local Login Get Check | opt=0 | protocol_version=V1.10.01 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.local_login

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)
