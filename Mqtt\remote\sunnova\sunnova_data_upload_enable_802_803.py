from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result, attr_map_common_opt

# sunnova数据上传使能

app_name = "sunnova"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 803

cmd_type_s2c = 802

attr_map_s2c = {

    **attr_map_common_opt,

    'sunnovaEn': ('sunnova_enabled', 'int'),
    'sunnovaPeriod': ('sunnova_period', 'int'),
    'sunnovaDurationTime': ('sunnova_duration', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SunnovaUploadEnable(object):

    def sunnova_data_upload_enabled(self, *args, **kwargs):
        """  sunnova data upload enabled-T802/803

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败,2：周期设置不合法（0-86400），3：持续时间设置不合法（0-86400） |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunnova_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，单位：s |
            | sunnova_duration | int | 数据持续上传时间，单位：s |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunnova Data Upload Enabled | sunnova_duration | sunnova_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def sunnova_data_upload_from_aws_get(self, *args, **kwargs):
        """  sunnova data upload from AWS,the message get-T802

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，单位：s |
            | sunnova_duration | int | 数据持续上传时间，单位：s |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunnova Data Upload From AWS Get | sunnova_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def sunnova_data_upload_from_aws_get_check(self, **kwargs):
        """  sunnova data upload from AWS,the message get check-T802

        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，单位：s |
            | sunnova_duration | int | 数据持续上传时间，单位：s |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Sunnova Data Upload From AWS Get Check | sunnova_enabled=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunnova_data_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunnova_data_upload_from_device_get(self, *args, **kwargs):
        """  sunnova data upload from device,the message get-T803

        Args：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败,2：周期设置不合法（0-86400），3：持续时间设置不合法（0-86400） |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunnova_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sunnova Data Upload From Device Get | sunnova_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def sunnova_data_upload_from_device_get_check(self, **kwargs):
        """  sunnova data upload from device,the message get check-T803

        Kwargs:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败,2：周期设置不合法（0-86400），3：持续时间设置不合法（0-86400） |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunnova_duration | int | 数据持续上传时间，60s（默认），单位：s |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  sunnova Data Upload From Device Get Check | sunnova_enabled=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunnova_data_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunnova_data_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T802/803

        Args for T802:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，单位：s |
            | sunnova_duration | int | 数据持续上传时间，单位：s |
        Args for T803：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败,2：周期设置不合法（0-86400），3：持续时间设置不合法（0-86400） |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunnova_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 802/803 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=802 | rx_time_window=300 |
        | ${status} | Sunnova Data Upload From Msg Get | sunnova_enabled | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunnova_data_upload_from_device_get, cmd_type_s2c: self.sunnova_data_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def sunnova_data_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T802/803

        Kwargs for T802:
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，单位：s |
            | sunnova_duration | int | 数据持续上传时间，单位：s |
        Kwargs for T803：
            | opt |  int | 命令类型， 0:查询，1：设置 |
            | result | int | 结果，0：成功，1：失败,2：周期设置不合法（0-86400），3：持续时间设置不合法（0-86400） |
            | sunnova_enabled | list | 数据上传使能，0：不使能，1：使能 |
            | sunnova_period | int | 数据上传周期，300s（默认，单位：s） |
            | sunnova_duration | int | 数据持续上传时间，60s（默认），单位：s |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 802/803 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=803 | rx_time_window=300 | filter_mode=and |
        | ${status} | Sunnova Data Upload From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunnova_data_upload_from_device_get_check, cmd_type_s2c: self.sunnova_data_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
