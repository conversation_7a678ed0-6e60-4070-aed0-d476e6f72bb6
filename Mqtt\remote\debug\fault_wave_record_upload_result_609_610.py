from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 故障录波记录文件上传的结果通知

app_name = "debug"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 610

cmd_type_c2s = 609

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    **attr_map_common_result,

    'subDir': ('sub_directory', 'list'),
    'fileName': ('file_name', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class FaultWaveRecordUploadResult(object):

    def fault_wave_record_upload_result_from_aws_get(self, *args, **kwargs):
        """  fault wave record upload result from AWS,the message get-T610

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Upload Result From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_upload_result_from_aws_get_check(self, **kwargs):
        """  fault wave record upload result from AWS,the message get check-T610

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Fault Wave Record Upload Result From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_upload_result_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_upload_result_from_device_get(self, *args, **kwargs):
        """   fault wave record upload result from device,the message get-T609

        Args:
            | result | int | 结果，0：上传成功；1：上传失败；2：连接失败；3：url解析失败；4文件不存在 |
            | file_name | string | 文件名，<=64Bytes |
            | sign | string | 文件MD5签名，32Bytes|
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Upload Result From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_upload_result_from_device_get_check(self, **kwargs):
        """  fault wave record upload result from device,the message get check-T609

        Kwargs:
            | result | int | 结果，0:成功；-1:文件不存在；-2:文件上传失败;-3:文件传输过程失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sub_directory | string list | 子目录文件名 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Fault Wave Record Upload Result From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_upload_result_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_upload_result_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T609/610

        Kwargs for T609：
            | result | int | 结果，0:成功；-1:文件不存在；-2:文件上传失败;-3:文件传输过程失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sub_directory | string list | 子目录文件名 |
        Kwargs for T610:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 610/609 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=609 | rx_time_window=300 |
        | ${status} | Fault Wave Record Upload Result From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_upload_result_from_device_get, cmd_type_s2c: self.fault_wave_record_upload_result_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def fault_wave_record_upload_result_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T609/610

        Kwargs for T609：
            | result | int | 结果，0:成功；-1:文件不存在；-2:文件上传失败;-3:文件传输过程失败 |
            | file_name | string | 文件名，<=64Bytes |
            | sub_directory | string list | 子目录文件名 |
        Kwargs for T610:
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 610/609 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=610 | rx_time_window=300 | filter_mode=and |
        | ${status} | Fault Wave Record Upload Result From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_upload_result_from_device_get_check, cmd_type_s2c: self.fault_wave_record_upload_result_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
