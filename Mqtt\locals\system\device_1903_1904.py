from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 光伏参数

cmd_type_c2s = 1903

cmd_type_s2c = 1904

attr_map_c2s = {

    **attr_map_common_opt,

    'remoteSolarEn': ('remote_pv_enabled', 'int'),
    'remoteSolarMode': ('remote_pv_mode', 'int'),
    'solarRatedPower': ('remote_pv_rated_power', 'int'),
    'installProximalsolar': ('near_pv_installed', 'int'),

    'installPV1port': ('install_pv1_port', 'int'),
    'installPV2port': ('install_pv2_port', 'int'),

    'PV1RatedPower': ('pv1_rated_power', 'int'),
    'PV2RatedPower': ('pv2_rated_power', 'int'),
    'loadSolarAmount': ('load_side_pv_number', 'int'),
    'loadSolar1RatedPower': ('load_side_pv1_rated_power', 'int'),
    'loadSolar2RatedPower': ('load_side_pv2_rated_power', 'int'),

    'mainsSolarRatedPower': ('grid_side_pv_rated_power', 'int'),
    'beforePVMeterRatedPower': ('before_meter_pv_rated_power', 'int'),
    'NEMPVRatedPower': ('NEM_pv_rated_power', 'int'),
    'protectTime': ('protect_time', 'int'),


    'reSolarSoc': ('recover_pv_soc', 'int'),

    'solarPower': ('total_pv_power', 'int'),
    'solarRelayStat': ('near_pv_relay_status', 'int'),
    'loadRelay1Stat': ('load_side_pv1_relay_status', 'int'),
    'loadRelay2Stat': ('load_side_pv2_relay_status', 'int'),

    'solarPowerGen': ('pv_generated_energy', 'int'),
    'grid_feed_max': ('grid_max_feed', 'int'),

    'DSPNEMPVEnb': ('NEM_pv_dsp_meter_enabled', 'int'),
    'DSPNEMPVRatePwr': ('NEM_pv_with_dsp_meter_rated_power', 'int'),
    'aGatePvExportEn': ('agate_pv_export_enabled', 'int'),

    'loadSideApboxFeedEn': ('load_side_apbox_feed_enabled', 'int'),
    'maxExportPower': ('max_export_power', 'int'),
    'pvSplitCtEn': ('pv_split_ct_enabled', 'int'),

    'pvSplitCtRatePwr': ('pv_split_ct_rated_power', 'int'),
    'gridSplitCtEn': ('grid_split_ct_enabled', 'int'),
    'splitCtGridPv': ('split_ct_grid_pv_enabled', 'int'),


    'threePhPvEnb': ('three_phase_pv_enabled', 'int'),
    'mpptInstallSta': ('mppt_installed_status', 'list'),

    'mPanPv1En': ('main_panel_pv1_enabled', 'int'),
    'mPanPv1RatedPower': ('main_panel_pv1_rated_power', 'int'),

    'mPanPv2En': ('main_panel_pv1_enabled', 'int'),
    'mPanPv2RatedPower': ('main_panel_pv2_rated_power', 'int'),

    'apbox20Pv': ('apbox2.0_pv_parameters', 'list'),
    'mpptPara': ('mppt_parameters', 'list'),

    'BSRelyEn': ('black_start_relay_enabled', 'int'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalPv(Base):

    def local_pv_set(self, *args, **kwargs):
        """  pv set-T1903
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | black_start_relay_enabled | int | 黑启动继电器使能，0：未安装，1：安装 |
        Kwargs:
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | black_start_relay_enabled | int | 黑启动继电器使能，0：未安装，1：安装 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Pv Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_pv_set_result_check(self, _response, **kwargs):
        """  pv set result check-T1904

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | remote_pv_enabled | int | 远程光伏接入使能，0：不使能，1：使能 |
            | remote_pv_mode | int | 远程光伏接入方式，0：未设置；1：负载侧；2：电网侧; 3:分路接入：4.NEM+光伏 |
            | remote_pv_rated_power | int | 近端光伏额定功率， 单位，0.1kw |
            | near_pv_installed | int | 是否安装近端光伏， 0:未安装；1：安装 |
            | install_pv1_port | int | 安装PV1端口，0:未安装；1：安装 |
            | install_pv2_port | int | 安装PV2端口，0:未安装；1：安装 |
            | pv1_rated_power | int | PV1额定功率， 单位：0.1kw |
            | pv2_rated_power | int | PV2额定功率， 单位：0.1kw |
            | load_side_pv_number | int | 负载侧光伏数量 |
            | load_side_pv1_rated_power | int | 负载侧光伏1额定功率， 单位：0.1kw |
            | load_side_pv2_rated_power | int | 负载侧光伏2额定功率， 单位：0.1kw |
            | grid_side_pv_rated_power | int | 市电侧光伏额定功率， 单位：0.1kw |
            | before_meter_pv_rated_power | int | 分路接入光伏额定功率， 单位：0.1kw |
            | NEM_pv_rated_power | int | NEM+光伏额定功率， 单位：0.1kw |
            | protect_time | int | 保护时长，单位：分钟，10-1000 |
            | recover_pv_soc | int | 恢复光伏电量点，单位：% |
            | total_pv_power | int | 光伏总输出功率， 单位：0.1kw |
            | near_pv_relay_status | int | 近端光伏继电器状态，0：断开，1：闭合 |
            | load_side_pv1_relay_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_side_pv2_relay_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | pv_generated_energy | int | 光伏当日总发电量，单位：0.1kwh |
            | grid_max_feed | int | 是否允许电网的最大馈网功率，0：不允许，-1：不限制 |
            | NEM_pv_dsp_meter_enabled | int | NEM光伏使用DSP内置电表使能，0：不使能，1：使能 |
            | NEM_pv_with_dsp_meter_rated_power | int | 使用DSP内置电表的NEM光伏额定功率，单位：0.1kw |
            | agate_pv_export_enabled | int | 是否允许光伏馈网，0：关闭，1：允许 |
            | load_side_apbox_feed_enabled | int | 是否允许abox馈网，0：关闭，1：允许 |
            | max_export_power | int | 系统最大的可馈网功率，单位：w，-1：不限制 |
            | pv_split_ct_enabled | int | 光伏电表splitCT使能，0：未安装，1：安装 |
            | pv_split_ct_rated_power | int | 超配光伏额定功率，单位：w |
            | grid_split_ct_enabled | int | 市电电表splitCT使能，0：未安装，1：安装 |
            | split_ct_grid_pv_enabled | int | 经过市电电表splitCT的市电侧光伏使能，0：未安装，1：NEM+光伏 |
            | three_phase_pv_enabled | int | 三相光伏使能，0：未使能，1：使能 |
            | mppt_installed_status | list | 是否安装MPPT光伏，0：未安装，1：安装 |
            | main_panel_pv1_enabled | int | 主配电盘光伏1使能，0：不使能，1：使能 |
            | main_panel_pv1_rated_power | int | 主配电盘光伏1额定功率，单位：kw |
            | main_panel_pv1_enabled | int | 主配电盘光伏2使能，0：不使能，1：使能 |
            | main_panel_pv2_rated_power | int | 主配电盘光伏2额定功率，单位：kw |
            | apbox2.0_pv_parameters | list | apbox2.0光伏参数 |
            | mppt_parameters | list | MPPT设备参数 |
            | black_start_relay_enabled | int | 黑启动继电器使能，0：未安装，1：安装 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Pv Set |
        | ${status} = | Local Pv Set Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
