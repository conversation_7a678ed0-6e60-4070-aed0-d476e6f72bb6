from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 自动编址

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 329

cmd_type_c2s = 330

attr_map_s2c = {

    'devNum': ('device_number', 'int'),
    'snMap': ('SN_map', 'list'),

}

attr_map_c2s = {

    **attr_map_common_result,

    'reason': ('', 'int'),

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AutoAdressing(object):

    def auto_addressing_set(self, *args, **kwargs):
        """  auto addressing set-T329/330

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Kwargs:
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Auto Addressing Set | device_number=0 |
        | ${status} = | Auto Addressing Set | result | reason | device_number=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('device_number', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def auto_addressing_set_result_check(self, _response, **kwargs):
        """  auto addressing set result check-T330

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Auto Addressing Set | device_number=0 |
        | ${status} = | Auto Addressing Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def auto_addressing_set_from_aws_get(self, *args, **kwargs):
        """  auto addressing set from AWS,the message get-T329

        Args：
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Auto Addressing Set From AWS Get | device_number |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def auto_addressing_set_from_aws_get_check(self, **kwargs):
        """  auto addressing set from AWS,the message get check-T329

        Kwargs:
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Auto Addressing Set From AWS Get Check | device_number=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.auto_addressing_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def auto_addressing_set_from_device_get(self, *args, **kwargs):
        """   auto addressing set response from device,the message get-T330

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Auto Addressing Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def auto_addressing_set_from_device_get_check(self, **kwargs):
        """  auto addressing set response from device,the message get check-T330

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Auto Addressing Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.auto_addressing_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def auto_addressing_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T329/330

        Args for T329:
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Args for T330：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 329/330 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=329 | rx_time_window=300 |
        | ${status} | Auto Addressing Set From Msg Get | device_number | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.auto_addressing_set_from_device_get, cmd_type_s2c: self.auto_addressing_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def auto_addressing_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T329/330

        Kwargs for T329:
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Kwargs for T330：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 最大aPower设备数量,最大15，0:自动编制 |
            | SN_map | list |  PE和BMS SN映射表 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 329/330 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=330 | rx_time_window=300 | filter_mode=and |
        | ${status} | Auto Addressing Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.auto_addressing_set_from_device_get_check, cmd_type_s2c: self.auto_addressing_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
