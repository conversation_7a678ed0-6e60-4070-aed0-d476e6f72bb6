import time
from influxdb_client import InfluxD<PERSON><PERSON>, Point
from robot.api import logger
import warnings
from .version import VERSION

warnings.filterwarnings("ignore")

TOKEN="QeMskmN3wfBHv26qFTLdMPBC-Eg1mypEjIHJd_IQRRvk0QPJuom1H9FWn28X8DMrNIzLiPwRUACcDD50ZbbuoQ=="

ORG ="backend"

BUCKET="iot-data-all"

class InfluxDB():

    ROBOT_LIBRARY_SCOPE = 'SUITE'

    ROBOT_LIBRARY_VERSION = VERSION

    def connect_to_influxDB(self, url = "https://alb.franklinwh.click:8086",org=ORG, token=TOKEN):

        self.client = InfluxDBClient(url=url, token=token, org=org, verify_ssl=False)
        logger.info('OK,successfully connected to influxDB...')

        self.query_api = self.client.query_api()

        health= self.client.health()
        
        logger.info(f'the influxDB health status:\n{health}')

    def query(self, filter_time='5s', query_statement=None, **kwargs):

        logger.info(f'the query parameters: filter_time:{filter_time},query_statement:{query_statement},kwargs:{kwargs}')

        if query_statement is not None:

            query = query_statement

        else:
            
            query = f"""from(bucket: "{BUCKET}")
              |> range(start: -{filter_time})"""

            logger.info(f'fecth the available records of tables in the last {filter_time}...')

        tables = self.query_api.query(query, org=ORG)

        logger.info(f'OK, got the table number:{len(tables)}')

        return tables

    def retrieve_query_result(self, result,filter_sn=None):

        ret = []
        
        for j in result:
          
          for i in j.records:

            value = i['_value']
            
            if value.startswith('{'):
                
              _entry = f"""timestamp:{i.get_time()},aGate:{i['gateway_id']},cmdType:{i['cmd_type']},value:{i['_value']}"""

              logger.info(_entry)
              
              if filter_sn is None:

                  ret.append(_entry)

              else:
                  if i['gateway_id'] == filter_sn:
                      
                      ret.append(_entry)

        return ret              

    def disconnect_influxDB(self):
        
        self.client.close()

