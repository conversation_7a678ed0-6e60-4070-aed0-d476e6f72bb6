from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_type_result

# 设备日志召唤

app_name = "debug"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 604

cmd_type_s2c = 603

common_dict = {

    'fileName': ('file_name', 'string'),

}

attr_map_s2c = {

    'logType': ('log_type', 'int'),
    'logEqSn': ('equipment_sn', 'string'),
    'url': ('', 'string'),
    'alarmId': ('alarm_id', 'int'),
    'logTime': ('log_time', 'string'),

    **common_dict,

}

attr_map_c2s = {

    **attr_map_common_type_result,

    **common_dict,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceLogUpload(object):

    def device_log_upload_set(self, *args, **kwargs):
        """  device log upload set-T603/T604

        Args:
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | log_type | int | 日志类型，1：PE故障录波；2：BMS事件日志；3：BMS快照记录；4：IBG运行日志；5：CAN报文；6：通信日志；7、电池日志；8：通信状态日志；9：设备系统资源监控日志；10：4G日志；11：AWS日志；12：主进程日志；13：分时段电量统计日志；14：日电量统计日志 15：电芯数据上传（不能召唤，隔天自动上传） 16:2030运行日志； 17：内核运行日志；18：sunspecmodbus日志；19:告警处理日志；20:文件传输日志；21：PID/VID日志; 22:EMS升级日志；23:串口数据日志；24：智能负载日志；25：电表日志；26：MCU升级日志；27：看门狗日志；28：其他日志；29：nsc所有日志，19-29仅NSC支持，支持；30：sunnova日志 |
            | equipment_sn | string | 设备编号，对应IBG编号和各子设备编号；故障录波需要对应告警设备编号，2/3 BMS类日志需要对应BMS设备编号，4/5/6对应IBG设备编号。<=32Bytes |
            | url | string | 文件路径，<=128Bytes |
            | alarm_id | int | 用于确定日志文件,对应于cmdType 207中的alarm_id, 0：不需要ID，适用于equipment_sn=4/5/6 |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Log Upload Set | log_type=4 | url=http://abc.com | file_name=123 | equipment_sn=ULG_Test | log_time=2024-12-26 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('log_type', 4)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def device_log_upload_set_result_check(self, _response, **kwargs):
        """  device log upload set result check-T604

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Device Log Upload Set | log_type=4 | url=http://abc.com | file_name=123 | equipment_sn=ULG_Test | log_time=2024-12-26 |
        | ${status} = | Device Log Upload Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def device_log_upload_set_from_aws_get(self, *args, **kwargs):
        """  device log upload set from AWS,the message get-T603

        Args：
            | log_type | int | 日志类型，1：PE故障录波；2：BMS事件日志；3：BMS快照记录；4：IBG运行日志；5：CAN报文；6：通信日志；7、电池日志；8：通信状态日志；9：设备系统资源监控日志；10：4G日志；11：AWS日志；12：主进程日志；13：分时段电量统计日志；14：日电量统计日志 15：电芯数据上传（不能召唤，隔天自动上传） 16:2030运行日志； 17：内核运行日志；18：sunspecmodbus日志；19:告警处理日志；20:文件传输日志；21：PID/VID日志; 22:EMS升级日志；23:串口数据日志；24：智能负载日志；25：电表日志；26：MCU升级日志；27：看门狗日志；28：其他日志；29：nsc所有日志，19-29仅NSC支持，支持；30：sunnova日志 |
            | equipment_sn | string | 设备编号，对应IBG编号和各子设备编号；故障录波需要对应告警设备编号，2/3 BMS类日志需要对应BMS设备编号，4/5/6对应IBG设备编号。<=32Bytes |
            | url | string | 文件路径，<=128Bytes |
            | alarm_id | int | 用于确定日志文件,对应于cmdType 207中的alarm_id, 0：不需要ID，适用于equipment_sn=4/5/6 |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Log Upload Set From AWS Get | log_type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_log_upload_set_from_aws_get_check(self, **kwargs):
        """  device log upload set from AWS,the message get check-T603

        Kwargs:
            | log_type | int | 日志类型，1：PE故障录波；2：BMS事件日志；3：BMS快照记录；4：IBG运行日志；5：CAN报文；6：通信日志；7、电池日志；8：通信状态日志；9：设备系统资源监控日志；10：4G日志；11：AWS日志；12：主进程日志；13：分时段电量统计日志；14：日电量统计日志 15：电芯数据上传（不能召唤，隔天自动上传） 16:2030运行日志； 17：内核运行日志；18：sunspecmodbus日志；19:告警处理日志；20:文件传输日志；21：PID/VID日志; 22:EMS升级日志；23:串口数据日志；24：智能负载日志；25：电表日志；26：MCU升级日志；27：看门狗日志；28：其他日志；29：nsc所有日志，19-29仅NSC支持，支持；30：sunnova日志 |
            | equipment_sn | string | 设备编号，对应IBG编号和各子设备编号；故障录波需要对应告警设备编号，2/3 BMS类日志需要对应BMS设备编号，4/5/6对应IBG设备编号。<=32Bytes |
            | url | string | 文件路径，<=128Bytes |
            | alarm_id | int | 用于确定日志文件,对应于cmdType 207中的alarm_id, 0：不需要ID，适用于equipment_sn=4/5/6 |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Log Upload Set From AWS Get Check | log_type=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_log_upload_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_log_upload_set_from_device_get(self, *args, **kwargs):
        """  device log upload set from device,the message get-T604

        Args：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Log Upload Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_log_upload_set_from_device_get_check(self, **kwargs):
        """  device log upload set from device,the message get check-T604

        Kwargs:
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Log Upload Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_log_upload_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_log_upload_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T603/604

        Args for T603:
            | log_type | int | 日志类型，1：PE故障录波；2：BMS事件日志；3：BMS快照记录；4：IBG运行日志；5：CAN报文；6：通信日志；7、电池日志；8：通信状态日志；9：设备系统资源监控日志；10：4G日志；11：AWS日志；12：主进程日志；13：分时段电量统计日志；14：日电量统计日志 15：电芯数据上传（不能召唤，隔天自动上传） 16:2030运行日志； 17：内核运行日志；18：sunspecmodbus日志；19:告警处理日志；20:文件传输日志；21：PID/VID日志; 22:EMS升级日志；23:串口数据日志；24：智能负载日志；25：电表日志；26：MCU升级日志；27：看门狗日志；28：其他日志；29：nsc所有日志，19-29仅NSC支持，支持；30：sunnova日志 |
            | equipment_sn | string | 设备编号，对应IBG编号和各子设备编号；故障录波需要对应告警设备编号，2/3 BMS类日志需要对应BMS设备编号，4/5/6对应IBG设备编号。<=32Bytes |
            | url | string | 文件路径，<=128Bytes |
            | alarm_id | int | 用于确定日志文件,对应于cmdType 207中的alarm_id, 0：不需要ID，适用于equipment_sn=4/5/6 |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Args for T604：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 603/604 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=604 | rx_time_window=300 |
        | ${status} | Device Log Upload Set From Msg Get | file_name | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_log_upload_set_from_device_get, cmd_type_s2c: self.device_log_upload_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_log_upload_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T603/604

        Kwargs for T603:
            | log_type | int | 日志类型，1：PE故障录波；2：BMS事件日志；3：BMS快照记录；4：IBG运行日志；5：CAN报文；6：通信日志；7、电池日志；8：通信状态日志；9：设备系统资源监控日志；10：4G日志；11：AWS日志；12：主进程日志；13：分时段电量统计日志；14：日电量统计日志 15：电芯数据上传（不能召唤，隔天自动上传） 16:2030运行日志； 17：内核运行日志；18：sunspecmodbus日志；19:告警处理日志；20:文件传输日志；21：PID/VID日志; 22:EMS升级日志；23:串口数据日志；24：智能负载日志；25：电表日志；26：MCU升级日志；27：看门狗日志；28：其他日志；29：nsc所有日志，19-29仅NSC支持，支持；30：sunnova日志 |
            | equipment_sn | string | 设备编号，对应IBG编号和各子设备编号；故障录波需要对应告警设备编号，2/3 BMS类日志需要对应BMS设备编号，4/5/6对应IBG设备编号。<=32Bytes |
            | url | string | 文件路径，<=128Bytes |
            | alarm_id | int | 用于确定日志文件,对应于cmdType 207中的alarm_id, 0：不需要ID，适用于equipment_sn=4/5/6 |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for T604：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 603/604 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=604 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Log Upload Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_log_upload_set_from_device_get_check, cmd_type_s2c: self.device_log_upload_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
