from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Grid(Base):

    def go_to_commission_grid_profile_page(self):
        """ Go to system->commission->grid profile page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Grid Profile Page |
        """
        value = "locator_grid_profile"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_grid_profile_page(self, operate='next'):
        """ Go to system->commission->grid profile page and go back to the system->commission page  or next page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Grid Profile Page | operate=next |
            | Exit Commission Grid Profile Page | operate=exit |
        """
        _operate = operate.lower()

        if operate == 'exit':

            locator = "locator_system_general_up"

            try:
                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            except Exception as e:

                logger.info(f'Got the exception:{e}')

                locator = "locator_grid_profile_exit"

                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif operate == 'next':

            self._system_next_locator()

    def commission_grid_profile(self, **kwargs):

        self._system_next_locator()

        locator_name = "locator_grid_profile_general"

        eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        # only the default grid profile to choose

        if len(eles) == 3:

            value = "locator_grid_profile_default"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._system_save_locator()

    def get_commission_grid_profile(self, *args, **kwargs):
        """ get commission->Grid Profile names

        args:
            | grid_profile | list |  Grid profile list, CA UL 1741 - SB/CA UL 1741 - SA/User Defined,etc.|
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission Bind Or Unbind Parameters  |
            | ${result}	| Get Commission Bind Or Unbind Parameters  |  distributor | bind |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        self._get_grid_profile_parameters(ret_dict)

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def get_commission_grid_profile_parameters(self, *args, **kwargs):
        """ get commission->Grid Profile parameters

        Kwargs:
            | permit_service | bool | enable/disable |
            | permit_service_es_v_low | float | 0.0~100.0,step:0.1, % |
            | permit_service_es_v_high | float | 100.0~135.0,step:0.1, % |
            | permit_service_es_f_high | float | 45.00~66.00,step:0.01, Hz |
            | permit_service_es_f_low | 45.00~66.00,step:0.01, Hz |
            | normal_ramp | int | 1~1000,单位:秒 |
            | soft_start_ramp_es_delay | int | 0~600,单位:秒 |
            | soft_start_ramp_es_ramp_rate | int | 1~1000,单位:秒 |
            | unintentional_islanding_mode | bool | enable/disable |
            | trip_and_ride_through_ov1_trip_v | float | 110.0~120.0,step:0.1，% |
            | trip_and_ride_through_ov1_trip_t | float | 0.0~12.0,step:0.01,单位:秒 |
            | trip_and_ride_through_ov2_trip_v | float | 120.0~125.0,step:0.1，% |
            | trip_and_ride_through_ov2_trip_t | float | 0.0~11.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv1_trip_v | float | 0.0~88.0,step:0.1，% |
            | trip_and_ride_through_uv1_trip_t | float | 0.0~50.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv2_trip_v | float | 0.0~50.0,step:0.1，% |
            | trip_and_ride_through_uv2_trip_t | float | 0.0~21.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv3_trip_v | float | 0.0~50.0,step:0.1，% |
            | trip_and_ride_through_uv3_trip_t | float | 0.0~21.0,step:0.01,单位:秒 |
            | trip_and_ride_through_of1_trip_f | float | 45.0.0~66.0,step:0.1,单位:Hz |
            | trip_and_ride_through_of1_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_of2_trip_f | float | 45.0.0~66.0,step:0.1,单位:Hz |
            | trip_and_ride_through_of2_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uf1_trip_f | float | 45.0.0~60.0,step:0.1,单位:Hz |
            | trip_and_ride_through_uf1_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uf2_trip_f | float | 45.0.0~60.0,step:0.1,单位:Hz |
            | trip_and_ride_through_uf2_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | constant_power_factor | bool | enable/disable |
            | constant_power_factor_const_pf | float | 0.85~1.0, step:0.01  |
            | constant_power_factor_const_pf_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | constant_power_factor_const_pf_excitation | string | inj/abs |
            | constant_reactive_power | bool | enable/disable |
            | constant_reactive_power_const_q | float | 0.0~53.0,step:0.1, % |
            | constant_reactive_power_const_q_olrt | float | 0.0~90.0.0, step:0.01 单位:秒 |
            | constant_reactive_power_const_q_excitation |  string | inj/abs |
            | voltage_active_power |  bool | enable/disable |
            | voltage_active_power_pv_curve_v1 | float | 105.0~109.0,step:0.1, % |
            | voltage_active_power_pv_curve_p1 | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_curve_v2 | float | 106.0~110.0,step:0.1, % |
            | voltage_active_power_pv_curve_p2(gen) | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_curve_p'2(load) | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | active_power_reactive_power |  bool | enable/disable |
            | active_power_reactive_power_qp_curve_p3(gen) | float | 50.0~100.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p2(gen) | float | 40.0~80.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p1(gen) | float | 0.0~70.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'1(load) | float | -70.0~0.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'2(load) | float | -80.0~-40.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'3(load) | float | -100.0~-50.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q3(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q2(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q1(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'1(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'2(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'3(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | votage_reactive_power |  bool | enable/disable |
            | votage_reactive_power_qv_vref_auto_mode | bool | true/false |
            | votage_reactive_power_qv_fixed_vref | float | 95.0~105.0,step:0.1, % |
            | votage_reactive_power_qv_auto_vref_avg_time | int | 300~5000,单位:秒 |
            | votage_reactive_power_qv_curve_v1 | float | 82.0~98.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q1 | float | 0.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v2 | float | 90.0~110.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q2 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v3 | float | 90.0~110.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q3 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v4 | float | 102.0~118.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q4 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_olrt |  float | 0.0~90.0.0, step:0.01 单位:秒 |
            | frequency_droop | bool |  bool | enable/disable |
            | frequency_droop_pf_dbof | float | 0.017~10, step:0.01 单位:Hz |
            | frequency_droop_pf_dbuf | float | 0.017~10, step:0.01 单位:Hz |
            | frequency_droop_pf_kof | float | 0.02~105.0,0.07, step:0.01 |
            | frequency_droop_pf_kuf | float | 0.02~105.0,0.07, step:0.01 |
            | frequency_droop_pf_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${results} | Get Commission Grid Profile Parameters|
            | ${results} | Get Commission Grid Profile Parameters| frequency_droop | votage_reactive_power_qv_curve_q4 |

        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        locator_prefix = "locator_grid_profile"

        # general parameters

        attr_list = ['permit_service', 'unintentional_islanding_mode', 'constant_power_factor',
                     'constant_reactive_power', 'voltage_active_power', 'active_power_reactive_power',
                     'votage_reactive_power', 'frequency_droop']

        for i in attr_list:

            value = self._get_attribute_value_by_locator(f'locator_grid_profile_{i}', locator_fun=get_locator, ele_find_func=self.find_element, ef=self.get_element_attribute, attr_name='content-desc', auto_scroll=True)

            ret_dict.update({i: value})

        # permit service

        locator = "locator_grid_profile_permit_service"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["permit_service_es_v_low", "permit_service_es_v_high", "permit_service_es_f_high", "permit_service_es_f_low"]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # normal ramp

        locator = "locator_grid_profile_normal_ramp_click"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["normal_ramp"]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # soft start ramp

        locator = "locator_grid_profile_soft_start_ramp_click"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["soft_start_ramp_es_delay", "soft_start_ramp_es_ramp_rate"]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # trip and ride through

        locator = "locator_grid_profile_trip_and_ride_through"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["trip_and_ride_through_ov1_trip_v", "trip_and_ride_through_ov1_trip_t", "trip_and_ride_through_ov2_trip_v", "trip_and_ride_through_ov2_trip_t",
                     "trip_and_ride_through_uv1_trip_v", "trip_and_ride_through_uv1_trip_t", "trip_and_ride_through_uv2_trip_v", "trip_and_ride_through_uv2_trip_t",
                     "trip_and_ride_through_uv3_trip_v", "trip_and_ride_through_uv3_trip_t",
                     "trip_and_ride_through_of1_trip_f", "trip_and_ride_through_of1_trip_t", "trip_and_ride_through_of2_trip_f", "trip_and_ride_through_of2_trip_t",
                     "trip_and_ride_through_uf1_trip_f", "trip_and_ride_through_uf1_trip_t", "trip_and_ride_through_uf2_trip_f", "trip_and_ride_through_uf2_trip_t",
                     ]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # constant power factor

        locator = "locator_grid_profile_constant_power_factor"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["constant_power_factor_const_pf", "constant_power_factor_const_pf_olrt", "constant_power_factor_const_pf_excitation"]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # constant reactive power

        locator = "locator_grid_profile_constant_reactive_power"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["constant_reactive_power_const_q", "constant_reactive_power_const_q_olrt", "constant_reactive_power_const_q_excitation"]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # voltage active power

        locator = "locator_grid_profile_voltage_active_power"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["voltage_active_power_pv_curve_v1", "voltage_active_power_pv_curve_p1", "voltage_active_power_pv_curve_v2",
                     "voltage_active_power_pv_curve_p2(gen)", "voltage_active_power_pv_curve_p'2(load)", "voltage_active_power_pv_olrt",
                     ]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # active power-reactive power

        locator = "locator_grid_profile_active_power_reactive_power"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["active_power_reactive_power_qp_curve_p3(gen)", "active_power_reactive_power_qp_curve_p2(gen)", "active_power_reactive_power_qp_curve_p1(gen)",
                     "active_power_reactive_power_qp_curve_p'1(load)", "active_power_reactive_power_qp_curve_p'2(load)", "active_power_reactive_power_qp_curve_p'3(load)",
                     "active_power_reactive_power_qp_curve_q3(gen)", "active_power_reactive_power_qp_curve_q2(gen)", "active_power_reactive_power_qp_curve_q1(gen)",
                     "active_power_reactive_power_qp_curve_q'1(load)", "active_power_reactive_power_qp_curve_q'2(load)", "active_power_reactive_power_qp_curve_q'3(load)",
                     "active_power_reactive_power_qp_olrt",
                     ]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # voltage-reactive power

        attr = "votage_reactive_power_qv_vref_auto_mode"

        locator = f"locator_grid_profile_{attr}"

        ele_value = self._get_attribute_value_by_locator(locator, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked')

        ret_dict.update({attr: ele_value})

        locator = "locator_grid_profile_votage_reactive_power"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["votage_reactive_power_qv_fixed_vref", "votage_reactive_power_qv_auto_vref_avg_time",
                     "votage_reactive_power_qv_curve_v1", "votage_reactive_power_qv_curve_q1",
                     "votage_reactive_power_qv_curve_v2", "votage_reactive_power_qv_curve_q2",
                     "votage_reactive_power_qv_curve_v3", "votage_reactive_power_qv_curve_q3",
                     "votage_reactive_power_qv_curve_v4", "votage_reactive_power_qv_curve_q4",
                     "votage_reactive_power_qv_olrt",
                     ]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        # frequency droop

        locator = "locator_grid_profile_frequency_droop"

        self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

        attr_list = ["frequency_droop_pf_dbof", "frequency_droop_pf_dbuf",
                     "frequency_droop_pf_kof", "frequency_droop_pf_kuf",
                     "frequency_droop_pf_olrt",
                     ]

        self._get_attribute_value_with_locator_general(attr_list=attr_list, locator_prefix=locator_prefix, locator_fun=get_locator, attr_name='content-desc', split_character=' ', split_index=0, ret_dict=ret_dict)

        self._general_backward_upper_page()

        logger.info(f'the ret dict:{ret_dict}')

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def commission_grid_profile_parameters(self, *args, **kwargs):

        self.config_commission_grid_profile_parameters(*args, **kwargs)

        self._system_next_locator()

    def config_commission_grid_profile_parameters(self, *args, **kwargs):
        """ configure commission->Grid Profile parameters

        Kwargs:
            | grid_profile | user_defined(缺省)/AS 4777.4-C/AS 4777.3-B/AS 4777.2-A |
            | permit_service | bool | enabled/disabled |
            | permit_service_es_v_low | float | 0.0~100.0,step:0.1, % |
            | permit_service_es_v_high | float | 100.0~135.0,step:0.1, % |
            | permit_service_es_f_high | float | 45.00~66.00,step:0.01, Hz |
            | permit_service_es_f_low | 45.00~66.00,step:0.01, Hz |
            | normal_ramp | int | 1~1000,单位:秒 |
            | soft_start_ramp_es_delay | int | 0~600,单位:秒 |
            | soft_start_ramp_es_ramp_rate | int | 1~1000,单位:秒 |
            | unintentional_islanding_mode | bool | enable/disable |
            | trip_and_ride_through_ov1_trip_v | float | 110.0~120.0,step:0.1，% |
            | trip_and_ride_through_ov1_trip_t | float | 0.0~12.0,step:0.01,单位:秒 |
            | trip_and_ride_through_ov2_trip_v | float | 120.0~125.0,step:0.1，% |
            | trip_and_ride_through_ov2_trip_t | float | 0.0~11.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv1_trip_v | float | 0.0~88.0,step:0.1，% |
            | trip_and_ride_through_uv1_trip_t | float | 0.0~50.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv2_trip_v | float | 0.0~50.0,step:0.1，% |
            | trip_and_ride_through_uv2_trip_t | float | 0.0~21.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uv3_trip_v | float | 0.0~50.0,step:0.1，% |
            | trip_and_ride_through_uv3_trip_t | float | 0.0~21.0,step:0.01,单位:秒 |
            | trip_and_ride_through_of1_trip_f | float | 45.0.0~66.0,step:0.1,单位:Hz |
            | trip_and_ride_through_of1_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_of2_trip_f | float | 45.0.0~66.0,step:0.1,单位:Hz |
            | trip_and_ride_through_of2_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uf1_trip_f | float | 45.0.0~60.0,step:0.1,单位:Hz |
            | trip_and_ride_through_uf1_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | trip_and_ride_through_uf2_trip_f | float | 45.0.0~60.0,step:0.1,单位:Hz |
            | trip_and_ride_through_uf2_trip_t | float | 0.0~1000.0,step:0.01,单位:秒 |
            | constant_power_factor | bool | enable/disable |
            | constant_power_factor_const_pf | float | 0.85~1.0, step:0.01  |
            | constant_power_factor_const_pf_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | constant_power_factor_const_pf_excitation | string | inj/abs |
            | constant_reactive_power | bool | enable/disable |
            | constant_reactive_power_const_q | float | 0.0~53.0,step:0.1, % |
            | constant_reactive_power_const_q_olrt | float | 0.0~90.0.0, step:0.01 单位:秒 |
            | constant_reactive_power_const_q_excitation |  string | inj/abs |
            | voltage_active_power |  bool | enable/disable |
            | voltage_active_power_pv_curve_v1 | float | 105.0~109.0,step:0.1, % |
            | voltage_active_power_pv_curve_p1 | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_curve_v2 | float | 106.0~110.0,step:0.1, % |
            | voltage_active_power_pv_curve_p2(gen) | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_curve_p'2(load) | float | 0.0~100.0,step:0.1, % |
            | voltage_active_power_pv_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | active_power_reactive_power |  bool | enable/disable |
            | active_power_reactive_power_qp_curve_p3(gen) | float | 50.0~100.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p2(gen) | float | 40.0~80.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p1(gen) | float | 0.0~70.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'1(load) | float | -70.0~0.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'2(load) | float | -80.0~-40.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_p'3(load) | float | -100.0~-50.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q3(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q2(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q1(gen) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'1(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'2(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_curve_q'3(load) | float | -53.0~53.0,step:0.1, % |
            | active_power_reactive_power_qp_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
            | votage_reactive_power |  bool | enable/disable |
            | votage_reactive_power_qv_vref_auto_mode | bool | true/false |
            | votage_reactive_power_qv_fixed_vref | float | 95.0~105.0,step:0.1, % |
            | votage_reactive_power_qv_auto_vref_avg_time | int | 300~5000,单位:秒 |
            | votage_reactive_power_qv_curve_v1 | float | 82.0~98.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q1 | float | 0.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v2 | float | 90.0~110.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q2 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v3 | float | 90.0~110.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q3 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_curve_v4 | float | 102.0~118.0,step:0.1, %Vref |
            | votage_reactive_power_qv_curve_q4 | float | -53.0~53.0,step:0.1, % |
            | votage_reactive_power_qv_olrt |  float | 0.0~90.0.0, step:0.01 单位:秒 |
            | frequency_droop | bool |  bool | enable/disable |
            | frequency_droop_pf_dbof | float | 0.017~10, step:0.01 单位:Hz |
            | frequency_droop_pf_dbuf | float | 0.017~10, step:0.01 单位:Hz |
            | frequency_droop_pf_kof | float | 0.02~105.0,0.07, step:0.01 |
            | frequency_droop_pf_kuf | float | 0.02~105.0,0.07, step:0.01 |
            | frequency_droop_pf_olrt | float | 0.0~90.0, step:0.01 单位:秒 |
        Return:
            | None |
        Examples:
            | ${results} | Config Commission Grid Profile Parameters| frequency_droop=enable |
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        grid_profile = kwargs.pop("grid_profile", "user_defined")

        locator_prefix = "locator_grid_profile"

        def _set_value(attr, kwargs, locator_prefix=locator_prefix):

            if attr in kwargs:

                _value = kwargs.pop(attr)

                locator = f"{locator_prefix}_{attr}"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=_value, confirm=True)

        if grid_profile == "user_defined":

            locator = "locator_grid_profile_user_defined"

            self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

            self._system_edit_locator()

            # permit service

            par_list = ["permit_service", "permit_service_es_v_low", "permit_service_es_v_high", "permit_service_es_f_high"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_permit_service"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                if "permit_service" in kwargs:

                    value = kwargs['permit_service']

                    locator = "locator_general_button_select"

                    self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value=value)

                attr = "permit_service_es_v_low"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "permit_service_es_v_high"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "permit_service_es_f_low"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "permit_service_es_f_high"
                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                self._system_save_locator()

            # normal ramp

            par_list = ["normal_ramp"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_normal_ramp_click"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                attr = "normal_ramp"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                self._system_save_locator()

            # soft start ramp

            par_list = ["soft_start_ramp_es_delay", "soft_start_ramp_es_delay", "soft_start_ramp_es_ramp_rate"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_soft_start_ramp_click"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                attr = "soft_start_ramp_es_delay"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "soft_start_ramp_es_ramp_rate"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                self._system_save_locator()

            # unintentional islanding mode

            par_list = ["unintentional_islanding_mode"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                attr = "unintentional_islanding_mode"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                locator = "locator_general_button_select"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                self._system_save_locator()

            # trip and ride through

            par_list = ["trip_and_ride_through_ov1_trip_v", "trip_and_ride_through_ov1_trip_t", "trip_and_ride_through_ov2_trip_v", "trip_and_ride_through_ov2_trip_t",
                        "trip_and_ride_through_uv1_trip_v", "trip_and_ride_through_uv1_trip_t", "trip_and_ride_through_uv2_trip_v", "trip_and_ride_through_uv2_trip_t",
                        "trip_and_ride_through_uv3_trip_v", "trip_and_ride_through_uv3_trip_t",
                        "trip_and_ride_through_of1_trip_f", "trip_and_ride_through_of1_trip_t", "trip_and_ride_through_of2_trip_f", "trip_and_ride_through_of2_trip_t",
                        "trip_and_ride_through_uf1_trip_f", "trip_and_ride_through_uf1_trip_t", "trip_and_ride_through_uf2_trip_f", "trip_and_ride_through_uf2_trip_t",
                        ]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_trip_and_ride_through"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                # begin to config parameters...

                attr = "trip_and_ride_through_ov1_trip_v"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_ov1_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_ov2_trip_v"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_ov2_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv1_trip_v"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv1_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv2_trip_v"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv2_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv3_trip_v"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uv3_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_of1_trip_f"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_of1_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_of2_trip_f"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_of2_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uf1_trip_f"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uf1_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uf2_trip_f"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                attr = "trip_and_ride_through_uf2_trip_t"

                _set_value(attr, kwargs, locator_prefix=locator_prefix)

                self._system_save_locator()

            # constant power factor

            par_list = ["frequency_droop_pf_dbof", "frequency_droop_pf_dbuf", "frequency_droop_pf_kof", "frequency_droop_pf_kuf", "frequency_droop_pf_olrt"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_constant_power_factor"

                # self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_constant_power_factor_const_pf"

                # self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0.9, confirm=True)

                locator = f"{locator_prefix}_constant_power_factor_const_pf_olrt"

                # self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=20.1, confirm=True)

                locator = f"{locator_prefix}_constant_power_factor_const_pf_excitation"

                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

                locator = "locator_general_seekbar"

                self.set_value_in_scroll_element(value='ABS', locator_fun=get_locator, target_locator="locator_general_seekbar")

                locator = "locator_general_button_select"

                # self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="enabled")

                self._system_save_locator()

            # constant reactive power

            par_list = ["frequency_droop_pf_dbof", "frequency_droop_pf_dbuf", "frequency_droop_pf_kof", "frequency_droop_pf_kuf", "frequency_droop_pf_olrt"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_constant_reactive_power"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_constant_reactive_power_const_q"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=45, confirm=True)

                locator = f"{locator_prefix}_constant_reactive_power_const_q_olrt"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=11.1, confirm=True)

                locator = f"{locator_prefix}_constant_power_factor_const_pf_excitation"

                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

                locator = "locator_general_seekbar"

                self.set_value_in_scroll_element(value='ABS', locator_fun=get_locator, target_locator="locator_general_seekbar")

                locator = "locator_general_button_select"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="enabled")

                self._system_save_locator()

            # voltage active power

            par_list = ["voltage_active_power_pv_curve_v1", "voltage_active_power_pv_curve_p1",
                        "voltage_active_power_pv_curve_v2", "voltage_active_power_pv_curve_p2(gen)",
                        "voltage_active_power_pv_curve_p'2(load)", "voltage_active_power_pv_olrt",
                        "voltage_active_power"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_voltage_active_power"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_curve_v1"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=107, confirm=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_curve_p1"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=88, confirm=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_curve_v2"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=108.6, confirm=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_curve_p2(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0, confirm=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_curve_p'2(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0, confirm=True)

                locator = f"{locator_prefix}_voltage_active_power_pv_olrt"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=80, confirm=True)

                locator = "locator_general_button_select"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                self._system_save_locator()

            # active power-reactive power

            par_list = ["active_power_reactive_power_qp_curve_p3(gen)", "active_power_reactive_power_qp_curve_p2(gen)",
                        "active_power_reactive_power_qp_curve_p1(gen)", "active_power_reactive_power_qp_curve_p'1(load)",
                        "active_power_reactive_power_qp_curve_p'2(load)", "active_power_reactive_power_qp_curve_p'3(load)",
                        "active_power_reactive_power_qp_curve_q3(gen)", "active_power_reactive_power_qp_curve_q2(gen)",
                        "active_power_reactive_power_qp_curve_q1(gen)", "active_power_reactive_power_qp_curve_q'1(load)",
                        "active_power_reactive_power_qp_curve_q'2(load)", "active_power_reactive_power_qp_curve_q'3(load)",
                        "active_power_reactive_power_qp_olrt", "active_power_reactive_power",
                        ]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_active_power_reactive_power"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p3(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=80.3, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p2(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=78.8, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p1(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=60.7, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p'1(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-66.9, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p'2(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-77.7, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_p'3(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-65.2, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q3(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-44.5, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q2(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=33.6, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q1(gen)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=44.4, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q'1(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=50.7, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q'2(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=52.7, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_curve_q'3(load)"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=52.9, confirm=True)

                locator = f"{locator_prefix}_active_power_reactive_power_qp_olrt"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=77.5, confirm=True)

                locator = "locator_general_button_select"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                self._system_save_locator()

            # voltage-reactive power

            par_list = ["votage_reactive_power_qv_fixed_vref", "votage_reactive_power_qv_auto_vref_avg_time",
                        "votage_reactive_power_qv_curve_v1", "votage_reactive_power_qv_curve_q1",
                        "votage_reactive_power_qv_curve_v2", "votage_reactive_power_qv_curve_q2",
                        "votage_reactive_power_qv_curve_v3", "votage_reactive_power_qv_curve_q3",
                        "votage_reactive_power_qv_curve_v4", "votage_reactive_power_qv_curve_q4",
                        "votage_reactive_power_qv_olrt", "votage_reactive_power_qv_vref_auto_mode",
                        "votage_reactive_power",
                        ]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_votage_reactive_power"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_fixed_vref"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=96, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_auto_vref_avg_time"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=700, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_v1"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=88, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_q1"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=50, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_v2"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=101, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_q2"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-27.8, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_v3"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=103.4, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_q3"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=52.8, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_v4"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=103.4, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_curve_q4"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=-36, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_olrt"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=79.7, confirm=True)

                locator = f"{locator_prefix}_votage_reactive_power_qv_vref_auto_mode"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                locator = f"{locator_prefix}_votage_reactive_power_set"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                self._system_save_locator()

            # frequency droop

            par_list = ["frequency_droop_pf_dbof", "frequency_droop_pf_dbuf", "frequency_droop_pf_kof", "frequency_droop_pf_kuf", "frequency_droop_pf_olrt"]

            if self._check_user_kwargs(kwargs=kwargs, par_list=par_list):

                locator = f"{locator_prefix}_frequency_droop"

                self.get_locator_and_operate_element(locator, locator_fun=get_locator, auto_scroll=True, click=True)

                locator = f"{locator_prefix}_frequency_droop_pf_dbof"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0.018, confirm=True)

                locator = f"{locator_prefix}_frequency_droop_pf_dbuf"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0.2, confirm=True)

                locator = f"{locator_prefix}_droop_pf_kof"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0.06, confirm=True)

                locator = f"{locator_prefix}_droop_pf_kuf"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=0.07, confirm=True)

                locator = f"{locator_prefix}_droop_pf_olrt"

                self._set_value_in_drop_pop_element(locator, locator_fun=get_locator, value=33, confirm=True)

                locator = "locator_general_button_select"

                self._config_select_element_status(locator, locator_func=get_locator, element_type='button', checked_type='checked', expected_value="disabled")

                self._system_save_locator()

            self._system_save_locator()

        else:

            # choose the existing grid profile...
            pass

    def _get_grid_profile_parameters(self, ret_dict):

        locator_name = "locator_grid_profile_general"

        eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        _value_list = []

        grid_number = len(eles)

        if grid_number == 3:

            locator_name = "locator_grid_profile_get"

            ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

            if ele:

                _value = self.get_element_attribute(ele, "content-desc")

                logger.debug(f'the values:{_value}')

                value = _value.split('\n')[-1]

                _value_list.append(value)

            else:

                raise ValueError("FAIL to get grid profile!")

        else:

            for i in eles[3:]:

                _value = self.get_element_attribute(i, "content-desc")

                _value_list.append(_value)

        ret_dict.update({"grid_profile": _value_list})
