from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt

# 智能负载开关查询

cmd_type_c2s = 1415

cmd_type_s2c = 1416

attr_map_c2s = {

    **attr_map_common_opt,

    'gridSwitchStatus': ('grid_switch_status', 'int'),
    'genStatus': ('generator_status', 'int'),
    'gridVoltCheck': ('generator_startup_type', 'int'),
    'currentSoc': ('current_soc', 'int'),
    'smartSwitchData': ('smart_switch_data', 'dict'),

    'circuitSwRsn': ('switch_reason', 'int'),
    'cutoffPower': ('power_before_switch', 'int'),
}

attr_map_s2c = {

    **attr_map_c2s,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSmartLoadSwitchRealTimeGet(Base):

    def local_smart_load_switch_real_time_get(self, *args, **kwargs):
        """  local smart load switch get-T1415
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Smart Load Switch Real Time Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_smart_load_switch_real_time_get_result_check(self, _response, **kwargs):
        """  local smart load switch get result check-T1416

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Smart Load Switch Real Time Get |
        | ${status} = | Local Smart Load Switch Real Time Get Result Check | ${response} | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
