from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 系统设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 315

cmd_type_c2s = 316

attr_map_s2c = {

    **attr_map_common_opt,

    'paraType': ('parameter_type', 'int'),
    'reboot': ('', 'int'),

    'cleanUnlockAlarm': ('clean_unlock_alarm', 'int'),
    'cleanLockAlarm': ('clean_lock_alarm', 'int'),
    'cleanAlarmFlag': ('clean_alarm_flag', 'int'),
    'reset': ('', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SystemSetting(object):

    def system_set(self, *args, **kwargs):
        """  system set-T315/316

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs:
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | System Set | reboot=1 |
        | ${status} = | System Set | result | reboot=1 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def system_set_result_check(self, _response, **kwargs):
        """  system set result check-T316

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | System Set | reboot=1 |
        | ${status} = | System Set Result Check | ${response} | result=0  | reboot=1 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def system_set_from_aws_get(self, *args, **kwargs):
        """  system set from AWS,the message get-T315

        Args：
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   System Set From AWS Get | reboot |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def system_set_from_aws_get_check(self, **kwargs):
        """  system set from AWS,the message get check-T315

        Kwargs:
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |   System Set From AWS Get Check | reboot=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.system_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def system_set_from_device_get(self, *args, **kwargs):
        """   system set response from device,the message get-T316

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | System Set From Device Get | result | reboot |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def system_set_from_device_get_check(self, **kwargs):
        """  system set response from device,the message get check-T316

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  System Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.system_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def system_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T315/316

        Args for T315:
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Args for T316：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 315/316 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=315 | rx_time_window=300 |
        | ${status} | System Set From Msg Get | reboot | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.system_set_from_device_get, cmd_type_s2c: self.system_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def system_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T315/316

        Kwargs for T315:
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs for TT315：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 1:设置 |
            | parameter_type | int | 参数类型， 1：EMS软件重启；2：清除锁定故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reboot | int | EMS软件重启，1：重启生效；0：无效 |
            | clean_unlock_alarm  | int | 清除非锁定故障，1：清除非锁定故障生效；0：无效 |
            | clean_lock_alarm  | int | 清除锁定故障，1：清除锁定故障生效；0：无效 |
            | clean_alarm_flag  | int | 清除故障标志，0：无效；1：整机系统故障；2：IBG故障；3：IBG从板故障；4：PE一般故障；5：PE严重故障；6：BMS故障；7：BMS故障解锁；8：IBG DSP严重故障 |
            | reset | int | 恢复出厂设置，1：恢复出厂生效；0：无效,目前未用此字段 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 315/316 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=316 | rx_time_window=300 | filter_mode=and |
        | ${status} | System Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.system_set_from_device_get_check, cmd_type_s2c: self.system_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
