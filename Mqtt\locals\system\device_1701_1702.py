from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 系统参数

cmd_type_c2s = 1701

cmd_type_s2c = 1702

attr_map_c2s = {

    **attr_map_common_opt,

    'electricSys': ('electric_system', 'int'),

    'electricSupply': ('overload_current', 'int'),
    'airSwitchCur': ('apower_rated_current', 'int'),
    'apowerChPw': ('apower_charging_power', 'int'),
    'apowerDishPw': ('apower_discharging_power', 'int'),
    'gridPhaseConSet': ('grid_phase_config', 'int'),
    'gridPhaseSeqSet': ('grid_phase_order', 'int'),

    'solarInstallState': ('pv_installed', 'int'),
    'genRatePower': ('generator_rated_power', 'float'),

    'solarInvManu': ('pv_inv_vendor', 'int'),
    'solarInvRatePower': ('pv_inv_rated_power', 'float'),
    'fhpRatePower': ('fhp_rated_power', 'float'),

    'kwRatePower': ('grid_feed_in_power_ratio', 'int'),
    'sugGridType': ('suggested_grid_type', 'int'),
    'gridExportEnable': ('grid_feed_in_enabled', 'int'),
    'gridSoftLimit': ('grid_feed_in_soft_limit', 'int'),
    'gridHardLimit': ('grid_feed_in_hard_limit', 'int'),
    'BSRelyEn': ('black_start_relay_installed', 'int'),

    'isThreePhaseInstall': ('three_phase_installed', 'int'),
    'mPanGridEnb': ('main_panel_grid_port_enabled', 'int'),
    'mPanGridCurLim': ('main_panel_grid_port_current', 'int'),
    'ratedPwr': ('apower20_rated_power', 'int'),
    'msaModel': ('msa_model', 'int'),
    'busbarCurLim': ('busbar_current', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'ratedGridVolt': ('grid_voltage', 'int'),
    'ratedGridHz': ('grid_frequency', 'int'),
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSysParameter(Base):

    def local_system_parameter_set(self, *args, **kwargs):
        """  local system parameter set-T1701
        Args：
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败，2：设置失败 |
            | electric_system | int | 用电制式，0：北美120V/240V SPLIT 60HZ |
            | grid_voltage | int | 额定电压，0:120V 其余地区显示；1:230V仅澳洲地区可显示；2：240V仅澳洲地区可显示；3:115V仅巴巴多斯地区可显示 |
            | grid_frequency | int | 额定频率，0:60Hz;1:50Hz |
            | overload_current | int | 空开最大过载电流,单位：A |
            | apower_rated_current | int | apower空开额定电流，单位：A |
            | apower_charging_power | int | apower充电限制功率，单位：w |
            | apower_discharging_power | int | apower放电限制功率，单位：w |
            | grid_phase_config | int | 市电相制设置，00:split;01单相三角形接法02：三相三角形接法 |
            | grid_phase_order | int | 电网相序异常，0：正常，1：异常 |
            | pv_installed | int | 是否安装光伏，0-无光伏；1-有光伏 |
            | generator_rated_power | float | 发电机额定功率，单位 0.1kW，0-40kw，0kW表示未安装 |
            | pv_inv_vendor | int | 逆变器厂商，0-未知，1-solarEdge；2.solis |
            | pv_inv_rated_power | float | 光伏逆变器额定功率，单位：w |
            | fhp_rated_power | float | FHP额定功率，单位：w |
            | grid_feed_in_power_ratio | int | 0不允许；-1不限制，单位：% |
            | suggested_grid_type | int | 建议相制设置，0-split；1-单相三角形接法；2-三相三角形接法 |
            | grid_feed_in_enabled | int | 馈网使能，0：不允许馈网 1: 允许馈网 |
            | grid_feed_in_soft_limit | int | 馈网软限制，-1：不限制， 单位：w |
            | grid_feed_in_hard_limit | int | 馈网硬限制，-1：不限制， 单位：w |
            | black_start_relay_installed | int | 黑启动继电器安装，0:未安装，1：安装 |
            | three_phase_installed | int | 三相安装，0:否；1：是 |
            | main_panel_grid_port_enabled | int | 使能主配电盘市电端口，0:否；1：是 |
            | main_panel_grid_port_current | int | 主备电盘市电端口限制电流,单位：A |
            | apower20_rated_power | int | apower2.0额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA); 3:6.7kw(铭牌7.7KVA); 5:8.4kw(铭牌9.6KVA); 4:10kw(铭牌11.5KVA) |
            | msa_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | busbar_current | int | 主配电盘铜排最大限制电流，单位：A |
        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | electric_system | int | 用电制式，0：北美120V/240V SPLIT 60HZ |
            | overload_current | int | 空开最大过载电流,单位：A |
            | apower_rated_current | int | apower空开额定电流，单位：A |
            | apower_charging_power | int | apower充电限制功率，单位：w |
            | apower_discharging_power | int | apower放电限制功率，单位：w |
            | grid_phase_config | int | 市电相制设置，00:split;01单相三角形接法02：三相三角形接法 |
            | grid_phase_order | int | 电网相序异常，0：正常，1：异常 |
            | pv_installed | int | 是否安装光伏，0-无光伏；1-有光伏 |
            | generator_rated_power | float | 发电机额定功率，单位 0.1kW，0-40kw，0kW表示未安装 |
            | pv_inv_vendor | int | 逆变器厂商，0-未知，1-solarEdge；2.solis |
            | pv_inv_rated_power | float | 光伏逆变器额定功率，单位：w |
            | fhp_rated_power | float | FHP额定功率，单位：w |
            | grid_feed_in_power_ratio | int | 0不允许；-1不限制，单位：% |
            | suggested_grid_type | int | 建议相制设置，0-split；1-单相三角形接法；2-三相三角形接法 |
            | grid_feed_in_enabled | int | 馈网使能，0：不允许馈网 1: 允许馈网 |
            | grid_feed_in_soft_limit | int | 馈网软限制，-1：不限制， 单位：w |
            | grid_feed_in_hard_limit | int | 馈网硬限制，-1：不限制， 单位：w |
            | black_start_relay_installed | int | 黑启动继电器安装，0:未安装，1：安装 |
            | three_phase_installed | int | 三相安装，0:否；1：是 |
            | main_panel_grid_port_enabled | int | 使能主配电盘市电端口，0:否；1：是 |
            | main_panel_grid_port_current | int | 主备电盘市电端口限制电流,单位：A |
            | apower20_rated_power | int | apower2.0额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA); 3:6.7kw(铭牌7.7KVA); 5:8.4kw(铭牌9.6KVA); 4:10kw(铭牌11.5KVA) |
            | msa_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | busbar_current | int | 主配电盘铜排最大限制电流，单位：A |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local System Parameter Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_system_parameter_set_result_check(self, _response, **kwargs):
        """  local system parameter set result check-T1702

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败，2：设置失败 |
            | electric_system | int | 用电制式，0：北美120V/240V SPLIT 60HZ |
            | grid_voltage | int | 额定电压，0:120V 其余地区显示；1:230V仅澳洲地区可显示；2：240V仅澳洲地区可显示；3:115V仅巴巴多斯地区可显示 |
            | grid_frequency | int | 额定频率，0:60Hz;1:50Hz |
            | overload_current | int | 空开最大过载电流,单位：A |
            | apower_rated_current | int | apower空开额定电流，单位：A |
            | apower_charging_power | int | apower充电限制功率，单位：w |
            | apower_discharging_power | int | apower放电限制功率，单位：w |
            | grid_phase_config | int | 市电相制设置，00:split;01单相三角形接法02：三相三角形接法 |
            | grid_phase_order | int | 电网相序异常，0：正常，1：异常 |
            | pv_installed | int | 是否安装光伏，0-无光伏；1-有光伏 |
            | generator_rated_power | float | 发电机额定功率，单位 0.1kW，0-40kw，0kW表示未安装 |
            | pv_inv_vendor | int | 逆变器厂商，0-未知，1-solarEdge；2.solis |
            | pv_inv_rated_power | float | 光伏逆变器额定功率，单位：w |
            | fhp_rated_power | float | FHP额定功率，单位：w |
            | grid_feed_in_power_ratio | int | 0不允许；-1不限制，单位：% |
            | suggested_grid_type | int | 建议相制设置，0-split；1-单相三角形接法；2-三相三角形接法 |
            | grid_feed_in_enabled | int | 馈网使能，0：不允许馈网 1: 允许馈网 |
            | grid_feed_in_soft_limit | int | 馈网软限制，-1：不限制， 单位：w |
            | grid_feed_in_hard_limit | int | 馈网硬限制，-1：不限制， 单位：w |
            | black_start_relay_installed | int | 黑启动继电器安装，0:未安装，1：安装 |
            | three_phase_installed | int | 三相安装，0:否；1：是 |
            | main_panel_grid_port_enabled | int | 使能主配电盘市电端口，0:否；1：是 |
            | main_panel_grid_port_current | int | 主备电盘市电端口限制电流,单位：A |
            | apower20_rated_power | int | apower2.0额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA); 3:6.7kw(铭牌7.7KVA); 5:8.4kw(铭牌9.6KVA); 4:10kw(铭牌11.5KVA) |
            | msa_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | busbar_current | int | 主配电盘铜排最大限制电流，单位：A |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local System Parameter Set |
        | ${status} = | Local System Parameter Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
