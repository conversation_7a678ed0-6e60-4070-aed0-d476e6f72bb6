from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from time import sleep
from robot.api import logger
from .smartcircuits import Smartcircuits
from .locator import get_locator
from .generator import Generator
from .v2l import V2L
from .stormhedge import StormHedge


class Function(Smartcircuits, Generator, V2L, StormHedge):

    def go_to_settings_mode_page(self):
        """ Go to system->setting->mode page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Mode Page |
        """
        locator_comm = ('acc_id', "Mode")

        self.find_element(*locator_comm, click=True)

    def exit_setting_mode_page(self):
        """ exit system->setting->mode page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Mode Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_smart_circuits_page(self):
        """ Go to system->setting->smart circuits page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Smart Circuits Page |
        """
        locator_comm = ('acc_id', "Smart Circuits")

        self.find_element(*locator_comm, click=True)

    def exit_setting_smart_circuits_page(self):
        """ exit system->setting->smart circuits page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Smart Circuits Page |
        """
        # locator = ("xpath", """//android.widget.ImageView[@content-desc="Smart Circuits\nWith a built-in smart circuit module, the aGate provides three smart circuits functionality, on which the appliances connected can be configured to automatically start/stop based on the battery reserve or according to the previously set schedule, or be turned on/off."]/android.view.View/android.widget.ImageView""")

        # self._go_to_sub_page(locator=locator)
        self._general_backward_upper_page()

    def go_to_settings_generator_page(self):
        """ Go to system->setting->generator page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Generator Page |
        """

        locator_comm = ('acc_id', "Generator")

        self.find_element(*locator_comm, click=True)

    def exit_setting_generator_page(self):
        """ exit system->setting->generator page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Generator Page |
        # """
        # locator = ("xpath", """//android.widget.ImageView[@content-desc="Generator\nThe FHP system can connect to a household backup power generator (generator) via a generator module built-in the aGate."]/android.view.View/android.widget.ImageView""")

        # self._go_to_sub_page(locator=locator)
        self._general_backward_upper_page()

    def go_to_settings_v2l_page(self):
        """ Go to system->setting->V2L page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings V2L Page |
        """
        locator_comm = ('acc_id', "Vehicle to Load (V2L)")

        self.find_element(*locator_comm, click=True)

    def exit_setting_v2l_page(self):
        """ exit system->setting->V2L page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting V2L Page |
        """
        locator = ("xpath", """//android.view.View[@content-desc="Vehicle\naGate\nHousehold loads"]/android.view.View[1]/android.widget.ImageView[1]""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_add_accessories_page(self):
        """ Go to system->setting->add accessories page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Add Accessories Page |
        """
        locator_comm = ('acc_id', "Add Accessories")

        self.find_element(*locator_comm, click=True)

    def exit_setting_add_accessories_page(self):
        """ exit system->setting->add accessories page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Add Accessories Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_part_replacement_page(self):
        """ Go to system->setting->part replacement accessories page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Part Replacement Page |
        """
        locator_comm = ('acc_id', "Part Replacement")

        self.find_element(*locator_comm, click=True)

    def exit_setting_part_replacement_page(self):
        """ exit system->setting->part replacement page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Part Replacement Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_tariff_settings_page(self):
        """ Go to system->setting->tariff settings page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Tariff Settings Page |
        """
        locator_comm = ('acc_id', "Tariff Settings")

        self.find_element(*locator_comm, click=True)

    def exit_setting_tariff_settings_page(self):
        """ exit system->setting->tariff settings page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Tariff Settings Page |
        """
        locator = ("xpath", """//android.widget.Button[@content-desc="Next"]/android.view.View[1]/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_grid_charing_and_energy_export_page(self):
        """ Go to system->setting->grid charing&energy export page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Grid Charging And Energy Export Page |
        """
        locator_comm = ('acc_id', "Grid Charging & Energy Export")

        self.find_element(*locator_comm, click=True)

    def exit_setting_grid_charing_and_energy_export_page(self):
        """ exit system->setting->grid charing&energy export page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Grid Charging And Energy Export Page |
        """
        locator = ("cls_name", "android.widget.ImageView")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_energy_incentives_page(self):
        """ Go to system->setting->energy incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Energy Incentives Page |
        """
        locator_comm = ('acc_id', "Energy Incentives")

        self.find_element(*locator_comm, click=True)

    def exit_setting_energy_incentives_page(self):
        """ exit system->setting->energy incentives page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Energy Incentives Page |
        """
        locator = ("xpath", """//android.widget.ImageView[@content-desc="No policy yet"]/android.view.View/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_device_info_page(self):
        """ Go to system->setting->device info page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Device Info Page |
        """
        locator_comm = ('acc_id', "Device Info")

        self.find_element(*locator_comm, click=True)

    def exit_setting_device_info_page(self):
        """ exit system->setting->device info page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Device Info Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_site_information_page(self):
        """ Go to system->setting->site information page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Site Information Page |
        """
        locator_comm = ('acc_id', "Site information")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_site_information_page(self, waiting_time=3):
        """ exit system->setting->site information page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Site Information Page |
        """
        locator = ("xpath", """//android.widget.Button[@content-desc="Submit"]/android.view.View[1]/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

        sleep(waiting_time)

    def go_to_settings_resource_address_page(self):
        """ Go to system->setting->2030.5 Resource Address page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Resource Address Page |
        """
        locator_comm = ('acc_id', "2030.5 Resource Address")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_resource_address_page(self):
        """ exit system->setting->2030.5 Resource Address page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Resource Address Page |
        """
        locator = ("cls_name", """android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_modbus_page(self):
        """ Go to system->setting->modbus page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Modbus Page |
        """
        locator_comm = ('acc_id', "Modbus")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_modbus_page(self):
        """ exit system->setting->modbus page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Modbus Page |
        """
        self._general_backward_upper_page()

    # the function below is for the homeowner account

    def turn_to_settings_storm_hedge_page(self):
        """ turn to setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Modbus Page |
        """
        locator_comm = ('acc_id', "Settings, Tab 3 of 4")

        self.find_element(*locator_comm, click=True)

        locator = ("xpath", """(//android.view.View[@content-desc])[3]""")

        self._go_to_sub_page(locator=locator)

    def exit_setting_storm_hedge(self):

        locator = ("xpath", """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.widget.ImageView/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView[1]""")

        self._go_to_sub_page(locator=locator)
