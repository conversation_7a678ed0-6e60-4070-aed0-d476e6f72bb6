from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# Sunrun数据主动上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 231

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'fhpSn': ('apower_sn', 'list'),
    'acVolt': ('ac_voltage', 'float'),
    'acFrep': ('ac_freq', 'float'),
    'aggPvTotalEnergy': ('kwh_mppt_pv_total', 'float'),
    'aggAcPvTotalEnergy': ('kwh_ac_pv_total', 'float'),

    'battMaxTemp': ('bat_max_temp', 'list'),
    'battMinTemp': ('bat_min_temp', 'list'),
    'currChgPower': ('current_charging_power', 'list'),
    'currDischgPower': ('current_discharging_power', 'list'),

    'dischgEnergy': ('kwh_charged', 'list'),
    'chgEnergy': ('kwh_discharged', 'list'),
    'chgTotalEnergy ': ('kwh_total_charged', 'list'),
    'dischgTotalEnergy': ('kwh_total_discharged', 'list'),
    'batteryState': ('bat_status', 'list'),

    'dcBusVolt': ('dc_bus_voltage', 'list'),
    'battVolt': ('bat_voltage', 'list'),
    'soc': ('', 'list'),
    'soh': ('', 'list'),
    'currBatStatus': ('current_bat_status', 'list'),
    'runStatus': ('run_status', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SunrunDataUpload(object):

    def sunrun_data_from_device_get(self, *args, **kwargs):
        """  sunrun date upload from device,the message get-T231

        Args：
            | apower_sn | list | aPower序列号 |
            | ac_voltage | float | 备电侧交流电压，单位：V |
            | ac_freq | float | 备电侧交流频率，单位：Hz |
            | kwh_mppt_pv_total | float | MPPT光伏总电量，单位：kwh |
            | kwh_ac_pv_total | float | 交流光伏总电量，单位：kwh |
            | bat_max_temp | list | 电池最高温度，单位：摄氏度 |
            | bat_min_temp | list | 电池最低温度，单位：摄氏度 |
            | current_charging_power | list | 当前充电功率，单位：kw |
            | current_discharging_power | list | 当前放电功率，单位：kw |
            | kwh_charged | list | 放电量， 单位：kwh |
            | kwh_discharged | list | 充电量，单位：kwh |
            | kwh_total_charged | list | 充电总电量，单位：kwh |
            | kwh_total_discharged | list | 放电总电量，单位：kwh |
            | bat_status | list | 电池状态 |
            | dc_bus_voltage | list | mid总线直流母线电压，单位：V |
            | bat_voltage | list | 电池组电压，单位：V |
            | soc | list | 电池电量soc， 单位：% |
            | soh | list | 电池健康度soh， 单位：% |
            | current_bat_status | list | 当前电池状态 |
            | run_status | int | 设备运行状态 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Sunrun Data From Device Get | soc | soh |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def sunrun_data_from_device_get_check(self, **kwargs):
        """  sunrun date upload data from device,the message get check-T231

        Kwargs:
            | apower_sn | list | aPower序列号 |
            | ac_voltage | float | 备电侧交流电压，单位：V |
            | ac_freq | float | 备电侧交流频率，单位：Hz |
            | kwh_mppt_pv_total | float | MPPT光伏总电量，单位：kwh |
            | kwh_ac_pv_total | float | 交流光伏总电量，单位：kwh |
            | bat_max_temp | list | 电池最高温度，单位：摄氏度 |
            | bat_min_temp | list | 电池最低温度，单位：摄氏度 |
            | current_charging_power | list | 当前充电功率，单位：kw |
            | current_discharging_power | list | 当前放电功率，单位：kw |
            | kwh_charged | list | 放电量， 单位：kwh |
            | kwh_discharged | list | 充电量，单位：kwh |
            | kwh_total_charged | list | 充电总电量，单位：kwh |
            | kwh_total_discharged | list | 放电总电量，单位：kwh |
            | bat_status | list | 电池状态 |
            | dc_bus_voltage | list | mid总线直流母线电压，单位：V |
            | bat_voltage | list | 电池组电压，单位：V |
            | soc | list | 电池电量soc， 单位：% |
            | soh | list | 电池健康度soh， 单位：% |
            | current_bat_status | list | 当前电池状态 |
            | run_status | int | 设备运行状态 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Sunrun Data From Device Get Check | soc=75 | run_status=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunrun_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunrun_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T231

        Args for T231：
            | apower_sn | list | aPower序列号 |
            | ac_voltage | float | 备电侧交流电压，单位：V |
            | ac_freq | float | 备电侧交流频率，单位：Hz |
            | kwh_mppt_pv_total | float | MPPT光伏总电量，单位：kwh |
            | kwh_ac_pv_total | float | 交流光伏总电量，单位：kwh |
            | bat_max_temp | list | 电池最高温度，单位：摄氏度 |
            | bat_min_temp | list | 电池最低温度，单位：摄氏度 |
            | current_charging_power | list | 当前充电功率，单位：kw |
            | current_discharging_power | list | 当前放电功率，单位：kw |
            | kwh_charged | list | 放电量， 单位：kwh |
            | kwh_discharged | list | 充电量，单位：kwh |
            | kwh_total_charged | list | 充电总电量，单位：kwh |
            | kwh_total_discharged | list | 放电总电量，单位：kwh |
            | bat_status | list | 电池状态 |
            | dc_bus_voltage | list | mid总线直流母线电压，单位：V |
            | bat_voltage | list | 电池组电压，单位：V |
            | soc | list | 电池电量soc， 单位：% |
            | soh | list | 电池健康度soh， 单位：% |
            | current_bat_status | list | 当前电池状态 |
            | run_status | int | 设备运行状态 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 231 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=231 | rx_time_window=320 | filter_mode=and |
        | ${status} | Sunrun Data From Msg Get | soc | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunrun_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def sunrun_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T231

        Kwargs for T231：
            | apower_sn | list | aPower序列号 |
            | ac_voltage | float | 备电侧交流电压，单位：V |
            | ac_freq | float | 备电侧交流频率，单位：Hz |
            | kwh_mppt_pv_total | float | MPPT光伏总电量，单位：kwh |
            | kwh_ac_pv_total | float | 交流光伏总电量，单位：kwh |
            | bat_max_temp | list | 电池最高温度，单位：摄氏度 |
            | bat_min_temp | list | 电池最低温度，单位：摄氏度 |
            | current_charging_power | list | 当前充电功率，单位：kw |
            | current_discharging_power | list | 当前放电功率，单位：kw |
            | kwh_charged | list | 放电量， 单位：kwh |
            | kwh_discharged | list | 充电量，单位：kwh |
            | kwh_total_charged | list | 充电总电量，单位：kwh |
            | kwh_total_discharged | list | 放电总电量，单位：kwh |
            | bat_status | list | 电池状态 |
            | dc_bus_voltage | list | mid总线直流母线电压，单位：V |
            | bat_voltage | list | 电池组电压，单位：V |
            | soc | list | 电池电量soc， 单位：% |
            | soh | list | 电池健康度soh， 单位：% |
            | current_bat_status | list | 当前电池状态 |
            | run_status | int | 设备运行状态 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 231 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=231 | rx_time_window=320 | filter_mode=and |
        | ${status} | Sunrun Data From Msg Get Check | soc=56 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunrun_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
