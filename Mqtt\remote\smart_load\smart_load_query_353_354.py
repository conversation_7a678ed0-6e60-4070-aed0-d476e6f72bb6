from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 智能负载数据查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 353

cmd_type_c2s = 354

attr_map_s2c = {

    **attr_map_common_opt,

    'Sw1Volt': ('smart_load1_voltage', 'int'),
    'Sw2Volt': ('smart_load2_voltage', 'int'),

    'CarSWCurr': ('charging_current_for_car', 'int'),
    'CarSWPower': ('charging_power_for_car', 'int'),
    'CarSWExpEnergy': ('charging_out_energy_for_car', 'int'),
    'CarSWImpEnergy': ('charging_in_energy_for_car', 'int'),

    'SW1Curr': ('smart_load1_current', 'int'),
    'SW2Curr': ('smart_load2_current', 'int'),

    'SW1ExpPower': ('smart_load1_power', 'int'),
    'SW2ExpPower': ('smart_load2_power', 'int'),
    'SW1ExpEnergy': ('smart_load1_energy', 'int'),
    'SW2ExpEnergy': ('smart_load2_energy', 'int'),

    'CarSwConsSupExpEnerge': ('charged_energy_for_car_with_fixed_energy_mode', 'int'),

    'power': ('generator_power', 'int'),
    'curr': ('generator_current', 'int'),
    'volt': ('generator_voltage', 'int'),
    'freq': ('generator_frequency', 'int'),
    'genpowerGen': ('generator_generated_energy', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SmartLoadQuery(object):

    def smart_load_set(self, *args, **kwargs):
        """  smart load set-T353/354

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Set | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def smart_load_set_result_check(self, _response, **kwargs):
        """  smart load set result check-T354

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | smart load Set | opt=0 |
        | ${status} = | Smart Load Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def smart_load_set_from_aws_get(self, *args, **kwargs):
        """  smart load set from AWS,the message get-T353

        Args：
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def smart_load_set_from_aws_get_check(self, **kwargs):
        """  smart load set from AWS,the message get check-T353

        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Smart Load Set From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.smart_load_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def smart_load_set_from_device_get(self, *args, **kwargs):
        """   smart load set response from device,the message get-T354

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def smart_load_set_from_device_get_check(self, **kwargs):
        """  smart load set response from device,the message get check-T354

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Smart Load Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.smart_load_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def smart_load_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T353/354

        Args for T353:
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Args for T354：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 353/354 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=353 | rx_time_window=300 |
        | ${status} | Smart Load Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.smart_load_set_from_device_get, cmd_type_s2c: self.smart_load_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def smart_load_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T353/354

        Kwargs for T353:
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w | |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs for T354：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | smart_load1_voltage | int | 智能负载1电压采样,单位：0.1V |
            | smart_load2_voltage | int | 智能负载2电压采样，单位：0.1V |
            | charging_current_for_car | int | 车充电流，单位：0.1A |
            | charging_power_for_car | int | 车充功率，单位：0.1w | |
            | charging_out_energy_for_car | int | 车充输出电量，单位：wh |
            | charging_in_energy_for_car | int | 车充输入电量，单位：wh |
            | smart_load1_current | int | 智能负载1电流采样，单位：0.1A |
            | smart_load2_current | int | 智能负载2电流采样，单位：0.1A |
            | smart_load1_power | int | 智能负载1输出功率，单位：w  |
            | smart_load2_power | int | 智能负载2输出功率，单位：w  |
            | smart_load1_energy | int | 智能负载1输出电量，单位：wh |
            | smart_load2_energy | int | 智能负载2输出电量，单位：wh |
            | charged_energy_for_car_with_fixed_energy_mode | int | 车充定量供电已充电量，单位：wh |
            | generator_power | int | 发电机功率，单位：0.1kw |
            | generator_current | int | 发电机电流，单位：0.1A |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | generator_frequency | int | 发电机频率，单位：0.1Hz |
            | generator_generated_energy | int | 发电机当日总发电量，单位：0.1kw |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 353/354 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=354 | rx_time_window=300 | filter_mode=and |
        | ${status} | Smart Load Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.smart_load_set_from_device_get_check, cmd_type_s2c: self.smart_load_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
