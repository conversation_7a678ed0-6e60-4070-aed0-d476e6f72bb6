from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 研发参数读取与设置

cmd_type_c2s = 1801

cmd_type_s2c = 1802

attr_map_c2s = {

    **attr_map_common_opt,

    'topSoc': ('top_soc', 'float'),
    'inhSocNormal': ('low_soc', 'float'),
    'inhSocCold': ('disable_charging_cold_soc', 'float'),
    'InhSocExtCold': ('disable_charging_extreme_cold_soc', 'float'),
    'normalTemp': ('normal_temp', 'float'),
    'coldTemp': ('cold_temp', 'float'),
    'extColdTemp': ('extreme_temp', 'float'),
    'startHeatBatTemp': ('start_heating_temp', 'float'),
    'stopHeatBatTemp': ('stop_heating_temp', 'float'),
    'batMaxChFactor': ('max_battery_charging_factor', 'float'),
    'delayToChTime': ('delay_charging_time', 'int'),
    'blackStartOnOff': ('black_start_enabled', 'int'),

    'startGenSoc': ('generator_start_soc', 'float'),
    'stopGenSoc': ('generator_stop_soc', 'float'),
    'InhUseFlag': ('system_disable_flag', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDebugGlobal(Base):

    def local_debug_global_set(self, *args, **kwargs):
        """  local debug global parameters set-T1801
        Args：
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败,2:设置失败 |
            | top_soc | float | IBG管理BMS映射到用户100%SOC的最高SOC值,0-100 |
            | low_soc | float | IBG管理BMS映射用户0%SOC的最低值,0-100 |
            | disable_charging_cold_soc | float | 在寒冷天气，禁止放电的SOC值,0-100 |
            | disable_charging_extreme_cold_soc | float | 在极寒天气，禁止放电的SOC值,0-100 |
            | normal_temp | float | 正常低温,-30~100 |
            | cold_temp | float | 寒冷低温, -30~100 |
            | extreme_temp | float | 极寒低温,-30~100 |
            | start_heating_temp | float | 低于此温度，启动加热, 0~20 |
            | stop_heating_temp | float | 高于此温度，关闭加热, 0~20 |
            | max_battery_charging_factor | float | 池最大充电功率系数，基准值为INFINITE功率值：5000w, 0~1.0 |
            | delay_charging_time | int | 离网-->并网时，延时充电时长, 0~60,单位：分钟 |
            | black_start_enabled | int | 系统黑启动soc，0：开启，1：关闭 |
            | generator_start_soc | float | 低于该值soc，启动发电机， 0~100 |
            | generator_stop_soc | float | 高于该值，关闭发电机，0~100 |
            | system_disable_flag | int | 系统禁止使用标志。特殊情况,0:系统可用，1：系统禁用 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | top_soc | float | IBG管理BMS映射到用户100%SOC的最高SOC值,0-100 |
            | low_soc | float | IBG管理BMS映射用户0%SOC的最低值,0-100 |
            | disable_charging_cold_soc | float | 在寒冷天气，禁止放电的SOC值,0-100 |
            | disable_charging_extreme_cold_soc | float | 在极寒天气，禁止放电的SOC值,0-100 |
            | normal_temp | float | 正常低温,-30~100 |
            | cold_temp | float | 寒冷低温, -30~100 |
            | extreme_temp | float | 极寒低温,-30~100 |
            | start_heating_temp | float | 低于此温度，启动加热, 0~20 |
            | stop_heating_temp | float | 高于此温度，关闭加热, 0~20 |
            | max_battery_charging_factor | float | 池最大充电功率系数，基准值为INFINITE功率值：5000w, 0~1.0 |
            | delay_charging_time | int | 离网-->并网时，延时充电时长, 0~60,单位：分钟 |
            | black_start_enabled | int | 系统黑启动soc，0：开启，1：关闭 |
            | generator_start_soc | float | 低于该值soc，启动发电机， 0~100 |
            | generator_stop_soc | float | 高于该值，关闭发电机，0~100 |
            | system_disable_flag | int | 系统禁止使用标志。特殊情况,0:系统可用，1：系统禁用 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Debug Global Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_debug_global_set_result_check(self, _response, **kwargs):
        """  local debug global parameters get result check-T1802

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败,2:设置失败 |
            | top_soc | float | IBG管理BMS映射到用户100%SOC的最高SOC值,0-100 |
            | low_soc | float | IBG管理BMS映射用户0%SOC的最低值,0-100 |
            | disable_charging_cold_soc | float | 在寒冷天气，禁止放电的SOC值,0-100 |
            | disable_charging_extreme_cold_soc | float | 在极寒天气，禁止放电的SOC值,0-100 |
            | normal_temp | float | 正常低温,-30~100 |
            | cold_temp | float | 寒冷低温, -30~100 |
            | extreme_temp | float | 极寒低温,-30~100 |
            | start_heating_temp | float | 低于此温度，启动加热, 0~20 |
            | stop_heating_temp | float | 高于此温度，关闭加热, 0~20 |
            | max_battery_charging_factor | float | 池最大充电功率系数，基准值为INFINITE功率值：5000w, 0~1.0 |
            | delay_charging_time | int | 离网-->并网时，延时充电时长, 0~60,单位：分钟 |
            | black_start_enabled | int | 系统黑启动soc，0：开启，1：关闭 |
            | generator_start_soc | float | 低于该值soc，启动发电机， 0~100 |
            | generator_stop_soc | float | 高于该值，关闭发电机，0~100 |
            | system_disable_flag | int | 系统禁止使用标志。特殊情况,0:系统可用，1：系统禁用 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Debug Global Set |
        | ${status} = | Local Debug Global Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
