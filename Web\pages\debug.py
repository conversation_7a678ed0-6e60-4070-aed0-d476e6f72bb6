from time import (sleep, time, strptime, mktime)
from Web.base import (Base, By)
from robot.api import logger
from functools import partial
from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver import ActionChains


common_element = f"//div[@class='tableBox dark']//div[@class='el-scrollbar']//tbody"
element_generate = partial(lambda x: f"{common_element}//td[{x}]//span")


class Debug(Base):

    def debug_enable(self, device='10080008B00A22151991', component='EMS', VID='374', value="""{"upgradeSetBmsIg":[1,1]}#""", first_operation='on'):

        logger.info(f"the user kwargs is:device:{device},component:{component},VID:{VID},value:{value}，first_operation：{first_operation}")

        if first_operation == 'on':

            nav_list = ['mon-icon-shebeiguanli-1']

            for i in nav_list:

                if i == 'mon-icon-shebeiguanli-1':

                    ele = self.find_by_CLASS_NAMEs('mon-icon-shebeiguanli')[0]

                    ele.click()
                    sleep(2)
                    break

            m = self.find_by_CLASS_NAMEs('el-menu-item')

            for i in m:

                firm_list = ['设备列表']

                if i.text in firm_list:
                    i.click()
                    sleep(3)
                    break

            logger.info(self.get_page_source()
                        )
            ele = self.find_by_Xpath("""//input[contains(@placeholder,"aGate名称、aGate序列号、")]""")

            # ele = self.find_by_Xpath("""//input[contains(@placeholder,"aGate名称、aGate序列号、...")]""")

            ele.send_keys(device)

            ele.click()

            ele = self.find_by_Xpath("""//span[contains(.,"搜索")]""").click()
            sleep(3)

            ele = self.find_by_Xpath("""//*[@id="fwh"]/div/section/section/main/div/div[3]/div[2]/div[1]/div[1]/div[3]/div/div[1]/div/table/tbody/tr/td[22]/div/div[3]""")

            if ele.text == '设置':

                sleep(3)
                ele.click()

            nn = """//div[@id="tab-Debugging"]"""

            ele = self.find_by_Xpath(nn)

            ele.click()

            sleep(5)

            if component == 'EMS':

                ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[1]/div[3]/span[2]""")  # choose EMS

                ele.click()

                sleep(3)

                ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[4]/div/div[1]/div[2]/div""")  # enable EMS
                ele.click()

            elif component == 'BMS':

                ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[2]/div/div[1]/div[2]/div""")  # enable BMS

            sleep(3)

            self.execute_script("document.body.style.zoom='80%'")

        sleep(5)

        # ele = self.find_by_Xpath("""//input[contains(@placeholder,"请选择编号")]""")  # choose aPower

        # ele.click()

        # sleep(1)

        # ele = self.find_by_Xpath("""//span[contains(.,'ULG_TEST_SamuelF1')]""")

        # ele.click()

        # ele = self.find_by_Xpath("""//input[contains(@placeholder,"请输入编号")]""")  # choose VID for BMS

        ele = self.find_by_Xpath("""//input[contains(@placeholder,"请输入VID/PID数值")]""")  # choose VID for EMS

        string = f"""{VID}"""

        ele.clear()

        for i in string:

            ele.send_keys(i)

        sleep(4)

        ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[4]/div/div[3]/div/label[2]/span[1]/span""")  # 设置

        # ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[2]/div/div[4]/div[1]/label[2]/span[1]/span""") # BMS

        ele.click()

        sleep(3)

        ele = self.find_by_Xpath("""//input[contains(@placeholder,"请选择数据类型")]""")
        ele.click()

        sleep(1)

        ele = self.find_by_Xpath("""//span[contains(.,'String数据')]""")

        ele.click()

        sleep(1)

        ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[4]/div/div[5]/div[2]/div/div/input""")  # choose

        string = f"""{value}"""

        ele.clear()  # throw out InvalidElementStateException error, report element disabled error, shall add isEnabled() for the ele here...

        ele.send_keys(string)

        sleep(2)

        ele = self.find_by_Xpath("""//*[@id="pane-Debugging"]/div/div[2]/div[5]/button""")
        ele.click()

        sleep(15)
