from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from robot.api import logger
from .locator import get_locator


class Generator(Base):
    def generator_guide_page(self):
        """ Go to system->settings->generator page
        Kwargs:
                | None |
        Return:
                | None |
        Examples:
                | generator guide page |
        """
        locators = ['locator_generator_help',
                    'locator_help_guide',
                    'locator_generator_installation',
                    'locator_gen_instruction_page_quit',
                    'locator_gen_guide_page_quit',
                    'locator_gen_instructions',
                    'locator_gen_instruction_page_quit'
                    ]

        for locator in locators:
            self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._general_backward_upper_page()

    def get_generator_parameters(self):
        """get generato config parameters
        Kwargs:
            | None|
        Return:
            | dict |
        Examples:
            | ${result} | Get Generator Parameters |
        """
        ret_dict = {}
        value = "locator_generator_para_page"
        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        content_desc = ele.get_attribute('content-desc')
        lines = content_desc.split('\n')

        for i in range(0, len(lines), 2):
            key = lines[i]
            value = lines[i + 1] if i + 1 < len(lines) else ""
            ret_dict[key] = value
        logger.info(f'OK,the generator parameter:{ret_dict}')
        if not ret_dict:
            self.save_screenshot()
            raise ValueError('Fail to get parameter !')
        return ret_dict

    def config_gen_opera_range(self, locator, left_start, left_target, right_start, right_target, duration=1000):

        element = self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        start_x = element.location['x']
        width = element.size['width']
        center_y = element.location['y'] + element.size['height'] // 2  # 控件的中心 y 坐标

        left_slider_x = start_x + width * left_start
        left_target_x = start_x + width * left_target

        right_slider_x = start_x + width * right_start
        right_target_x = start_x + width * right_target

        self.driver.swipe(start_x=left_slider_x, start_y=center_y, end_x=left_target_x, end_y=center_y, duration=duration)
        self.driver.swipe(start_x=right_slider_x, start_y=center_y, end_x=right_target_x, end_y=center_y, duration=duration)

    def seek_bar_handle(self, locator, direction, distance=300, duration=1000):
        value = locator
        element = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        start_x = element.location['x'] + element.size['width'] // 2
        start_y = element.location['y'] + element.size['height'] // 2

        if direction == 'up':
            end_x = start_x
            end_y = start_y - distance
        elif direction == 'down':
            end_x = start_x
            end_y = start_y + distance
        elif direction == 'left':
            end_x = start_x - distance
            end_y = start_y
        elif direction == 'right':
            end_x = start_x + distance
            end_y = start_y
        else:
            raise ValueError("direction parameter must be 'up', 'down', 'left', 'right' 之一")

        self.driver.swipe(start_x, start_y, end_x, end_y, duration)

    def config_generator_settings_page(self, **kwargs):
        """ Go to system->settings->generator page
        Kwargs:
                | oper_mode | string | 控制模式: Manual/Schedule |
                | exe_flag | bool | 油机演练:true/false |
                | duration | int | 油机演练时长: 20min |
                | interval | string | 油机演练间隔: 2 week |
                | day in week | string | Friday |
                | start time | string | 开始时间: 19:00 |
                | start control type | string | Voltage sense/ATS/Dry contact |
                | generator model | string | generator1 |
                | generator rated power | int | 1.0~40.0,step:0.1,单位:kW |
                | best power duty | int | 10-100,step:1,单位:% |
                | alarm delay after start failure | int | 300-3600,step:1,单位:秒 |
        Return:
                | None |
        Examples:
                | config smart circuits Page | select=240 | GUIDE=False | Circuit_name=circui 1t
        """
        value = 'locator_generator_settings'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        logger.info(f"the user parameters:kwargs:{kwargs}")

        exe_flag = kwargs.pop('exe_flag', None)
        value = 'locator_generator_exercise'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        if exe_flag is not None:
            locator_name = 'locator_generator_exe_switch_off'
            status = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            if exe_flag == 'true':
                if status == 'false':

                    x_ration = 900 / 1072
                    y_ration = 342 / 2276

                    self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)
                else:
                    pass

                operations = [
                    ("locator_gen_exe_duration", '10min', "locator_gen_exe_seekbar"),
                    ("locator_gen_exe_interval", '2 weeks', "locator_gen_exe_seekbar"),
                    ("locator_gen_exe_dayinweek", 'Friday', "locator_gen_exe_seekbar"),
                    ("locator_gen_exe_start_time", '01', "locator_gen_exe_start_time_hour"),
                    ("locator_gen_exe_start_time", '02', "locator_gen_exe_start_time_min")
                ]

                for locator, value, target_locator in operations:
                    self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
                    self.set_value_in_scroll_element(value=value, locator_fun=get_locator, target_locator=target_locator)
            elif status == 'true' and exe_flag == 'false':
                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
            self._system_confirm_locator()

        oper_mode = kwargs.pop('oper_mode', 'Manual')

        if oper_mode == 'Manual':
            x_ration = 473 / 1072
            y_ration = 604 / 2276

            self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)

            value = 'locator_gen_manual_turn_on'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif oper_mode == 'Schedule':

            x_ration = 455 / 1072
            y_ration = 879 / 2276

            self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)
            self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)

            try:
                value = 'locator_delete_oper_time'
                locator_name = "locator_delete_schedule_confirm"
                for _ in range(2):
                    self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
                    self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, retry=2, will_do_loading_detect=True, click=True)

            except Exception as e:
                logger.info(f"Sorry, could not find the element for '{value}'. Continuing with the next steps. Error: {str(e)}")

            for times in [[1, 1, 8, 8], [10, 10, 1, 1]]:
                value = 'locator_schedule_oper_time_add'
                self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
                locator_start_end_time = ['locator_start_time_hour', 'locator_start_time_min', 'locator_end_time_hour', 'locator_end_time_min']
                for item1, item2 in zip(locator_start_end_time, times):
                    self.seek_bar_handle(item1, direction='up', distance=100 * int(item2))
                self.confirm_cancel_diag_handling(value=True)

            # config operation slide control left and right
            value = 'locator_schedule_oper_range'
            self.config_gen_opera_range(value, left_start=0.1, left_target=0.2, right_start=0.9, right_target=0.8)

            value = 'locator_schedule_save'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        value = 'locator_performance_para'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        value = 'locator_start_control_type'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        self.set_value_in_scroll_element(value='Voltage sense', locator_fun=get_locator, target_locator="locator_control_type_seekbar")

        gen_model = kwargs.pop('gen_model', None)
        if gen_model is not None:
            value1 = 'locator_generator_model'
            value2 = 'locator_edit_gen_mod_name'
            self.handle_locator_input(value1, value2, gen_model)

        rate_power = kwargs.pop('rate_power', 13.0)
        if rate_power is not None:
            value1 = 'locatoer_gen_rated_power'
            value2 = 'locator_gen_para_edit'
            self.handle_locator_input(value1, value2, rate_power)

        power_duty = kwargs.pop('power_duty', 75)
        if rate_power is not None:
            value1 = 'locator_best_power_duty'
            value2 = 'locator_gen_para_edit'
            self.handle_locator_input(value1, value2, power_duty)

        Alarm_delay = kwargs.pop('Alarm_delay', 2000)
        if rate_power is not None:
            value1 = 'locator_alarm_delay'
            value2 = 'locator_gen_para_edit'
            self.handle_locator_input(value1, value2, Alarm_delay)

        self._system_confirm_locator()
        self._general_backward_upper_page()

    def handle_locator_input(self, locator, edit_locator, value):
        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        locator = get_locator(edit_locator)
        eles = self.find_element(*locator, multiple=True)

        eles[0].click()
        eles[0].clear()
        eles[0].send_keys(value)
        self._system_confirm_locator()

    def config_gen_opera_range(self, locator, left_start, left_target, right_start, right_target, duration=1000):

        element = self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        start_x = element.location['x']
        width = element.size['width']
        center_y = element.location['y'] + element.size['height'] // 2

        left_slider_x = start_x + width * left_start
        left_target_x = start_x + width * left_target

        right_slider_x = start_x + width * right_start
        right_target_x = start_x + width * right_target

        self.driver.swipe(start_x=left_slider_x, start_y=center_y, end_x=left_target_x, end_y=center_y, duration=duration)
        self.driver.swipe(start_x=right_slider_x, start_y=center_y, end_x=right_target_x, end_y=center_y, duration=duration)
