from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# MPTT参数

cmd_type_c2s = 1717

cmd_type_s2c = 1718

attr_map_c2s = {

    **attr_map_common_opt,

    'id': ('', 'int'),
    'mpptActPower': ('out_power', 'int'),
    'mpptRunSta': ('run_status', 'int'),
    'mpptMolVol': ('pv_voltage', 'list'),
    'mpptMolCul': ('pv_current', 'list'),
    'mpptMolArcStr': ('pv_arc_strength', 'list'),
    'mpptAfciCur': ('pv_afci_current', 'list'),
    'mpptCyTmp': ('cavity_temp', 'float'),
    'mpptHs1Tmp': ('heater1_temp', 'float'),
    'mpptHs2Tmp': ('heater1_tem2', 'float'),
    'mpptIrCty': ('insu_res_conduct', 'int'),

    'mpptAuxVol': ('aux_voltage', 'float'),
    'mpptPvVol': ('pv_side_bus_voltage', 'float'),
    'mpptPeVol': ('pe_side_bus_voltage', 'float'),
    'afciHdVer': ('afci_arc_hw_version', 'int'),
    'afciStVer': ('afci_arc_sw_version', 'int'),
    'afciAlgVer': ('afci_arc_algriothm_version', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalMppt(Base):

    def local_mppt_set(self, *args, **kwargs):
        """  local MPPT set-T1717
        Args：
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败，2：设置失败 |
            | id | int | PE所属ID |
            | out_power | int | 输出功率，单位：w |
            | run_status | int | 运行状态 |
            | pv_voltage | float list | PV1~PV4电压，单位：V |
            | pv_current | float list | PV1~PV4电流，单位：A |
            | pv_arc_strength | int list | PV1~PV4拉弧强度 |
            | pv_afci_current | int list | PV1~PV4 AFCI电流，单位： mA |
            | cavity_temp | float | 腔温，单位：摄氏度 |
            | heater1_temp | float | 散热器1温度，单位：摄氏度 |
            | heater1_tem2 | float | 散热器2温度，单位：摄氏度 |
            | insu_res_conduct | int | 绝缘阻抗电阻，单位：Kohm |
            | aux_voltage | float | 12V辅源电压，单位：V |
            | pv_side_bus_voltage | float | PV侧BUS电压，单位：V |
            | pe_side_bus_voltage | float | PE侧BUS电压，单位：V |
            | afci_arc_hw_version | int | AFCI拉弧硬件版本 |
            | afci_arc_sw_version | int | AFCI拉弧软件版本 |
            | afci_arc_algriothm_version | int | AFCI 拉弧算法版本 |
        Kwargs:
            | opt | int | 操作，0:查询，1：清除 |
            | id | int | PE所属ID |
            | out_power | int | 输出功率，单位：w |
            | run_status | int | 运行状态 |
            | pv_voltage | float list | PV1~PV4电压，单位：V |
            | pv_current | float list | PV1~PV4电流，单位：A |
            | pv_arc_strength | int list | PV1~PV4拉弧强度 |
            | pv_afci_current | int list | PV1~PV4 AFCI电流，单位： mA |
            | cavity_temp | float | 腔温，单位：摄氏度 |
            | heater1_temp | float | 散热器1温度，单位：摄氏度 |
            | heater1_tem2 | float | 散热器2温度，单位：摄氏度 |
            | insu_res_conduct | int | 绝缘阻抗电阻，单位：Kohm |
            | aux_voltage | float | 12V辅源电压，单位：V |
            | pv_side_bus_voltage | float | PV侧BUS电压，单位：V |
            | pe_side_bus_voltage | float | PE侧BUS电压，单位：V |
            | afci_arc_hw_version | int | AFCI拉弧硬件版本 |
            | afci_arc_sw_version | int | AFCI拉弧软件版本 |
            | afci_arc_algriothm_version | int | AFCI 拉弧算法版本 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local MPPT Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_mppt_set_result_check(self, _response, **kwargs):
        """  local MPPT set result check-T1718

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败，2：设置失败 |
            | id | int | PE所属ID |
            | out_power | int | 输出功率，单位：w |
            | run_status | int | 运行状态 |
            | pv_voltage | float list | PV1~PV4电压，单位：V |
            | pv_current | float list | PV1~PV4电流，单位：A |
            | pv_arc_strength | int list | PV1~PV4拉弧强度 |
            | pv_afci_current | int list | PV1~PV4 AFCI电流，单位： mA |
            | cavity_temp | float | 腔温，单位：摄氏度 |
            | heater1_temp | float | 散热器1温度，单位：摄氏度 |
            | heater1_tem2 | float | 散热器2温度，单位：摄氏度 |
            | insu_res_conduct | int | 绝缘阻抗电阻，单位：Kohm |
            | aux_voltage | float | 12V辅源电压，单位：V |
            | pv_side_bus_voltage | float | PV侧BUS电压，单位：V |
            | pe_side_bus_voltage | float | PE侧BUS电压，单位：V |
            | afci_arc_hw_version | int | AFCI拉弧硬件版本 |
            | afci_arc_sw_version | int | AFCI拉弧软件版本 |
            | afci_arc_algriothm_version | int | AFCI 拉弧算法版本 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local MPPT Set |
        | ${status} = | Local MPPT Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
