from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 2030.5URI查询设置

cmd_type_c2s = 1205

cmd_type_s2c = 1206

attr_map_c2s = {

    **attr_map_common_opt,

    'enable': ('', 'int'),
    'uri': ('', 'string'),
    'dcap2030_5': ('2030_5_server', 'string'),
    'lfdi': ('', 'string'),

    'sfdi': ('', 'string'),
    'pin': ('', 'string'),
    'status2030_5': ('2030_5_status', 'int'),

    'ip': ('sun_md_ip', 'string'),
    'port': ('sun_md_port', 'int'),
    'sunsMdEn': ('sun_md_enabled', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalProfile20305Set(Base):

    def local_20305_set(self, *args, **kwargs):
        """  local 2030.5 set-T1205
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败，2：设置失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | enable | int | 2030.5使能，0：去使能，1：使能 |
            | uri | string | 2030.5 URI，<=128Byes |
            | 2030_5_server | string | 2030.5服务器属性地址,默认/sep2/dcap |
            | lfdi | string | LFDI,长证书标识码 |
            | sfdi | string | SFDI,短证书标识码 |
            | pin | string | PIN |
            | 2030_5_status | int | 2030.5服务器状态，0：未连接；1：成功连接2030服务器服务器连接状态 |
            | suns_md_ip | string | Modbus IP |
            | suns_md_port | int |  Modbus IP port |
            | suns_md_enabled | int |  Modbus使能,0：使能；1：不使能 |
        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | enable | int | 2030.5使能，0：去使能，1：使能 |
            | uri | string | 2030.5 URI，<=128Byes |
            | 2030_5_server | string | 2030.5服务器属性地址,默认/sep2/dcap |
            | lfdi | string | LFDI,长证书标识码 |
            | sfdi | string | SFDI,短证书标识码 |
            | pin | string | PIN |
            | 2030_5_status | int | 2030.5服务器状态，0：未连接；1：成功连接2030服务器服务器连接状态 |
            | suns_md_ip | string | Modbus IP |
            | suns_md_port | int |  Modbus IP port |
            | suns_md_enabled | int |  Modbus使能,0：使能；1：不使能 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local 20305 Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_20305_set_result_check(self, _response, **kwargs):
        """  local 2030.5 set result check-T1206

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败，2：设置失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | enable | int | 2030.5使能，0：去使能，1：使能 |
            | uri | string | 2030.5 URI，<=128Byes |
            | 2030_5_server | string | 2030.5服务器属性地址,默认/sep2/dcap |
            | lfdi | string | LFDI,长证书标识码 |
            | sfdi | string | SFDI,短证书标识码 |
            | pin | string | PIN |
            | 2030_5_status | int | 2030.5服务器状态，0：未连接；1：成功连接2030服务器服务器连接状态 |
            | suns_md_ip | string | Modbus IP |
            | suns_md_port | int |  Modbus IP port |
            | suns_md_enabled | int |  Modbus使能,0：使能；1：不使能 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local 20305 Set |
        | ${status} = | Local 20305 Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
