from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 设备事件推送

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 310

cmd_type_c2s = 309

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    'event': ('', 'int'),
    'startTime': ('start_time', 'string'),
    'msgType': ('message_type', 'int'),
    'reason': ('', 'string'),
    'eReason': ('detailed_reason', 'int'),
    'nextMode': ('next_mode', 'int'),
    'swDetail': ('sw_detail', 'json'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DevicePush(object):

    def device_event_push(self, *args, **kwargs):

        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        build_tx_dict(kwargs, attr_map_c2s, com_dict_c2s, com_dict_s2c)

        kwargs.update(c2s_cmd_type=cmd_type_c2s)

        kwargs.update(s2c_cmd_type=cmd_type_s2c)

        return self._get(*args, **kwargs)

    def device_event_push_from_aws_get(self, *args, **kwargs):
        """  device event push from AWS,the message get-T310

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Event Push From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_event_push_from_aws_get_check(self, **kwargs):
        """  device event push  from AWS,the message get check-T310

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Device Event Push From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_event_push_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_event_push_from_device_get(self, *args, **kwargs):
        """   device event push from device,the message get-T309

        Args:
            | event | int | 1：市电停电；2：市电恢复；3：离网系统电量偏低；4：离网系统电量为0，进入休眠等待黑启动；5：黑启动失败；6：EPO停机；（不推送）7：EPO解除；（不推送）8：发电机投入；9：发电机退出；10：风暴备电开始；11：风暴备电结束；12：固件升级开始；13：固件升级结束；14：系统停止；15：aPower停止；16：离网过载保护；17：失电负载切除；18：低电量切断智能负载；19：恢复智能负载；（不推送）20：离网超发断光伏；（不推送）21：离网恢复光伏；（不推送）22：启动发电机；（不推送）23：停止发电机（不推送）24：发电机启动异常；25：发电机输出异常；26：系统电量耗尽进入深度休眠；27：输出过载 28：发电机光伏尝试 29：电池耗尽与黑启动尝试 30：离网过载锁定 31：电量恢复至20% 32：车充定量已充满事件 33：油机演习成功 34：油机演习失败 35：油机不稳定工作事件 36：油机不稳定工作事件恢复 37：回路异常休眠 38：紧急电源接入 39：紧急电源退出 40：紧急备电退出 41：V2L输出过载告警 42：V2L输出过载退出 43：mppt拉弧故障 |
            | start_time | string | 开始时间，<=20Bytes,例如2020-10-10 13:14:50 |
            | message_type | int | 0：在线；1：离线 |
            | reason | int | network instability（离线状态该字段有效）|
            | detailed_reason | int | 该字段在event=40有效 |
            | next_mode | int | 该字段在event=40有效 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Device Event Push From Device Get | event |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_event_push_from_device_get_check(self, **kwargs):
        """  device event push from device,the message get check-T309

        Kwargs:
            | event | int | 1：市电停电；2：市电恢复；3：离网系统电量偏低；4：离网系统电量为0，进入休眠等待黑启动；5：黑启动失败；6：EPO停机；（不推送）7：EPO解除；（不推送）8：发电机投入；9：发电机退出；10：风暴备电开始；11：风暴备电结束；12：固件升级开始；13：固件升级结束；14：系统停止；15：aPower停止；16：离网过载保护；17：失电负载切除；18：低电量切断智能负载；19：恢复智能负载；（不推送）20：离网超发断光伏；（不推送）21：离网恢复光伏；（不推送）22：启动发电机；（不推送）23：停止发电机（不推送）24：发电机启动异常；25：发电机输出异常；26：系统电量耗尽进入深度休眠；27：输出过载 28：发电机光伏尝试 29：电池耗尽与黑启动尝试 30：离网过载锁定 31：电量恢复至20% 32：车充定量已充满事件 33：油机演习成功 34：油机演习失败 35：油机不稳定工作事件 36：油机不稳定工作事件恢复 37：回路异常休眠 38：紧急电源接入 39：紧急电源退出 40：紧急备电退出 41：V2L输出过载告警 42：V2L输出过载退出 43：mppt拉弧故障 |
            | start_time | string | 开始时间，<=20Bytes,例如2020-10-10 13:14:50 |
            | message_type | int | 0：在线；1：离线 |
            | reason | int | network instability（离线状态该字段有效）|
            | detailed_reason | int | 该字段在event=40有效 |
            | next_mode | int | 该字段在event=40有效 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Device Event Push From Device Get Check | event=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_event_push_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_event_push_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T309/310

        Args for T309:
            | event | int | 1：市电停电；2：市电恢复；3：离网系统电量偏低；4：离网系统电量为0，进入休眠等待黑启动；5：黑启动失败；6：EPO停机；（不推送）7：EPO解除；（不推送）8：发电机投入；9：发电机退出；10：风暴备电开始；11：风暴备电结束；12：固件升级开始；13：固件升级结束；14：系统停止；15：aPower停止；16：离网过载保护；17：失电负载切除；18：低电量切断智能负载；19：恢复智能负载；（不推送）20：离网超发断光伏；（不推送）21：离网恢复光伏；（不推送）22：启动发电机；（不推送）23：停止发电机（不推送）24：发电机启动异常；25：发电机输出异常；26：系统电量耗尽进入深度休眠；27：输出过载 28：发电机光伏尝试 29：电池耗尽与黑启动尝试 30：离网过载锁定 31：电量恢复至20% 32：车充定量已充满事件 33：油机演习成功 34：油机演习失败 35：油机不稳定工作事件 36：油机不稳定工作事件恢复 37：回路异常休眠 38：紧急电源接入 39：紧急电源退出 40：紧急备电退出 41：V2L输出过载告警 42：V2L输出过载退出 43：mppt拉弧故障 |
            | start_time | string | 开始时间，<=20Bytes,例如2020-10-10 13:14:50 |
            | message_type | int | 0：在线；1：离线 |
            | reason | int | network instability（离线状态该字段有效）|
            | detailed_reason | int | 该字段在event=40有效 |
            | next_mode | int | 该字段在event=40有效 |
        Args for T310：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 309/310 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=310 | rx_time_window=300 |
        | ${status} | Device Event Push From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_event_push_from_device_get, cmd_type_s2c: self.device_event_push_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_event_push_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T309/310

        Kwargs for T309:
            | event | int | 1：市电停电；2：市电恢复；3：离网系统电量偏低；4：离网系统电量为0，进入休眠等待黑启动；5：黑启动失败；6：EPO停机；（不推送）7：EPO解除；（不推送）8：发电机投入；9：发电机退出；10：风暴备电开始；11：风暴备电结束；12：固件升级开始；13：固件升级结束；14：系统停止；15：aPower停止；16：离网过载保护；17：失电负载切除；18：低电量切断智能负载；19：恢复智能负载；（不推送）20：离网超发断光伏；（不推送）21：离网恢复光伏；（不推送）22：启动发电机；（不推送）23：停止发电机（不推送）24：发电机启动异常；25：发电机输出异常；26：系统电量耗尽进入深度休眠；27：输出过载 28：发电机光伏尝试 29：电池耗尽与黑启动尝试 30：离网过载锁定 31：电量恢复至20% 32：车充定量已充满事件 33：油机演习成功 34：油机演习失败 35：油机不稳定工作事件 36：油机不稳定工作事件恢复 37：回路异常休眠 38：紧急电源接入 39：紧急电源退出 40：紧急备电退出 41：V2L输出过载告警 42：V2L输出过载退出 43：mppt拉弧故障 |
            | start_time | string | 开始时间，<=20Bytes,例如2020-10-10 13:14:50 |
            | message_type | int | 0：在线；1：离线 |
            | reason | int | network instability（离线状态该字段有效）|
            | detailed_reason | int | 该字段在event=40有效 |
            | next_mode | int | 该字段在event=40有效 |
        Kwargs for T310：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 309/310 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=309 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Event Push From Msg Get Check | device_event_push=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_event_push_from_device_get_check, cmd_type_s2c: self.device_event_push_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
