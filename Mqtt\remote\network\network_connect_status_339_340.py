from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 网络连接状态查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 339

cmd_type_c2s = 340

attr_map_s2c = {

    **attr_map_common_opt,

}

attr_map_c2s = {

    **attr_map_s2c,

    'routerStatus': ('router', 'int'),
    'netStatus': ('net', 'int'),
    'awsStatus': ('aws', 'int'),
    'EthConnectRouterStatus': ('Eth', 'int'),
    'wifiConnectRouterStatus': ('WiFi', 'int'),
    '4GConnectBSStatus': ('4G', 'int'),
    'WifiSignalStrength': ('WiFi_signal', 'int'),
    '4GSignalStrength': ('4G_signal', 'int'),
    'currentNetType': ('current_network', 'int')
}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class NetworkConnectStatus(object):

    def network_connect_status_get(self, *args, **kwargs):
        """  network connect status get-T339/340

        Args：
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Connect Status Get | opt=0 |
        | ${status} = | Network Connect Status Get | current_network | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def network_connect_status_get_result_check(self, _response, **kwargs):
        """ network connect status get result check-T340

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Network Connect Status Get | opt=0 |
        | ${status} = | Network Connect Status Get Result Check | ${response} | current_network=2  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def network_connect_status_get_from_aws_get(self, *args, **kwargs):
        """  network connect status get from AWS,the message get-T339

        Args：
            | opt | int | 操作类型，0:查询 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Connect Status Get From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def network_connect_status_get_from_aws_get_check(self, **kwargs):
        """  network connect status get from AWS,the message get check-T339

        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Connect Status Get From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_connect_status_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_connect_status_get_from_device_get(self, *args, **kwargs):
        """   network connect status get response from device,the message get-T340

        Args：
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Connect Status Get From Device Get | current_network |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def network_connect_status_get_from_device_get_check(self, **kwargs):
        """  network connect status get response from device,the message get check-T340

        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Network Connect Status Get From Device Get Check | current_network=2 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_connect_status_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_connect_status_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T339/340

        Args for T339:
            | opt | int | 操作类型，0:查询 |
        Args for T340：
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 339/340 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=339 | rx_time_window=300 |
        | ${status} | Network Connect Status Get From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_connect_status_get_from_device_get, cmd_type_s2c: self.network_connect_status_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def network_connect_status_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T339/340

        Kwargs for T339:
            | opt | int | 操作类型，0:查询 |
        Kwargs for T340:
            | opt | int | 操作类型，0:查询 |
            | router | int | 路由器连接状态，0：连接；1：未连接；2：密码错误；3：IP获取失败；4：扫描中；5：连接超时 |
            | net | int | 网络连接状态，0：连接；1：未连接 |
            | aws | int | AWS云网络连接状态，0：连接；1：未连接 |
            | Eth | int | 有线网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi | int | WiFi网络连接状态，0：预留；1：已连接；2：未连接 |
            | 4G | int | 4G网络连接状态，0：预留；1：已连接；2：未连接 |
            | WiFi_signal | int | WiFi信号强度，-1：未连接，0-100 |
            | 4G_signal | int | 4G信号强度，-1：未连接，0-100 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 339/340 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=340 | rx_time_window=300 | filter_mode=and |
        | ${status} | Network Connect Status Get From Msg Get Check | current_network=2 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_connect_status_get_from_device_get_check, cmd_type_s2c: self.network_connect_status_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
