from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from time import sleep
from robot.api import logger
from .locator import get_locator


class Settings(Base):

    def go_to_settings_mode_page(self):
        """ Go to system->setting->mode page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Mode Page |
        """
        locator_comm = ('acc_id', "Mode")

        self.find_element(*locator_comm, click=True)

    def exit_setting_mode_page(self):
        """ exit system->setting->mode page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Mode Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_smart_circuits_page(self):
        """ Go to system->setting->smart circuits page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Smart Circuits Page |
        """
        locator_comm = ('acc_id', "Smart Circuits")

        self.find_element(*locator_comm, click=True)

    def exit_setting_smart_circuits_page(self):
        """ exit system->setting->smart circuits page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Smart Circuits Page |
        """
        locator = ("xpath", """//android.widget.ImageView[@content-desc="Smart Circuits\nThe aGate provides three optional Smart Circuits on which the appliances can be configured to automatically start/stop based on the battery reserve, according to the previously set schedule, or operated manually. "]/android.view.View/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_generator_page(self):
        """ Go to system->setting->generator page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Generator Page |
        """
        locator = "Generator"

        locator_comm = ('acc_id', "Generator")

        self.find_element(*locator_comm, click=True)

    def exit_setting_generator_page(self):
        """ exit system->setting->generator page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Generator Page |
        """
        locator = ("xpath", """//android.widget.ImageView[@content-desc="The aGate can connect to a household standby generator via the optional, built-in Generator Module."]/android.view.View/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_v2l_page(self):
        """ Go to system->setting->V2L page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings V2L Page |
        """
        locator_comm = ('acc_id', "Vehicle to Load (V2L)")

        self.find_element(*locator_comm, click=True)

    def exit_setting_v2l_page(self):
        """ exit system->setting->V2L page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting V2L Page |
        """
        locator = ("xpath", """//android.view.View[@content-desc="Vehicle\naGate\nHousehold loads"]/android.view.View[1]/android.widget.ImageView[1]""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_add_accessories_page(self):
        """ Go to system->setting->add accessories page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Add Accessories Page |
        """
        locator_comm = ('acc_id', "Add Accessories")

        self.find_element(*locator_comm, click=True)

    def exit_setting_add_accessories_page(self):
        """ exit system->setting->add accessories page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Add Accessories Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_part_replacement_page(self):
        """ Go to system->setting->part replacement accessories page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Part Replacement Page |
        """
        locator_comm = ('acc_id', "Part Replacement")

        self.find_element(*locator_comm, click=True)

    def exit_setting_part_replacement_page(self):
        """ exit system->setting->part replacement page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Part Replacement Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_tariff_settings_page(self):
        """ Go to system->setting->tariff settings page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Tariff Settings Page |
        """
        locator_comm = ('acc_id', "Tariff Settings")

        self.find_element(*locator_comm, click=True)

    def exit_setting_tariff_settings_page(self):
        """ exit system->setting->tariff settings page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Tariff Settings Page |
        """
        locator = ("xpath", """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView[1]""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_grid_charing_and_energy_export_page(self):
        """ Go to system->setting->grid charing&energy export page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Grid Charging And Energy Export Page |
        """
        locator_comm = ('acc_id', "Grid Charge & Export")

        self.find_element(*locator_comm, click=True)

    def exit_setting_grid_charing_and_energy_export_page(self):
        """ exit system->setting->grid charing&energy export page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Grid Charging And Energy Export Page |
        """
        locator = ("cls_name", "android.widget.ImageView")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_energy_incentives_page(self):
        """ Go to system->setting->energy incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Energy Incentives Page |
        """
        locator_comm = ('acc_id', "Energy Incentives")

        self.find_element(*locator_comm, click=True)

    def exit_setting_energy_incentives_page(self):
        """ exit system->setting->energy incentives page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Energy Incentives Page |
        """
        locator = ("xpath", """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_device_info_page(self):
        """ Go to system->setting->device info page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Device Info Page |
        """
        locator_comm = ('acc_id', "Device Info")

        self.find_element(*locator_comm, click=True)

    def exit_setting_device_info_page(self):
        """ exit system->setting->device info page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Device Info Page |
        """
        self._general_backward_upper_page()

    def go_to_settings_site_information_page(self):
        """ Go to system->setting->site information page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Site Information Page |
        """
        locator_comm = ('acc_id', "Site Information")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_site_information_page(self, waiting_time=3):
        """ exit system->setting->site information page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Site Information Page |
        """
        locator = ("xpath", """//android.widget.Button[@content-desc="Submit"]/android.view.View[1]/android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

        sleep(waiting_time)

    def go_to_settings_resource_address_page(self):
        """ Go to system->setting->2030.5 Resource Address page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Resource Address Page |
        """
        locator_comm = ('acc_id', "2030.5 Resource Address")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_resource_address_page(self):
        """ exit system->setting->2030.5 Resource Address page and go back to the system main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Resource Address Page |
        """
        locator = ("cls_name", """android.widget.ImageView""")

        self._go_to_sub_page(locator=locator)

    def go_to_settings_modbus_page(self):
        """ Go to system->setting->modbus page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Settings Modbus Page |
        """
        locator_comm = ('acc_id', "Modbus")

        self._detect_element_by_scroll(locator_comm)

    def exit_setting_modbus_page(self):
        """ exit system->setting->modbus page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Setting Modbus Page |
        """
        self._general_backward_upper_page()

    def change_hotspot_bluetooth_password(self, wifi_hotspot=None, wifi_password=None, **kwargs):
        """ go to setting->device info->connect password and change hotspot name
        Kwargs:
            | Hotspot_name | string | AP_ULG_TEST_Samuel |
            | Hotspot_Bluetooth_password | string | 12345678 |

        """
        logger.info(f"the circuit parameters:kwargs:{kwargs}")

        locator_comm = ('acc_id', "Settings, Tab 3 of 4")

        self.find_element(*locator_comm, click=True)

        self.swipe_up()

        locator = ("xpath", """(//android.view.View[@content-desc])[10]""")

        self._go_to_sub_page(locator=locator)

        locator_comm = ('acc_id', "Connect Password")

        self.find_element(*locator_comm, click=True)

        locator = ("xpath", """//android.widget.ImageView[@content-desc][1]""")
        self._go_to_sub_page(locator=locator)

        new_hotspot_name = kwargs.pop('hotspot_name', None)

        value = 'locator_circuits_name'
        locator = get_locator(value)

        eles = self.find_element(*locator, multiple=True)

        eles[0].click()
        eles[0].clear()
        eles[0].send_keys(new_hotspot_name)

        self._system_save_locator()

        locator = ("xpath", """//android.widget.ImageView[@content-desc][2]""")
        self._go_to_sub_page(locator=locator)

        new_password = kwargs.pop('new_password', None)

        value1 = 'locator_new_password'
        locator = get_locator(value1)

        eles1 = self.find_element(*locator, multiple=True)

        eles1[0].click()
        eles1[0].send_keys(new_password)

        value2 = 'locator_confirm_password'
        locator = get_locator(value2)

        eles2 = self.find_element(*locator, multiple=True)

        eles2[0].click()
        eles2[0].send_keys(new_password)

        self._system_save_locator()

        locator = ("xpath", """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView""")
        self._go_to_sub_page(locator=locator)

        locator = ("xpath", """//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.widget.ImageView[1]""")
        self._go_to_sub_page(locator=locator)

        locator = ("xpath", """(//android.view.View[@content-desc])[11]""")
        self._go_to_sub_page(locator=locator)

        # self.commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=2, network_mode_change=False)

        # //android.widget.ImageView[@content-desc][1]
        # //android.widget.ImageView[@content-desc][2]
