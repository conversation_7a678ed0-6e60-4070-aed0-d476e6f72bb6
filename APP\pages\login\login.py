from APP.base import Base
from robot.api import logger
from .locator import get_locator


class Login(Base):

    def enable_developer_setting(self, dev_mode="test", proxy_ip=None, proxy_port=None):
        """ Developer options

        Kwargs:
            | dev_mode | string | test(默认）/local/device_test/release,开发者选项 |
            | proxy_ip | string | None（默认），代理IP地址 |
            | proxy_port | string | None（默认），代理port |
        Return:
            | None |
        Examples:
            | Enable Developer Setting | dev_mode=test |
        """
        locator = get_locator('locator_dev_option')

        self.find_element(*locator, click=True)

        if dev_mode == "test":

            locator = get_locator('locator_dev_option_test')

        elif mode == "local":

            locator = get_locator('locator_dev_option_local')

        elif mode == "release":

            locator = get_locator('locator_dev_option_release')

        elif mode == "device_test":

            locator = get_locator('locator_dev_option_device_test')

        self.find_element(*locator, click=True)

        if proxy_ip is not None:

            locator = get_locator('locator_dev_option_proxy_ip')

            self.find_element(*locator, click=True)

        if proxy_port is not None:

            locator = get_locator('locator_dev_option_proxy_port')

            self.find_element(*locator, click=True)

        if proxy_port or proxy_ip:

            locator = get_locator('locator_dev_option_button')

            self.find_element(*locator, click=True)

        # go back to login page
        locator = get_locator('locator_go_back_to_login_button')

        self.find_element(*locator, click=True)

    def enable_visitor_mode(self, role="Homeowner"):
        """ visitor mode

        Kwargs:
            | role | string | Homeowner(默认）/Installer,访客角色 |
        Return:
            | None |
        Examples:
            | Enable Visitor Mode | role=Homeowner |
        """
        locator = get_locator('locator_guest')

        self.find_element(*locator, click=True)

        if role == installer:

            locator = get_locator('locator_guest_installer')

        else:
            locator = get_locator('locator_guest_homeowner')

        self.find_element(*locator, click=True)

    def _already_login_check(self, timeout=0.1):

        locator = get_locator('locator_new')

        ele = self.find_element(*locator, throw_exception=False, capture_screen=False, timeout=timeout)

        if ele:

            logger.info('OK,already login app now!')

            return 'Logged_in'

    def _no_login_with_dev_option_check(self, timeout=0.1):

        locator = get_locator('locator_dev_option')

        ele = self.find_element(*locator, throw_exception=False, capture_screen=False, timeout=timeout)

        if ele:

            logger.info('OK,detect developer options!')

            return 'Login_with_Developer'

    def _no_login_with_user_password_check(self, timeout=0.1):

        locator = get_locator('locator_user_pass')

        ele = self.find_element(*locator, throw_exception=False, capture_screen=False, timeout=timeout)

        if ele:

            logger.info('OK,detect username/password options!')

            return 'Login_with_Username_Password'

    def _no_login_with_dev_option(self, dev_mode=None, proxy_ip=None, proxy_port=None):

        self.enable_developer_setting(dev_mode=dev_mode, proxy_ip=proxy_ip, proxy_port=proxy_port)

        # manual login- check sign in page?

        locator = get_locator('locator_dev_option')

        dev_option = self.find_element(*locator, throw_exception=False, timeout=1)

        if dev_option:

            locator = get_locator('locator_sign_in')

            self.find_element(*locator, click=True)

    def _no_login_with_username_password(self, user_name=None, password=None):

        logger.info('OK,begin to input the username/password!')

        locator = get_locator('locator_user_pass')

        eles = self.find_element(*locator, multiple=True)

        logger.info(f'OK,detect elements:{eles}')

        if self.platform_name == 'Android':

            for index, value in enumerate(eles):

                value.click()

                value.clear()

                if index == 0:

                    text = user_name

                else:

                    text = password

                value.send_keys(text)

            locator = get_locator('locator_sign_in')

            self.find_element(*locator, click=True, retry=2)

        elif self.platform_name == 'iOS':

            for index, value in enumerate(eles):

                value.click()

                value.clear()

                if index == 0:

                    text = user_name

                else:

                    text = password

                value.send_keys(text)

            # locator = get_locator('locator_user_pass')

            # value = self.find_element(*locator, clear=True, send_keys=password)

            # deal with password
            # self.get_element_coordinate_and_action(eles[-1])

            """
            locator = get_locator('locator_password_apple')

            value = self.find_element(*locator, first_click=True, clear=True, send_keys=password)
            """

            locator = get_locator('locator_sign_in')

            ele_sign = self.find_element(*locator)

            target = self.get_element_coordinate_and_action(ele_sign, action=None)

            self.click_by_coordinate(*target)

        if self.model == 'Xiaomi':

            locator = get_locator('locator_xiaomi_permission')

            self.find_element(*locator, click=True, timeout=0.5, throw_exception=False)

    def _detect_login_mode(self, loop_number=3):

        logger.info(f'the user parameters in _detect_login_mode:loop_number:{loop_number}')

        result = None

        for i in range(loop_number):

            logger.info(f"the loop number:{i}")

            method_list = [self._already_login_check, self._no_login_with_dev_option_check, self._no_login_with_user_password_check]

            for j in method_list:

                result = j()

                if result:

                    return result

        if result is None:

            logger.info('Sorry,could not find the login page!')

    def login(self, user_name='IRISB007', password='123456', dev_mode="test", proxy_ip=None, proxy_port=None):
        """ Login APP

        Kwargs:
            | user_name |  string | IRISB007(默认),用户名 |
            | password | string | 123456(默认),密码 |
            | dev_mode | string | test(默认）/local,开发者选项 |
            | proxy_ip | string | None（默认），代理IP地址 |
            | proxy_port | int | 0~65535, None（默认），代理port |
        Return:
            | None |
        Examples:
            | Login | user_name=IRISB007 | password=123456 |  dev_mode=test |
        """
        logger.info(f'the user parameters:user_name:{user_name},'
                    f'password:{password},'
                    f'dev_mode:{dev_mode},'
                    f'proxy_ip:{proxy_ip},'
                    f'proxy_port:{proxy_port},'
                    )

        if not self.debug:

            self.driver.activate_app(self.package_name)

        mode = self._detect_login_mode()

        logger.info(f"the current login mode:{mode}")

        if mode == "Login_with_Username_Password":

            self._no_login_with_username_password(user_name=user_name, password=password)

        elif mode == "Login_with_Developer":

            self._no_login_with_dev_option(dev_mode=dev_mode, proxy_ip=proxy_ip, proxy_port=proxy_port)

            self._no_login_with_username_password(user_name=user_name, password=password)

        elif mode == "Logged_in":

            pass

        else:

            raise ValueError('Fail to find the login page!')

        logger.info('OK,successfully log into the APP!')

    def sign_up(self, user_name='IRISB007', password='123456'):
        """ Sign up for APP

        Kwargs:
            | user_name |  string | IRISB007(默认),用户名 |
            | password | string | 123456(默认),密码 |
        Return:
            | None |
        Examples:
            | Sign Up | user_name=IRISB007 | password=123456 |
        """
        logger.info(f'the user parameters:user_name:{user_name} '
                    f'password:{password} '
                    )

        locator = get_locator('locator_sign_up')

        self.find_element(*locator, click=True)
