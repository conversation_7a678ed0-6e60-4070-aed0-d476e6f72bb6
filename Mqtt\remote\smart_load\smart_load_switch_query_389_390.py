from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 智能负载开关实时数据查

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 389

cmd_type_c2s = 390

attr_map_s2c = {

    **attr_map_common_opt,

}

attr_map_c2s = {

    **attr_map_s2c,

    'gridSwitchStatus': ('grid_switch_status', 'int'),
    'genStatus': ('generator_status', 'int'),
    'gridVoltCheck': ('generator_startup_type', 'int'),
    'currentSoc': ('current_soc', 'int'),
    'smartSwitchData': ('smart_switch_data', 'dict'),

    'circuitSwRsn': ('switch_reason', 'int'),
    'cutoffPower': ('power_before_switch', 'int'),


}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class SmartLoadSwitch(object):

    def smart_load_switch_get(self, *args, **kwargs):
        """  smart load switch get-T389/390

        Args：
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
        Kwargs:
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Switch Get  |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def smart_load_switch_get_result_check(self, _response, **kwargs):
        """  smart load switch get result check-T390

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Smart Load Switch Get  |
        | ${status} = | Smart Load Switch Get Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def smart_load_switch_get_from_aws_get(self, *args, **kwargs):
        """  smart load switch get from AWS,the message get-T389

        Args：
            | opt | int | 操作类型，0：查询 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Switch Get From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def smart_load_switch_get_from_aws_get_check(self, **kwargs):
        """  smart load switch get from AWS,the message get check-T389

        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Smart Load Switch Get From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.smart_load_switch_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def smart_load_switch_get_from_device_get(self, *args, **kwargs):
        """   smart load switch get response from device,the message get-T390

        Args：
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Smart Load Switch Get From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def smart_load_switch_get_from_device_get_check(self, **kwargs):
        """  smart load switch get response from device,the message get check-T390

        Kwargs:
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Smart Load Switch Get From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.smart_load_switch_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def smart_load_switch_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T389/390

        Args for T389:
            | opt | int | 操作类型，0：查询 |
        Args for T390：
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 389/390 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=389 | rx_time_window=300 |
        | ${status} | Smart Load Switch Get From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.smart_load_switch_get_from_device_get, cmd_type_s2c: self.smart_load_switch_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def smart_load_switch_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T389/390

        Kwargs for T389:
            | opt | int | 操作类型，0：查询 |
        Kwargs for T390：
            | opt | int | 操作类型，0：查询 |
            | grid_switch_status | int | 并离网状态，0：开关断开，1：开关闭合 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | current_soc | int | 系统当前soc |
            | smart_switch_data | dict | 智能开关实时数据 |
            | switch_reason | int | 智能负载开关切换原因,for MSA |
            | power_before_switch | int | 智能负载开关切换前功率,for MSA |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 389/390 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=390 | rx_time_window=300 | filter_mode=and |
        | ${status} | Smart Load Switch Get From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.smart_load_switch_get_from_device_get_check, cmd_type_s2c: self.smart_load_switch_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
