from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result, attr_map_common_type

# 电池控制

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 343

cmd_type_c2s = 344

attr_map_s2c = {

    **attr_map_common_opt,
    **attr_map_common_type,

    'data': ('power_control_data', 'list'),
    'cmd': ('apower_control_cmd', 'int'),
    'start': ('start_timestamp', 'int'),
    'end': ('end_timestamp', 'int'),
}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class BatControl(object):

    def battery_control_set(self, *args, **kwargs):
        """  battery control set-T343/344

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Battery Control Set | type=1 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)
        kwargs.setdefault('type', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def battery_control_set_result_check(self, _response, **kwargs):
        """  battery control set result check-T344

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Battery Control Set | type=1 |
        | ${status} = | Battery Control Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def battery_control_set_from_aws_get(self, *args, **kwargs):
        """  battery control set from AWS,the message get-T343

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Battery Control Set From AWS Get | type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def battery_control_set_from_aws_get_check(self, **kwargs):
        """  battery control set from AWS,the message get check-T343

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Battery Control Set From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.battery_control_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def battery_control_set_from_device_get(self, *args, **kwargs):
        """   battery control set response from device,the message get-T344

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Battery Control Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def battery_control_set_from_device_get_check(self, **kwargs):
        """  battery control set response from device,the message get check-T344

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Battery Control Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.battery_control_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def battery_control_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T343/344

        Args for T343:
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Args for T344：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 343/344 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=343 | rx_time_window=300 |
        | ${status} | Battery Control Set From Msg Get | type | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.battery_control_set_from_device_get, cmd_type_s2c: self.battery_control_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def battery_control_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T343/344

        Kwargs for T343:
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs for T344：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | type | int | 操作类型，1:功率控制，2:apower总开关 |
            | power_control_data | int | apower功率控制数据列表,仅适用于type为功率控制 |
            | apower_control_cmd | int | apower总开关，0:未设置，1：开机，2：关机 |
            | start_timestamp | int | apower打开时刻的时戳 |
            | end_timestamp | int | apower关闭时刻的时戳 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 343/344 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=344 | rx_time_window=300 | filter_mode=and |
        | ${status} | Battery Control Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.battery_control_set_from_device_get_check, cmd_type_s2c: self.battery_control_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
