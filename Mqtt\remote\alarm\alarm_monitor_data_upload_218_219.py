from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result, attr_map_common_type

# 告警快照文件上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 218

cmd_type_s2c = 219

attr_map_s2c = {

    **attr_map_common_result,
}

attr_map_c2s = {

    **attr_map_common_type,

    'notice': ('', 'int'),
    'alarmSnapshFile': ('alarm_snap_file', 'string'),
    'sign': ('', 'string'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class AlarmSnapUpload(object):

    def alarm_snap_file_upload_from_aws_get(self, *args, **kwargs):
        """  the current alarm list data response from AWS,the message get-T219

        Args：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Snap File Upload From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_snap_file_upload_from_aws_get_check(self, **kwargs):
        """  the current alarm list data from AWS,the message get check-T219

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Snap File Upload From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_snap_file_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_snap_file_upload_from_device_get(self, *args, **kwargs):
        """  the current alarm list data from device,the message get-T218

        Args：
            | notice | int | 1：通知更新告警快照文件数据  |
            | alarm_snap_file |  string | <=128Bytes,告警快照文件上传路径 |  |
            | sign | string | 32位，文件 MD5 checksum |
            | type | int | 1:主动上传，2：召唤 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Alarm Snap File Upload From Device Get | alarm_snap_file |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def alarm_snap_file_upload_from_device_get_check(self, **kwargs):
        """  the current alarm list data from device,the message get check-T218

        Kwargs:
            | notice | int | 1：通知更新告警快照文件数据  |
            | alarm_snap_file |  string | <=128Bytes,告警快照文件上传路径 |  |
            | sign | string | 32位，文件 MD5 checksum |
            | type | int | 1:主动上传，2：召唤 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Alarm Snap File Upload From Device Get Check | current_alarm_list=@{EMPTY} |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.alarm_snap_file_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def alarm_snap_file_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T218/219

        Args for T218:
            | notice | int | 1：通知更新告警快照文件数据  |
            | alarm_snap_file |  string | <=128Bytes,告警快照文件上传路径 |  |
            | sign | string | 32位，文件 MD5 checksum |
            | type | int | 1:主动上传，2：召唤 |
        Args for T219：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 218/219 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=218 | rx_time_window=300 |
        | ${status} | Alarm Snap File Upload From Msg Get | alarm_snap_file | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_snap_file_upload_from_device_get, cmd_type_s2c: self.alarm_snap_file_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def alarm_snap_file_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T218/219

        Kwargs for T218:
            | notice | int | 1：通知更新告警快照文件数据  |
            | alarm_snap_file |  string | <=128Bytes,告警快照文件上传路径 |  |
            | sign | string | 32位，文件 MD5 checksum |
            | type | int | 1:主动上传，2：召唤 |
        Kwargs for T219：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 218/219 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=218 | rx_time_window=300 | filter_mode=and |
        | ${status} | Alarm Snap File Upload From Msg Get Check | type=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.alarm_snap_file_upload_from_device_get_check, cmd_type_s2c: self.alarm_snap_file_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
