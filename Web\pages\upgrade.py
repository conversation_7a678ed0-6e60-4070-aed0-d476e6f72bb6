from time import (sleep, time, strptime, mktime)
from Web.base import (Base, By)
from robot.api import logger
from functools import partial
from selenium.common.exceptions import NoSuchElementException

common_element = f"//div[@class='tableBox dark']//div[@class='el-scrollbar']//tbody"
element_generate = partial(lambda x: f"{common_element}//td[{x}]//span")


class Upgrade(Base):

    def firmware_upgrade(self, device='ULG_TEST_Samuel', version_type='system', software_version='ibg_ud_V10R10B09D00_ems_0511_v1.zip', expected_grid_status=None, upgrade_result_check=True, step_mode=False):

        logger.info(f"the user kwargs is:device:{device},version_type:{version_type},software_version:{software_version},upgrade_result_check:{upgrade_result_check}")

        if version_type in ['system', '系统版本']:

            version_type = '系统版本'

        elif version_type in ['non-standard', '非标版本']:

            version_type = '非标版本'

        elif version_type in ['patch', '补丁版本']:

            version_type = '补丁版本'

        ret = dict()

        ele = self.find_by_CLASS_NAME('mon-icon-gujian').click()

        sleep(2)

        m = self.find_by_CLASS_NAMEs('el-menu-item')

        for i in m:

            firm_list = ['固件升级']

            if i.text in firm_list:
                i.click()
                sleep(3)

        ele = self.find_by_Xpath("//input[contains(@placeholder,\"aGate序列号、安装商公司、终端用户...\")]")
        ele.send_keys(device)

        ele.click()

        ele = self.find_by_Xpath("//span[contains(.,\"搜索\")]").click()
        sleep(3)

        # ele = self.find_by_CLASS_NAME('el-checkbox__inner').click()  # select all matched SNs

        ele_index = 1

        for i in range(1, 10):

            ele = self.find_by_Xpath(f"//table/tbody/tr[{i}]/td[5]/div/span")

            if ele:

                current_sn = ele.text

                msg = f'detect the SN:{current_sn}'

                logger.console(msg)

                logger.info(msg)

                if current_sn == device:

                    msg = f'OK,fond the matched SN for {device}'

                    logger.console(msg)

                    logger.info(msg)

                    ele_index = i

                    break

        ele = self.find_by_Xpath(f"//table/tbody/tr[{ele_index}]/td[1]/div").click()

        sleep(3)

        logger.console('Retrieve current system status...')

        ele = self.find_by_Xpath(f"//table/tbody/tr[{ele_index}]/td[6]/div/span")

        system_grid_connect_status = ele.text

        output = f'system grid connect status:{system_grid_connect_status}'

        logger.console(output)

        logger.info(output)

        if expected_grid_status:

            if expected_grid_status != system_grid_connect_status:

                raise ValueError(f'Sorry,detected the unexpected grid status:{system_grid_connect_status} for expected grid status:{expected_grid_status}!')

        ele = self.find_by_Xpath(f"//table/tbody/tr[{ele_index}]/td[3]/div/span")

        system_network_connect_status = ele.text

        output = f'system network connect status:{system_network_connect_status}'

        logger.console(output)

        logger.info(output)

        ele = self.find_by_Xpath(f"//table/tbody/tr[{ele_index}]/td[4]/div/span")

        system_fault_status = ele.text

        output = f'system fault status:{system_fault_status}'

        logger.console(output)

        logger.info(output)

        ele = self.find_by_Xpath(f"//table/tbody/tr[{ele_index}]/td[10]/div/span")

        system_apower_number = ele.text

        output = f'system aPower number:{system_apower_number}'

        logger.console(output)

        logger.info(output)

        # 生成工单
        ele = self.find_by_Xpath("//button[@class='btn']").click()

        ele = self.find_by_Xpath("//div//input[starts-with(@id,\"el-id-\")]")

        output = 'Generated upgrade order...'

        logger.console(output)

        logger.info(output)

        #  选择版本类型

        ele, exp_version = self._auto_select_sw_type(ele, version_type, software_version)

        output = f'automatically choose the firmware type:{exp_version} for {software_version}'

        logger.console(output)

        logger.info(output)

        #  选择软件

        self.move_to_element(ele)

        sleep(3)

        ele = self.find_by_CLASS_NAME('footer-box')
        eles = ele.find_elements(By.XPATH, "//button")
        ele = eles[-1]

        ele.click()
        sleep(5)

        m = self.find_by_CLASS_NAMEs('el-menu-item')

        for i in m:

            firm_list = ['固件升级']

            if i.text in firm_list:
                i.click()
                sleep(3)

        ele = self.find_by_Xpath("//div[@id=\"tab-2\"]").click()

        sleep(3)

        ele = self.find_by_Xpath("//input[contains(@placeholder,\"aGate序列号\")]")
        ele.send_keys(device)

        ele.click()

        ele = self.find_by_Xpath("//span[contains(.,\"搜索\")]").click()

        sleep(3)

        eles = self.find_by_CLASS_NAME('el-checkbox__inner')

        m = eles.find_elements(By.XPATH, "//label")
        m[1].click()

        m = self.find_by_CLASS_NAMEs("handleBtn")
        m[1].click()

        output = f"begin to upgrade to {software_version}"

        logger.console(output)

        logger.info(output)

        sleep(7)

        if not step_mode:

            result = self._get_upgrading_progress(ret=ret, refresh_period=5)

            if upgrade_result_check and not result:

                raise ValueError(f"Fail to upgrade to {software_version} due to {ret['cause_of_failure']}")

        return ret

    def _auto_select_sw_type(self, ele, version_type, software_version):

        _version_type = ['系统版本', '非标版本', '补丁版本']

        for i in range(len(_version_type)):

            ele_sum = ele.find_elements(By.XPATH, "//div/input")

            ele = ele_sum[-2]

            ele.click()

            exp_version = f'{_version_type[i]}'

            logger.info(f'the expected version:{exp_version}')

            try:

                ele = self.find_by_Xpath(f"//span[contains(.,\"{exp_version}\")]")

                logger.info(f'the element:{ele}')

                if ele:

                    ele.click()

                ele = self.find_by_Xpath("//input[contains(@class,'el-select-v2__combobox-input')]")

                ele.click()

                ele.send_keys(f"{software_version}")

                ele = self.find_by_Xpath("//div/li")

                return (ele, exp_version)

            except NoSuchElementException:

                logger.info(f'Fail to find the expected {software_version} in {exp_version} files')

                if i == 2:

                    raise

        return ele

    def _get_upgrading_progress(self, ret=None, refresh_period=5):

        number = 0

        init_status = self.find_by_Xpath("//div[@class='tableBox dark']//div[@class='el-scrollbar']//tbody//td[5]//span")

        error_count = 0

        MAX_ERROR_COUNT = 10

        t1 = time()

        status = init_status.text

        while status == "升级中":

            sleep(refresh_period)

            self.refresh()

            try:

                ele = self.find_by_Xpath("//div[@class='tableBox dark']//div[@class='el-scrollbar']//tbody//td[5]//span")

                status = ele.text

                logger.debug("the current upgrading status:", status)

            except NoSuchElementException:

                # ignore the error web refresh

                error_count += 1

                logger.debug(f"the ignored web refresh number:{error_count}")

                if error_count > MAX_ERROR_COUNT:

                    logger.debug(f"Too many error during refreshing pages,exit the page refresh!")

                    break

        t2 = time()

        delta = t2 - t1

        logger.info(f"the upgrade waiting time:{delta}seconds")

        ele_1 = self.find_by_Xpath(element_generate(1))  # No.

        ele_2 = self.find_by_Xpath(element_generate(2))  # aGate

        ele_3 = self.find_by_Xpath(element_generate(3))  # package name

        ele_4 = self.find_by_Xpath(element_generate(4))  # operation

        ele_5 = self.find_by_Xpath(element_generate(6))  # comments

        ele_6 = self.find_by_Xpath(element_generate(7))  # error code

        ele_7 = self.find_by_Xpath(element_generate(8))   # error reason

        ele_8 = self.find_by_Xpath(element_generate(10))  # upgrade start time

        ele_9 = self.find_by_Xpath(f"{common_element}//td[11]")  # upgrade end time

        ret.update({'upgrade_ticket_number': ele_1.text,
                    'aGate_name': ele_2.text,
                    'upgrade_result': status,
                    'aGate_SN': ele_3.text,
                    'package_name': ele_4.text,
                    'note_information': ele_5.text,
                    'error_code': ele_6.text,
                    'cause_of_failure': ele_7.text,
                    'upgrade-start_time': ele_8.text,
                    'upgrade_end_time': ele_9.text,
                    'upgrade_duration': delta,
                    })

        logger.debug(f"the upgrade result:{ret}")

        if status != "升级成功":

            logger.info("Fail to upgrade firmware!")

            return False

        return True

    def get_upgrade_status(self):

        self.refresh()

        try:

            ele = self.find_by_Xpath("//div[@class='tableBox dark']//div[@class='el-scrollbar']//tbody//td[5]//span")

            status = ele.text

            logger.debug("the current upgrading status:", status)

        except NoSuchElementException:

            # ignore the error web refresh

            logger.debug(f"Fail to get the upgrade status")

        return status

    def get_upgrade_result(self, status=None):

        ret = dict()

        ele_1 = self.find_by_Xpath(element_generate(1))  # No.

        ele_2 = self.find_by_Xpath(element_generate(2))  # aGate

        ele_3 = self.find_by_Xpath(element_generate(3))  # package name

        ele_4 = self.find_by_Xpath(element_generate(4))  # operation

        ele_5 = self.find_by_Xpath(element_generate(6))  # comments

        ele_6 = self.find_by_Xpath(element_generate(7))  # error code

        ele_7 = self.find_by_Xpath(element_generate(8))   # error reason

        ele_8 = self.find_by_Xpath(element_generate(10))  # upgrade start time

        ele_9 = self.find_by_Xpath(f"{common_element}//td[11]")  # upgrade end time

        start = strptime(ele_8.text, '%Y-%m-%d %H:%M:%S')

        end = strptime(ele_9.text, '%Y-%m-%d %H:%M:%S')

        elapse = mktime(end) - mktime(start)

        ret.update({'upgrade_ticket_number': ele_1.text,
                    'aGate_name': ele_2.text,
                    'upgrade_result': status,
                    'aGate_SN': ele_3.text,
                    'package_name': ele_4.text,
                    'note_information': ele_5.text,
                    'error_code': ele_6.text,
                    'cause_of_failure': ele_7.text,
                    'upgrade-start_time': ele_8.text,
                    'upgrade_end_time': ele_9.text,
                    'upgrade_duration': elapse,
                    })

        logger.debug(f"the upgrade result:{ret}")

        return ret
