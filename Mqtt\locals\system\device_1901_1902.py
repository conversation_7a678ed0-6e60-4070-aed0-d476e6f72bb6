from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 发电机参数

cmd_type_c2s = 1901

cmd_type_s2c = 1902

attr_map_c2s = {

    **attr_map_common_opt,

    'genEn': ('generator_enabled', 'int'),
    'gridVoltCheck': ('generator_startup_type', 'int'),
    'mode': ('', 'int'),
    'genModel': ('model', 'string'),

    'genRatedPower': ('rated_power', 'int'),
    'genOptiPPoint': ('optimal_power', 'int'),
    'startDelTime': ('start_delay_time', 'int'),

    'genStartElec': ('start_energy', 'int'),
    'genCloseElec': ('stop_energy', 'int'),
    'genStat': ('generator_status', 'int'),
    'manuSw': ('manual_switch', 'int'),

    'power': ('', 'int'),
    'curr': ('current', 'int'),
    'volt': ('voltage', 'int'),
    'freq': ('frequency', 'int'),


    'genpowerGen': ('generated_energy', 'int'),

    'charge1En': ('charging_time1_enable', 'int'),
    'charge1StartTime': ('charging_time1_start', 'string'),
    'charge1EndTime': ('charging_time1_end', 'string'),

    'charge2En': ('charging_time2_enable', 'int'),
    'charge2StartTime': ('charging_time2_start', 'string'),
    'charge2EndTime': ('charging_time2_end', 'string'),

    'charge3En': ('charging_time3_enable', 'int'),
    'charge3StartTime': ('charging_time3_start', 'string'),
    'charge3EndTime': ('charging_time3_end', 'string'),

    'oilmanoEn': ('manoeuvre_enabled', 'int'),
    'manoFre': ('manoeuvre_frequency', 'string'),
    'manoDate': ('manoeuvre_date', 'string'),

    'manoStartTime': ('manoeuvre_start_time', 'string'),
    'manoTime': ('manoeuvre_duration', 'int'),
    'manoManExit': ('manoeuvre_manual_exit_time', 'int'),


    'v2LModeEnable': ('V2L_mode_enabled', 'int'),
    'v2LStartTimeout': ('V2L_start_timeout', 'int'),
    'v2LCarRatedPower': ('V2L_car_rated_power', 'int'),

    'v2LModeCtrl': ('V2L_mode_control', 'int'),
    'v2LRunState': ('V2L_running_status', 'int'),
    'v2lChargeEna': ('V2L_charging_enabled', 'int'),

    'genAccessMethod': ('generator_access_method', 'int'),
    'v2lAccessMethod': ('V2L_access_method', 'int'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalGenerator(Base):

    def local_generator_set(self, *args, **kwargs):
        """  generator set-T1901
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Generator Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_generator_set_result_check(self, _response, **kwargs):
        """  generator set result check-T1902

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Generator Set |
        | ${status} = | Local Generator Set Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
