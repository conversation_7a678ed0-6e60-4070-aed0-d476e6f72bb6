from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_type

# 设备实时数据被动召唤上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 212

cmd_type_s2c = 211

attr_map_s2c = {

    **attr_map_common_type,
    'fhpSn': ('fhp_sn', 'string'),
}

attr_map_c2s = {

    **attr_map_common_type,
    'fhpSn': ('fhp_sn', 'list'),
    'gridFreq': ('grid_freq', 'float'),

    #  IBG part
    'gridVol1': ('grid_voltage_L1', 'float'),
    'gridVol2': ('grid_voltage_L2', 'float'),
    'gridCurr1': ('grid_curret_L1', 'float'),
    'gridCurr2': ('grid_curret_L2', 'float'),
    'loadCurr1': ('load_curret_L1', 'float'),
    'loadCurr2': ('load_curret_L2', 'float'),
    'dspSetFreq': ('dsp_freq', 'float'),
    'gridRelayStat': ('grid_relay_status', 'int'),
    'oilRelayStat': ('generator_relay_status', 'int'),
    'solarRelayStat': ('solar_relay_status', 'int'),
    'loadRelay1Stat': ('load_relay1_status', 'int'),
    'loadRelay2Stat': ('load_relay2_status', 'int'),
    'evRelayStat': ('ev_relay_status', 'int'),
    'loadSolarRelay1Stat': ('load_solar_relay1_status', 'int'),
    'loadSolarRelay2Stat': ('load_solar_relay2_status', 'int'),
    'ibgRunStatus': ('ibg_run_status', 'int'),
    'name': ('', 'string'),
    'electricity_type': ('', 'int'),
    'dspRunStatus': ('dsp_run_status', 'int'),
    'gridLineVol': ('grid_line_voltage', 'int'),
    'gridRelay2': ('grid_relay2_status', 'int'),
    'pvRelay2': ('pv_relay2_status', 'int'),
    'blackStartRelay': ('black_start_relay_status', 'int'),
    'genVoltage': ('generator_voltage', 'int'),
    'BFPVApboxRelay': ('bfpv_apbox_relay', 'int'),

    'msaModel': ('msa_model', 'int'),
    'msaInternalTemp': ('msa_internal_temp', 'float'),

    # PE,BMS part

    'singleHighestVolt': ('single_highest_voltage', 'float'),
    'singleLowestVolt': ('single_lowest_voltage', 'float'),
    'singleHighestTemp': ('single_highest_temp', 'float'),
    'singleLowestTemp': ('single_lowest_temp', 'float'),
    'batVolt': ('bat_voltage', 'list'),
    'batTemp': ('bat_temp', 'list'),
    'batTotalVolt': ('bat_total_voltage', 'float'),
    'batCurr': ('bat_current', 'float'),
    'batSoc': ('bat_soc', 'float'),
    'batSoh': ('bat_soh', 'float'),
    'alarmLevel': ('alarm_level', 'int'),
    'gridVol1': ('grid_voltage_L1', 'float'),
    'gridVol2': ('grid_voltage_L2', 'float'),
    'invVolt1': ('inv_voltage_L1', 'float'),
    'invVolt2': ('inv_voltage_L2', 'float'),
    'positiveBusVolt': ('pos_bus_voltage', 'float'),
    'negativeBusVolt': ('neg_bus_voltage', 'float'),
    'midBusVolt': ('mid_bus_voltage', 'float'),
    'invCurr1': ('inv_current_L1', 'float'),
    'invCurr2': ('inv_current_L2', 'float'),
    'buckboostCurr': ('buckboost_current', 'float'),
    'pebatVolt': ('pe_bat_voltage', 'float'),
    'runMode': ('run_mode', 'int'),
    'inverterStatus': ('inv_run_status', 'int'),
    'DCDCStatus': ('dc_dc_run_status', 'int'),
    'outCur1': ('out_current_L1', 'float'),
    'outCur2': ('out_current_L2', 'float'),
    'actPwr1': ('act_power_L1', 'int'),
    'actPwr2': ('act_power_L2', 'int'),
    'reactPwr1': ('react_power_L1', 'int'),
    'reactPwr2': ('react_power_L2', 'int'),
    'devTemp': ('device_temp', 'float'),
    'llcTemp': ('llc_temp', 'float'),
    'buckBoostTemp': ('buck_boost_temp', 'float'),
    'invTemp': ('inv_temp', 'float'),
    'maxVolPos': ('max_single_bms_voltage_sn', 'int'),
    'minVolPos': ('min_single_bms_voltage_sn', 'int'),
    'maxTempPos': ('max_single_bms_temp_sn', 'int'),
    'minTempPos': ('min_single_bms_temp_sn', 'int'),
    'bmsState': ('bms_state', 'int'),
    'mosState': ('mos_state', 'int'),
    'switchState': ('apower_switch_state', 'int'),
    'heatState': ('bms_heat_state', 'int'),
    'fanState': ('bms_fan_state', 'int'),
    'balanState': ('bms_balance_state', 'int'),
    'gridVoltAN': ('grid_phase_a_voltage', 'float'),
    'gridVoltBN': ('grid_phase_b_voltage', 'float'),
    'solarVoltAN': ('solar_phase_a_voltage', 'float'),
    'solarVoltBN': ('solar_phase_b_voltage', 'float'),
    'gridLineVol': ('grid_line_voltage', 'float'),
    'invLineVol': ('inv_line_voltage', 'float'),
    'samBatVol': ('bat_sample_voltage', 'float'),
    'peChEnergy': ('kwh_pe_charge', 'float'),
    'peDchEnergy': ('kwh_pe_discharge', 'float'),
    'envBmsTmp': ('bms_env_temp', 'float'),

    # MPPT part

    'mpptActPower': ('mppt_act_power', 'int'),
    'mpptRunSta': ('mppt_run_status', 'int'),
    'mpptMolVol': ('mppt_pv_voltage', 'list'),
    'mpptMolCul': ('mppt_pv_current', 'list'),
    'mpptMolArcStr': ('mppt_pv_arc_strength', 'list'),
    'mpptAfciCur': ('mppt_afci_current', 'list'),
    'mpptCyTmp': ('mppt_cavity_temp', 'float'),

    'mpptHs1Tmp': ('mppt_hs1_temp', 'float'),
    'mpptHs2Tmp': ('mppt_hs2_temp', 'float'),
    'mpptIrCty': ('mppt_insu_conduct', 'int'),
    'mpptAuxVol': ('mppt_aux_power_voltage', 'float'),
    'mpptPvVol': ('mppt_pv_bus_voltage', 'float'),
    'mpptPeVol': ('mppt_pe_bus_voltage', 'float'),
    'afciHdVer': ('mppt_afci_hw_ver', 'int'),
    'afciStVer': ('mppt_afci_sw_ver', 'int'),
    'afciAlgVer': ('mppt_afci_algorithm_ver', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceData(object):

    def device_data_upload(self, *args, **kwargs):
        """  the device data upload-T211

        Args:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
        Kwargs:
            | type | int | 操作类型，1(by default)：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | fhp_sn |  string | <=32Bytes,仅对type=2使用 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Data Upload | type=2 | fhp_sn=10050015S00X24350039 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('type', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def device_data_upload_result_check(self, _response, **kwargs):
        """  the device data upload result check-T212

        Args:
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Device Data Upload | type=2 | fhp_sn=10050015S00X24350039 |
        | ${status} = |  Device Data Upload Result Check | ${response} | type=2 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def device_data_from_aws_get(self, *args, **kwargs):
        """  the device data from AWS,the message get-T211

        Args：
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | fhp_sn |  string | <=32Bytes,仅对type=2使用 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Data From AWS Get | type |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_data_from_aws_get_check(self, **kwargs):
        """  the device data from AWS,the message get check-T211

        Kwargs:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | fhp_sn |  string | <=32Bytes,仅对type=2使用 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device Data From AWS Get Check | type=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_data_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_data_from_device_get(self, *args, **kwargs):
        """  the device data from device,the message get-T212

        Args：
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Device Data From Device Get | reason |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_data_from_device_get_check(self, **kwargs):
        """  the device data from device,the message get check-T212

        Kwargs:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Device  Data From Device Get Check | reason=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T211/212

        Args for T211:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | fhp_sn |  string | <=32Bytes,仅对type=2使用 |
        Args for T212：
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 211/212 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=211 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Data From Msg Get | type | msg=${packets} |
        | ${status} | Device Data From Msg Get | type | apower_sn | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=212 | rx_time_window=300 |
        | ${status} | Device Data From Msg Get | type | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_data_from_device_get, cmd_type_s2c: self.device_data_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T211/212

        Kwargs for T211:
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | fhp_sn |  string | <=32Bytes,仅对type=2使用 |
        Kwargs for T212：
            | type | int | 操作类型，1：IBG数据，2：单台PE、BMS数据，3：单台MPPT数据 |
            | IBG部分 |
            | fhp_sn | string list | aPower SN |
            | grid_voltage_L1 | float | 市电L1 电压，单位：V |
            | grid_voltage_L2 | float | 市电L2 电压，单位：V |
            | grid_curret_L1 | float | 市电L1 电流，单位：A |
            | grid_curret_L2 | float | 市电L2 电流，单位：A |
            | load_curret_L1 | float | 负载L1电流，单位：A |
            | load_curret_L2 | float | 负载L2电流，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP设定的频率，单位：Hz |
            | grid_relay_status  | int | 市电继电器状态，0：断开，1：闭合 |
            | generator_relay_status | int | 发电机继电器状态，0：断开，1：闭合 |
            | solar_relay_status  | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status  | int | 负载继电器1状态，0：断开，1：闭合 |
            | load_relay2_status  | int | 负载继电器2状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 | 自发自用或者TOU |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_line_voltage | int | 市电L1L2线电压，单位：0.1V |
            | grid_relay2_status | int | 市电继电器2状态，0：断开，1：闭合 |
            | pv_relay2_status | int | 光伏继电器2状态，0：断开，1：闭合 |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | generator_voltage | int | 发电机电压，单位：0.1V |
            | bfpv_apbox_relay | int | 光伏电表前aPobx继电器状态，0：断开，1：闭合 |
            | msa_model | int | MSA型号, 0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | msa_internal_temp | float | MSA内部温度，单位：摄氏度，MSA=CND才有效 |
            | PE,BMS部分 |
            | single_highest_voltage | float | 最高单体电压，单位：mV |
            | single_lowest_voltage | float | 最低单体电压，单位：mV |
            | single_highest_temp | float | 最高单体温度，单位：摄氏度 |
            | single_lowest_temp | float | 最低单体温度，单位：摄氏度 |
            | bat_voltage | float list | BMS电池电压，单位：mV |
            | bat_temp | float list | BMS电池温度，单位：摄氏度 |
            | bat_total_voltage | float | 电池总电压，单位：V |
            | bat_current | float | 电池总电流，单位：A |
            | bat_soc  | float | 电池SOC，单位：% |
            | bat_soh  | float | 电池SOH，单位：% |
            | alarm_level | int | 告警级别 |
            | grid_voltage_L1 | float | 电网电压L1，单位：V |
            | grid_voltage_L2 | float | 电网电压L2，单位：V |
            | inv_voltage_L1 | float | 逆变电压L1，单位：V |
            | inv_voltage_2 | float | 逆变电压L2，单位：V |
            | pos_bus_voltage | float | 正母线电压，单位：V |
            | neg_bus_voltage | float | 负母线电压，单位：V |
            | mid_bus_voltage | float | 中间母线电压，单位：V |
            | grid_freq  | float | 电网频率，单位：Hz |
            | inv_current_L1 | float | 逆变电流L1，单位：A |
            | inv_current_L2 | float | 逆变电流L2，单位：A |
            | buckboost_current | float | buck boost电流，单位：A |
            | pe_bat_voltage | float | PE电池电压，单位：V |
            | run_mode | int | PE运行模式，0：初始化；1：参数初始化；2：监听；3：待机；4：预充检测；5：预充；6：起机延时；7：逆变继电器吸合；8：运行；9：故障 |
            | inv_run_status | int | 逆变器运行状态，0：系统状态初始化；1：系统参数初始化；2：监听；3：系统就绪；4：预充检测；5：交流预充；6：系统启动延时；7：继电器吸合；8：系统运行；9：系统故障 |
            | dc_dc_run_status | int | DC-DC运行状态，0：系统状态初始化；1：系统参数初始化；2：待机态；3：电池缓冲中间母线状态；4：电池缓冲母线状态；5：离网BuckBoost状态；6：并网充电状态；7：并网放电状态；8：逆向缓冲状态(电网缓冲中间母线)；9：逆向缓冲状态（中间母线缓冲电池） |
            | out_current_L1 | float | PE L1输出电流，单位：A |
            | out_current_L2 | float | PE L2输出电流，单位：A |
            | act_power_L1 | int | PE L1有功功率，单位：W |
            | act_power_L2 | int | PE L2有功功率，单位：W |
            | react_power_L1 | int | PE L1无功功率，单位：W |
            | react_power_l2 | int | PE L2无功功率，单位：W |
            | device_temp | float | PE 机内温度，单位：摄氏度 |
            | llc_temp | float | PE 机内温度，单位：摄氏度 |
            | buck_boost_temp | float | PE buck boost散热器温度，单位：摄氏度 |
            | inv_temp | float | PE 逆变器温度，单位：摄氏度 |
            | max_single_bms_voltage_sn | int | 最高单体电压序号 |
            | min_single_bms_voltage_sn | int | 最低单体电压序号 |
            | max_single_bms_temp_sn | int | 最高单体温度序号 |
            | min_single_bms_temp_sn | int | 最低单体温度序号 |
            | bms_state | int | BMS工作状态，1=初始化，2=自检，3=待机，4=预充，5=运行，6=充电，7=放电，8=充电保护，9=放电保护，10=充放电保护，11=故障，12=异常充电准备，13=异常充电，14=异常充电停止，15=工装模式，16=补电模式，17=补电故障 |
            | mos_state | int |  MOS管工作状态，0：断开，1：闭合 |
            | apower_switch_state |  int |  aPower按钮状态，0：断开，1：闭合 |
            | bms_heat_state |  int |  BMS加热状态，0：未使能，1：使能 |
            | bms_fan_state |  int |  BMS风扇状态，0：未使能，1：使能 |
            | bms_balance_state |  int |  BMS均衡状态，0：未使能，1：使能 |
            | grid_phase_a_voltage | float | 电表A相相电压，单位：V |
            | grid_phase_b_voltage | float | 电表B相相电压，单位：V |
            | solar_phase_a_voltage | float | 光伏A相相电压，单位：V |
            | solar_phase_b_voltage | float | 光伏B相相电压，单位：V |
            | grid_line_voltage  | float | 电网线电压，单位：V |
            | inv_line_voltage  | float | 逆变线电压，单位：V |
            | bat_sample_voltage | float | 采样电池电压，单位：V |
            | kwh_pe_charge  | float | PE充电电量，单位：kwh |
            | kwh_pe_discharge  | float | PE放电电量，单位：kwh |
            | bms_env_temp  | float | BMS环境温度，单位：摄氏度 |
            | MPPT部分 |
            | mppt_act_power | int | MPPT输出功率，单位：w |
            | mppt_run_status | int | MPPT运行状态， |
            | mppt_pv_voltage | float list | MPPT PV1~PV4输出电压，单位：V |
            | mppt_pv_current | float list | MPPT PV1~PV4输出电流，单位：A |
            | mppt_pv_arc_strength | int list |  MPPT PV1~PV4拉弧强度 |
            | mppt_afci_current | int list | MPPT PV1~PV4 AFCI电流，单位：mA |
            | mppt_cavity_temp | float | MPPT腔温，单位：摄氏度 |
            | mppt_hs1_temp  | float | MPPT散热器1温度，单位：摄氏度 |
            | mppt_hs2_temp  | float | MPPT散热器2温度，单位：摄氏度 |
            | mppt_insu_conduct | int | MPPT绝缘阻抗电导，单位：us |
            | mppt_aux_power_voltage | float | MPPT 12V辅源电压，单位：V |
            | mppt_pv_bus_voltage | float | MPP PV侧总线电压，单位：V |
            | mppt_pe_bus_voltage | float |  MPP PE侧总线电压，单位：V |
            | mppt_afci_hw_ver | int | MPPTAFCI拉弧硬件版本 |
            | mppt_afci_sw_ver | int | MPPTAFCI拉弧软件版本 |
            | mppt_afci_algorithm_ver | int | MPPTAFCI拉弧算法版本 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 211/212 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=211 | rx_time_window=300 | filter_mode=and |
        | ${status} | Device Data From Msg Get Check | type=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_data_from_device_get_check, cmd_type_s2c: self.device_data_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
