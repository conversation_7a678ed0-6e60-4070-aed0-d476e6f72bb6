from Web.base import (Base, WebDriverWait, By, EC)
from Web.util import ret_value_handle
from robot.api import logger

locate_username = 'username'
locate_password = 'password'

locate_login = '//html/body/div[1]/button'
locate_soowoo = '//html/body/nav/ul/li[2]/a'
locate_opt1 = 'lanIp'
locate_opt2 = 'time'
locate_ip = 'ipAddress'
locate_SN = 'inputSN'
locate_get1 = '//*[@id="loginForm1"]/div[2]/button'
locate_get2 = '//*[@id="loginForm2"]/div[4]/button'
locate_mypwd = 'output'


class Skey(Base):

    def ssh_key_get(self, *args, url='http://bear.ems-code.com/login/', username='Shawn', password='Feng10086', mode='auto', browser='Chrome', browser_mode='headless', executable_path=None, ret_mode='auto'):

        logger.info(f"the user kwargs is:url:{url},username:{username},password:{password},mode:{mode},browser:{browser},browser_mode:{browser_mode}")

        mode = mode.lower()

        result = {}

        ret = None

        if not len(args):

            raise ValueError(f"please input SN or IP of aGate!")

        self.open(url=url, browser=browser, browser_mode=browser_mode, executable_path=executable_path)

        self.driver.find_element(By.ID, locate_username).send_keys(username)
        self.driver.find_element(By.ID, locate_password).send_keys(password)
        self.driver.find_element(By.XPATH, locate_login).click()

        alert = WebDriverWait(self.driver, 5).until(EC.alert_is_present())
        alert.accept()

        element_sw = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable((By.XPATH, locate_soowoo)))
        element_sw.click()

        for i in args:

            if mode == 'auto':

                if '.' in i and i.count('.') == 3:
                    locate_opt = locate_opt1
                    locate_get = locate_get1
                    locate_input = locate_ip
                else:
                    locate_opt = locate_opt2
                    locate_get = locate_get2
                    locate_input = locate_SN

            elif mode == 'ip':
                locate_opt = locate_opt1
                locate_get = locate_get1
                locate_input = locate_ip

            else:
                locate_opt = locate_opt2
                locate_get = locate_get2
                locate_input = locate_SN

            self.driver.refresh()
            element_opt = WebDriverWait(self.driver, 5).until(EC.element_to_be_clickable((By.ID, locate_opt)))
            element_opt.click()
            self.driver.find_element(By.ID, locate_input).send_keys(i)
            ele_pwd = self.driver.find_element(By.ID, locate_mypwd)
            my_password_old = ele_pwd.text
            self.driver.find_element(By.XPATH, locate_get).click()
            WebDriverWait(self.driver, 5).until(lambda driver: ele_pwd.text != my_password_old)

            result.update({i: ele_pwd.text})

        logger.info(f"ssh-key:{result}")

        WebDriverWait(self.driver, 5).until(lambda driver: driver.execute_script('return document.readyState') == 'complete')

        self.driver.quit()

        return ret_value_handle(result, *args, ret_mode=ret_mode)
