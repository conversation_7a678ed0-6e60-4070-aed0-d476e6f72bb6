from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备编址结果查询

cmd_type_c2s = 1105

cmd_type_s2c = 1106

attr_map_c2s = {

    **attr_map_common_opt,

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'devNum': ('device_number', 'int'),
    'isOver': ('addressing_status', 'int'),
    'devMap': ('device_map', 'list'),
    'snMap': ('SN_map', 'list'),
    'apbox20Num': ('apbox2.0_number', 'int'),
    'apbox20Map': ('apbox2.0_map', 'list'),
    'msaModel': ('mas_model', 'int'),

}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalAddressingResult(Base):

    def local_addressing_get(self, *args, **kwargs):
        """  local addressing get-T1105
        Args：
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息? |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs:
            | opt |  int | 0:查询  |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Addressing Get | result | device_number |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_addressing_get_result_check(self, _response, **kwargs):
        """  local addressing get result check-T1106

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：成功；1：总台数匹配异常；2：ID编址失败；3：超时失败；4：IBG未在编址状态；5：BMS数量异常；20：找不到任何PE;21:apower PE_sn为0；22：PE数量少于设定值；23：PE数量大于设定值；24：BMS数量小于设定值；25：BMS数量大于设定值；26：BMS_SN和PE不匹配 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | SN_map | list |  PE和BMS SN映射结果 |
            | addressing_status | int | 编址状态，0：正在编址中，1：编址已结束 |
            | device_map | list | apower id号与SN和编址结果的组合 |
            | current_SN | list | 当前设备SN信息? |
            | apbox2.0_number | int | 识别到的abox2.0数量，最大为4 |
            | apbox2.0_number | int | apobox2.0 id和SN组合和编址结果列表 |
            | mas_model | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Addressing Get |
        | ${status} = | Local Addressing Get Result Check | ${response} | opt=0 |  device_number=3 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
