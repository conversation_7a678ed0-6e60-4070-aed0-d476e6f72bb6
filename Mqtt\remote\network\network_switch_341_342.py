from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 网络终端开关设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 341

cmd_type_c2s = 342

attr_map_s2c = {

    **attr_map_common_opt,

    'ethernet0NetSwitch': ('Eth0', 'int'),
    'ethernet1NetSwitch': ('Eth1', 'int'),
    'wifiNetSwitch': ('WiFi', 'int'),
    '4GNetSwitch': ('4G', 'int'),

}

attr_map_c2s = {

    **attr_map_common,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class NetworkSwitch(object):

    def network_switch_set(self, *args, **kwargs):
        """  network switch set-T341/342

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Switch Set | opt=1 | WiFi=1 |
        | ${status} = | Network Switch Set | opt | WiFi=1 | opt=1 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def network_switch_set_result_check(self, _response, **kwargs):
        """ network switch set result check-T342

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | current_network | int | 当前联网方式，0：未入网；1：ethernet0；2：ethernet1；3：wifi；4：4G/5G |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Network Switch Set | opt=1 | WiFi=1 |
        | ${status} = | Network Switch Set Result Check | ${response} | WiFi=1  | opt=1 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def network_switch_set_from_aws_get(self, *args, **kwargs):
        """  network switch set from AWS,the message get-T341

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Switch Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def network_switch_set_from_aws_get_check(self, **kwargs):
        """  network switch set from AWS,the message get check-T341

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Switch Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_switch_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_switch_set_from_device_get(self, *args, **kwargs):
        """   network switch set response from device,the message get-T342

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Switch Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def network_switch_set_from_device_get_check(self, **kwargs):
        """  network switch set response from device,the message get check-T342

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Network Switch Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_switch_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_switch_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T341/342

        Args for T341:
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Args for T342：
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 341/342 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=341 | rx_time_window=300 |
        | ${status} | Network Switch Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_switch_set_from_device_get, cmd_type_s2c: self.network_switch_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def network_switch_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T341/342

        Kwargs for T341:
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs for T342:
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 341/342 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=342 | rx_time_window=300 | filter_mode=and |
        | ${status} | Network Switch Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_switch_set_from_device_get_check, cmd_type_s2c: self.network_switch_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
