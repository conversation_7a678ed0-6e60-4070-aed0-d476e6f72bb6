from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 发电机参数设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 323

cmd_type_c2s = 324

attr_map_s2c = {

    **attr_map_common_opt,

    'genEn': ('generator_enabled', 'int'),
    'gridVoltCheck': ('generator_startup_type', 'int'),
    'mode': ('', 'int'),
    'genModel': ('model', 'string'),

    'genRatedPower': ('rated_power', 'int'),
    'genOptiPPoint': ('optimal_power', 'int'),
    'startDelTime': ('start_delay_time', 'int'),

    'genStartElec': ('start_energy', 'int'),
    'genCloseElec': ('stop_energy', 'int'),
    'genStat': ('generator_status', 'int'),
    'manuSw': ('manual_switch', 'int'),

    'power': ('', 'int'),
    'curr': ('current', 'int'),
    'volt': ('voltage', 'int'),
    'freq': ('frequency', 'int'),


    'genpowerGen': ('generated_energy', 'int'),

    'charge1En': ('charging_time1_enable', 'int'),
    'charge1StartTime': ('charging_time1_start', 'string'),
    'charge1EndTime': ('charging_time1_end', 'string'),

    'charge2En': ('charging_time2_enable', 'int'),
    'charge2StartTime': ('charging_time2_start', 'string'),
    'charge2EndTime': ('charging_time2_end', 'string'),

    'charge3En': ('charging_time3_enable', 'int'),
    'charge3StartTime': ('charging_time3_start', 'string'),
    'charge3EndTime': ('charging_time3_end', 'string'),

    'oilmanoEn': ('manoeuvre_enabled', 'int'),
    'manoFre': ('manoeuvre_frequency', 'string'),
    'manoDate': ('manoeuvre_date', 'string'),

    'manoStartTime': ('manoeuvre_start_time', 'string'),
    'manoTime': ('manoeuvre_duration', 'int'),
    'manoManExit': ('manoeuvre_manual_exit_time', 'int'),


    'v2LModeEnable': ('V2L_mode_enabled', 'int'),
    'v2LStartTimeout': ('V2L_start_timeout', 'int'),
    'v2LCarRatedPower': ('V2L_car_rated_power', 'int'),

    'v2LModeCtrl': ('V2L_mode_control', 'int'),
    'v2LRunState': ('V2L_running_status', 'int'),
    'v2lChargeEna': ('V2L_charging_enabled', 'int'),

    'genAccessMethod': ('generator_access_method', 'int'),
    'v2lAccessMethod': ('V2L_access_method', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class GeneratorSet(object):

    def generator_set(self, *args, **kwargs):
        """  Generator set-T323/324

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Generator Set | opt=0 |
        | ${status} = | Generator Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def generator_set_result_check(self, _response, **kwargs):
        """  Generator set result check-T324

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Generator Set | opt=1 | generator_enabled=1 |
        | ${status} = | Generator Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def generator_set_from_aws_get(self, *args, **kwargs):
        """  Generator set from AWS,the message get-T323

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Generator Set From AWS Get | generator_enabled |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def generator_set_from_aws_get_check(self, **kwargs):
        """  Generator set from AWS,the message get check-T323

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Generator Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.generator_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def generator_set_from_device_get(self, *args, **kwargs):
        """   Generator set response from device,the message get-T324

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Generator Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def generator_set_from_device_get_check(self, **kwargs):
        """  Generator set response from device,the message get check-T324

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Generator Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.generator_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def generator_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T323/324

        Args for T323:
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Args for T324：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 323/324 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=323 | rx_time_window=300 |
        | ${status} | Generator Set From Msg Get | generator_enabled | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.generator_set_from_device_get, cmd_type_s2c: self.generator_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def generator_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T323/324

        Kwargs for T323:
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs for T324：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | generator_enabled | int | 发电机使能，0：不使能，1：使能 |
            | generator_startup_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型；4：移动油机 |
            | mode | int | 工作模式，1：自动；2：调试 |
            | model | string | 发电机型号 |
            | rated_power | int | 发电机额定功率，单位：0.1kw，0~40kw |
            | optimal_power | int | 发电机最佳工作点，单位：%，20~100 |
            | start_delay_time | int | 发电机启动延迟时间，300~3600，单位：s |
            | start_energy | int | 发电机启动电量，单位：%，10~80 |
            | stop_energy | int | 发电机关闭电量，单位：%，+20~100 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障；6：演习中 |
            | manual_switch | int | 0：不操作；1：手动关；2：手动开 |
            | power | int | 发电机功率，单位：0.1kw |
            | current | int | 发电机电流，单位：0.1A |
            | voltage | int | 发电机电压，单位：0.1V |
            | frequency | int | 发电机频率，单位：0.1Hz |
            | generated_energy | int | 发电机当日总发电量，单位：0.1kwh |
            | charging_time1_enable | int | 充电时间段1使能,0:关闭，1：开启 |
            | charging_time1_start | string | 充电时间段1开始时间 |
            | charging_time1_end | string | 充电时间段1结束时间 |
            | charging_time2_enable | int | 充电时间段2使能,0:关闭，1：开启 |
            | charging_time2_start | string | 充电时间段2开始时间 |
            | charging_time2_end | string | 充电时间段2结束时间 |
            | charging_time3_enable | int | 充电时间段3使能,0:关闭，1：开启 |
            | charging_time3_start | string | 充电时间段3开始时间 |
            | charging_time3_end | string | 充电时间段3结束时间 |
            | manoeuvre_enabled | int | 发电机演习使能，0：未使能，1：使能 |
            | manoeuvre_frequency | string | 发电机演习频率，0：仅一次，7：每周，14：每双周，28：每月 |
            | manoeuvre_date | string | 发电机演习日期，0~6,0代表星期天 |
            | manoeuvre_start_time | string | 发电机演习开始时间 |
            | manoTime': ('manoeuvre_duration | int | 发电机演习时长 |5~60，单位：分钟 |
            | manoeuvre_manual_exit_time | int | 发电机演习手动退出，0：正常运行，1：退出 |
            | V2L_mode_enabled | int | V2L模式开关，0：关闭紧急电源使能，1：打开紧急电源模式使能开关 |
            | V2L_start_timeout | int | V2L接入时闭合发电机继电器超时时间，单位：秒 |
            | V2L_car_rated_power | int | V2L模式下，电动车输出额定功率，-1：不限制，单位：w |
            | V2L_mode_control | int | V2L是否接入，1：退出V2LM模式，2：进入V2L模式，闭合油机继电器接入电车供载 |
            | V2L_running_status | int | V2L 运行状态，0：不使能 1：停机 2：启动  3：运行 4：退出 5：故障 |
            | V2L_charging_enabled | int | V2L 是否给aPower充电,0:否，1：是 |
            | generator_access_method | int | 发电机接入方式,0：aGate,1:aPbox2.0 |
            | V2L_access_method | int | V2L接入方式，0：aGate,1:aPbox2.0 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 323/324 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=324 | rx_time_window=300 | filter_mode=and |
        | ${status} | Generator Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.generator_set_from_device_get_check, cmd_type_s2c: self.generator_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
