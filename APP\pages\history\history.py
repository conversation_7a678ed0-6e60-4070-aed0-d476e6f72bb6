from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException

from APP.base import Base
from time import sleep
from robot.api import logger


class History(Base):

    def history_data_show_by_date(self, value='forward'):

        if forward:

            locator = "//android.view.View[@content-desc=\"07/09/24\"]/android.widget.ImageView[2]"
        else:
            locator = "//android.view.View[@content-desc=\"07/10/24\"]/android.widget.ImageView"

        ele = self.find_element_by_xpath(locator, click=True)

    def history_data_show_by_date_unit(self, value='Day'):

        locator = f"//android.widget.Button[@content-desc=\"{value}\"]/android.widget.ImageView"

        ele = self.find_element_by_xpath(locator, click=True)

    def history_data_show_by_component(self, value='Home'):

        if value in ['Home', 'Solar', 'Grid']:

            locator = "Home"

            ele = self.find_element_by_accessibility_id(locator, click=True)

        elif value in ['aPower']:

            locator = "//android.widget.Button[@content-desc=\"aPower\"]"

            ele = self.find_element_by_xpath(locator, click=True)
