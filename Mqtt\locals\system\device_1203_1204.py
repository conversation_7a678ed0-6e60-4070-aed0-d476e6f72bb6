from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 用户能源政策查询设置

cmd_type_c2s = 1203

cmd_type_s2c = 1204

attr_map_c2s = {

    **attr_map_common_opt,

    'policy': ('', 'int'),
    'gridPowerDown': ('grid_power_enabled', 'int'),
    'gridPowerUpPeriod': ('grid_power_disabled_duration', 'list'),
    'gridPowerDownPeriod': ('grid_power_enabled_duration', 'list'),

    'packageState': ('state', 'int'),
    'packageArea': ('area', 'int'),
    'eleCompany': ('company', 'int'),
    'eleCompliance': ('compliance', 'int'),

    'modeChoose': ('mode', 'int'),
    'defaultMode': ('defafult_mode', 'int'),
    'solarPower': ('pv_access_enabled', 'int'),
    'dischargePower': ('discharge_power', 'int'),

    'dischargeStartTime': ('discharge_start_time', 'string'),
    'dischargeEndTime': ('discharge_stop_time', 'string'),
    'rampTime': ('ramp_time', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalEnergyPolicy(Base):

    def local_energy_policy_set(self, *args, **kwargs):
        """  local energy policy set-T1203
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败，2：设置失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | policy | int | 政策 |
            | grid_power_enabled | int | 电网取电， 0：禁止取电；1：允许取电 |
            | grid_power_disabled_duration | list | 禁止倒送时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | grid_power_enabled_duration | list | 允许电网取电时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | state | int | 州，1：其他；2：夏威夷州；3：加州（默认1） 4：澳洲；5：波多黎各；6：新西兰 |
            | area | int | 区域 ，0：其他；1：Oahu 欧胡岛； 2：Maui 毛伊岛；3：Lanai 拉奈岛；4：Molokai 莫洛凯岛 5：加州 6：澳洲 7：波多黎各 8：新西兰 |
            | company | int | 电力公司，0：其他；1：HECO 夏威夷电力公司；2：SDGE 加州电力公司；3：SCE 加州电力公司；4：PG&E 加州电力公司；5：毛伊电力公司；6：夏威夷电力照明公司 |
            | compliance | int | 法规，0：UserDefined；1：HECO SRD V2.0；2：CA UL 1741-SA |
            | mode | int | 可选模式，bit表示，1：备电；2：自发自用；3：TOU；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | defafult_mode | int | 默认模式，1：备电；2：自发自用；3：TOU |
            | pv_access_enabled | int | 是否接入光伏，0：禁止，1：接入 |
            | discharge_power | int | 合约放电功率，0~5*apower数量kw，单位：0.1kw |
            | discharge_start_time | string | 合约起始放电开始时间，例如00:00-23:59 |
            | discharge_stop_time | string | 合约起始放电结束开始时间，例如00:00-23:59 |
            | ramp_time | int | 爬坡时间，100~500，单位：秒 |
        Kwargs:
            | opt | int | 操作类型，0：查询，1:设置 |
            | policy | int | 政策 |
            | grid_power_enabled | int | 电网取电， 0：禁止取电；1：允许取电 |
            | grid_power_disabled_duration | list | 禁止倒送时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | grid_power_enabled_duration | list | 允许电网取电时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | state | int | 州，1：其他；2：夏威夷州；3：加州（默认1） 4：澳洲；5：波多黎各；6：新西兰 |
            | area | int | 区域 ，0：其他；1：Oahu 欧胡岛； 2：Maui 毛伊岛；3：Lanai 拉奈岛；4：Molokai 莫洛凯岛 5：加州 6：澳洲 7：波多黎各 8：新西兰 |
            | company | int | 电力公司，0：其他；1：HECO 夏威夷电力公司；2：SDGE 加州电力公司；3：SCE 加州电力公司；4：PG&E 加州电力公司；5：毛伊电力公司；6：夏威夷电力照明公司 |
            | compliance | int | 法规，0：UserDefined；1：HECO SRD V2.0；2：CA UL 1741-SA |
            | mode | int | 可选模式，bit表示，1：备电；2：自发自用；3：TOU；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | defafult_mode | int | 默认模式，1：备电；2：自发自用；3：TOU |
            | pv_access_enabled | int | 是否接入光伏，0：禁止，1：接入 |
            | discharge_power | int | 合约放电功率，0~5*apower数量kw，单位：0.1kw |
            | discharge_start_time | string | 合约起始放电开始时间，例如00:00-23:59 |
            | discharge_stop_time | string | 合约起始放电结束开始时间，例如00:00-23:59 |
            | ramp_time | int | 爬坡时间，100~500，单位：秒 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Energy Policy Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_energy_policy_set_result_check(self, _response, **kwargs):
        """  local energy policy set result check-T1204

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败，2：设置失败 |
            | opt | int | 操作类型，0：查询，1:设置 |
            | policy | int | 政策 |
            | grid_power_enabled | int | 电网取电， 0：禁止取电；1：允许取电 |
            | grid_power_disabled_duration | list | 禁止倒送时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | grid_power_enabled_duration | list | 允许电网取电时间段，[1,1,0,0...],间隔30分钟，共48个点；1：允许；0：禁止 |
            | state | int | 州，1：其他；2：夏威夷州；3：加州（默认1） 4：澳洲；5：波多黎各；6：新西兰 |
            | area | int | 区域 ，0：其他；1：Oahu 欧胡岛； 2：Maui 毛伊岛；3：Lanai 拉奈岛；4：Molokai 莫洛凯岛 5：加州 6：澳洲 7：波多黎各 8：新西兰 |
            | company | int | 电力公司，0：其他；1：HECO 夏威夷电力公司；2：SDGE 加州电力公司；3：SCE 加州电力公司；4：PG&E 加州电力公司；5：毛伊电力公司；6：夏威夷电力照明公司 |
            | compliance | int | 法规，0：UserDefined；1：HECO SRD V2.0；2：CA UL 1741-SA |
            | mode | int | 可选模式，bit表示，1：备电；2：自发自用；3：TOU；5：BB&NEM；6：BB&CSS；7：BB&CGS+ |
            | defafult_mode | int | 默认模式，1：备电；2：自发自用；3：TOU |
            | pv_access_enabled | int | 是否接入光伏，0：禁止，1：接入 |
            | discharge_power | int | 合约放电功率，0~5*apower数量kw，单位：0.1kw |
            | discharge_start_time | string | 合约起始放电开始时间，例如00:00-23:59 |
            | discharge_stop_time | string | 合约起始放电结束开始时间，例如00:00-23:59 |
            | ramp_time | int | 爬坡时间，100~500，单位：秒 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Energy Policy Set |
        | ${status} = | Local Energy Policy Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
