from appium.webdriver.common.appiumby import AppiumBy
from selenium.common.exceptions import (NoSuchElementException, TimeoutException)
from selenium.webdriver.support.wait import WebDriverWait
# from selenium.webdriver.support.ui import WebDriverWait  #???
from selenium.webdriver.support import expected_conditions as EC
from time import (sleep, time)
from robot.api import logger

method_dict = {'id': AppiumBy.ID,
               'xpath': AppiumBy.XPATH,
               'cls_name': AppiumBy.CLASS_NAME,
               'acc_id': AppiumBy.ACCESSIBILITY_ID,
               'name': AppiumBy.NAME,
               'android': AppiumBy.ANDROID_UIAUTOMATOR,
               'viewtag': AppiumBy.ANDROID_VIEWTAG,
               'data_matcher': AppiumBy.ANDROID_DATA_MATCHER,
               'view_matcher': AppiumBy.ANDROID_VIEW_MATCHER,
               'css': AppiumBy.CSS_SELECTOR,
               'chain': AppiumBy.IOS_CLASS_CHAIN,
               'predicate': AppiumBy.IOS_PREDICATE,
               }


class Location(object):

    def _find_element(self, method, locator):

        return self.driver.find_element(method, locator)

    def _find_elements(self, method, locator):

        return self.driver.find_elements(method, locator)

    def find_element_by_id(self, locator, **kwargs):

        return self.find_element('id', locator, **kwargs)

    def find_element_by_xpath(self, locator, **kwargs):

        return self.find_element('xpath', locator, **kwargs)

    def find_element_by_accessibility_id(self, locator, **kwargs):

        return self.find_element('acc_id', locator, **kwargs)

    def find_element_by_class_name(self, locator, **kwargs):

        return self.find_element('cls_name', locator, **kwargs)

    def find_element(self, method, locator, multiple=False, click=False, first_click=False, clear=False, send_keys=None, throw_exception=True, capture_screen=True, element_located_check=False, element_visible_check=True, timeout=20, retry=1, auto_swipe=False, loading_detect=False, will_do_loading_detect=False, max_auto_swipe_number=1):
        """
        will_do_loading_detect:False(by default), it will automatically detect the 'Loading...' after activate the element
        """
        logger.info(f'the user kwargs:method:{method},multiple:{multiple},click:{click},clear:{clear},'
                    f'send_keys:{send_keys},throw_exception:{throw_exception},capture_screen:{capture_screen},first_click:{first_click},'
                    f'element_located_check:{element_located_check},element_visible_check:{element_visible_check},timeout:{timeout},retry:{retry}',
                    f'will_do_loading_detect:{will_do_loading_detect}'
                    )
        old_implicitly_wait_time = self.implicitly_wait_time

        logger.info(f'the current implicitly_wait time:{self.implicitly_wait_time}')

        time_change_flag = self.implicitly_wait_time > timeout

        if time_change_flag:

            self.driver.implicitly_wait(timeout)

        wait = WebDriverWait(self.driver, timeout, 0.1)

        pars = (method_dict[method], locator)

        ele = None

        auto_swipe_number = 0

        loading_flag = False

        try:
            if multiple:

                if element_located_check:

                    ele = wait.until(EC.presence_of_all_elements_located(pars))

                if element_visible_check:

                    ele = wait.until(EC.visibility_of_any_elements_located(pars))

                else:

                    ele = self.driver.find_elements(method_dict[method], locator)
            else:

                for i in range(retry):

                    logger.debug(f"the current loop number:{i}!")

                    try:
                        if element_located_check:

                            ele = wait.until(EC.presence_of_element_located(pars))

                        if element_visible_check:

                            ele = wait.until(EC.visibility_of_element_located(pars))

                            if click or first_click:

                                ele = wait.until(EC.element_to_be_clickable(pars))

                        else:

                            ele = self.driver.find_element(method_dict[method], locator)

                    except (TimeoutException, NoSuchElementException):

                        logger.info("Fail to load the element due to timeout!")

                        # deal with the loading scenario...

                        while True and loading_detect:

                            try:

                                ele_loading = self.driver.find_element(AppiumBy.ACCESSIBILITY_ID, 'Loading...')

                                logger.info('Detect the loading...')

                                sleep(1)

                            except NoSuchElementException:

                                logger.info('OK,the loading window is gone!')

                                loading_flag = True

                                break

                        if i == retry - 2:

                            if capture_screen:

                                self.save_screenshot()

                        if i == retry - 1:

                            if throw_exception:

                                if auto_swipe and auto_swipe_number == max_auto_swipe_number:

                                    raise

                                elif not auto_swipe:

                                    if not loading_flag:

                                        logger.info('OK,throw out the exception...')

                                        raise

                        if auto_swipe and auto_swipe_number < max_auto_swipe_number:

                            self.swipe_up()

                            auto_swipe_number += 1

        except Exception as e:

            # NoSuchElementException,timeout,TimeoutException,StaleElementReferenceException,ElementNotVisibleException,ElementNotSelectableException?

            logger.info(f'got the exception....{e}')

            if capture_screen:

                self.save_screenshot()

            if throw_exception:

                raise e

            else:

                logger.info(f"detect the exception:{e}!")

                if time_change_flag:

                    self.driver.implicitly_wait(old_implicitly_wait_time)

        if ele is not None:

            try:
                if first_click:

                    ele.click()

                if clear:

                    ele.clear()

                if click:

                    ele.click()

                if send_keys is not None:

                    ele.send_keys(send_keys)

            except Exception as e:

                logger.info(f'got the exception again....{e}')

                if throw_exception:

                    raise e

        while will_do_loading_detect:

            try:

                ele_loading = self.driver.find_element(AppiumBy.ACCESSIBILITY_ID, 'Loading...')

                logger.info('Detect the loading...')

                sleep(0.5)

            except NoSuchElementException:

                logger.info('OK,the loading window is gone!')

                break

        if time_change_flag:

            self.driver.implicitly_wait(old_implicitly_wait_time)

        logger.info(f'the current implicitly_wait time is restored to {old_implicitly_wait_time}')

        return ele

    def get_element_attribute(self, element, key='text'):
        """key:
            clickable
            checkable (ele.is_checked())
            resouceId
            className
            text
            checked
            enabled
            focusable
            focused
            scrollable
            name: return content-desc, will return text if content-desc is empty
        """
        if key == 'text':
            ret = element.text

        # tag_name->class attribute
        elif key == 'tag_name':
            ret = element.tag_name

        # content-desc->text if content-desc is empty, otherwise, return content-desc

        elif key == 'content-desc':
            ret = element.get_attribute(key)

        elif key == 'is_selected':
            ret = element.is_selected()

        else:
            ret = element.get_attribute(key)

        return ret

    def _get_attribute_value_by_locator(self, locator, locator_fun=None, ele_find_func=None, attr_get_func=None, attr_name='content-desc', ctrl_enter_strip=True, ctrl_empty_strip=True, ctrl_lower_case=True, replace_empty_to_dash=False, auto_scroll=False, **kwargs):
        """Given locator name, return the attribute of the WEB element

        """
        logger.info(f'the user kwargs:locator:{locator},locator_fun:{locator_fun},'
                    f'ele_find_func:{ele_find_func},attr_get_func:{attr_get_func},attr_name:{attr_name}'
                    f'ctrl_enter_strip:{ctrl_enter_strip},ctrl_empty_strip:{ctrl_empty_strip},ctrl_lower_case:{ctrl_lower_case},'
                    f'replace_empty_to_dash:{replace_empty_to_dash},auto_scroll:{auto_scroll},kwargs:{kwargs}'
                    )
        try:

            ele = self._get_locator_and_find_element(locator, locator_fun=locator_fun, ele_find_func=ele_find_func, **kwargs)

        except (NoSuchElementException, TimeoutException) as e:

            logger.info(f'Sorry,could not find the element for {locator}!')

            # try to scroll to find the element...

            if auto_scroll:

                # scroll down firstly

                ele = self._scroll_to_find_element(locator, locator_fun, ele_find_func, direction='down', **kwargs)

                if ele is None:

                    # scroll up secondly

                    ele = self._scroll_to_find_element(locator, locator_fun, ele_find_func, direction='up', **kwargs)

                if ele is None:

                    raise ValueError(f'Fail to find the element for {locator}')
            else:

                raise

        value = attr_get_func(ele, attr_name)

        logger.debug(f'OK,got the value:{value} for {attr_name}')

        if ctrl_enter_strip:

            value = value.split('\n')[-1]

        if ctrl_lower_case:

            value = value.lower()

        if ctrl_empty_strip:

            value = value.strip()

        if replace_empty_to_dash:

            value = value.replace(' ', '-')

        logger.debug(f'OK,the final return value is {value}')

        return value

    def _scroll_to_find_element(self, locator, locator_fun, ele_find_func, direction='down', timeout=60, **kwargs):

        logger.info(f'the user parameters:locator:{locator},locator_fun:{locator_fun},ele_find_func:{ele_find_func},direction：{direction}，kwargs：{kwargs}')

        found_flag = False

        ele = None

        start = time()

        while not found_flag:

            before = self.get_page_source()

            if direction == 'down':

                self.swipe_up()

            else:
                self.swipe_down()

            logger.info(f'OK,do scroll {direction}...')

            try:

                if not isinstance(locator, tuple):

                    locator = locator_fun(locator)

                logger.debug(f'ok, got the locator:{locator}')

                ele = ele_find_func(*locator, **kwargs)

                logger.info(f'OK,found the target element:{ele}!')

                found_flag = True

            except (NoSuchElementException, TimeoutException) as e:

                logger.info(f'Oops,fail to find the element:{locator}!')

                after = self.get_page_source()

                if before == after:

                    if direction == 'down':

                        value = 'bottom'

                    else:

                        value = 'top'

                    logger.info(f'Sorry,already scroll to the {value} of the page!')

                    break

            end = time()

            if (end - start) > timeout:

                raise ValueError(f'Fail to find the element:{locator} in {timeout} seconds!')

        return ele

    def _get_locator_and_find_element(self, locator, locator_fun=None, ele_find_func=None, auto_scroll=False, throw_exception=True, **kwargs):
        """ find the element by the given locator and do various actions to the target element.

            kwargs: for various actions for the element

        """
        logger.info(f'the user kwargs in _get_locator_and_find_element,locator:{locator},locator_fun:{locator_fun},'
                    f'ele_find_func:{ele_find_func},auto_scroll:{auto_scroll},throw_exception:{throw_exception},kwargs:{kwargs}'
                    )
        eles = None

        if not isinstance(locator, tuple):

            _locator = locator_fun(locator)

        else:

            _locator = locator

        logger.debug(f'OK,got the locator:{_locator}')

        if ele_find_func is None:

            ele_find_func = self.find_element

        try:

            eles = ele_find_func(*_locator, **kwargs)

        except (NoSuchElementException, TimeoutException) as e:

            logger.info(f'Sorry,could not find the element for {locator}!')

            # try to scroll to find the element...

            if auto_scroll:

                # scroll down firstly

                eles = self._scroll_to_find_element(locator, locator_fun, ele_find_func, direction='down', **kwargs)

                if eles is None:

                    # scroll up secondly

                    eles = self._scroll_to_find_element(locator, locator_fun, ele_find_func, direction='up', **kwargs)

                if eles is None:

                    raise ValueError(f'Fail to find the element for {locator}')
            else:

                if throw_exception:
                    raise

        return eles

    def get_locator_and_operate_element(self, locator, locator_fun=None, ele_find_func=None, auto_scroll=True, **kwargs):
        """ find the element with the given locator and do various actions to the target element.

            locator: locator name in locator file or tuple(method,locator)

            kwargs: for various actions for the element

        """
        logger.info(f'the user kwargs in get_locator_and_operate_element,locator:{locator},locator_fun:{locator_fun},'
                    f'ele_find_func:{ele_find_func},auto_scroll:{auto_scroll},kwargs:{kwargs}'
                    )

        if ele_find_func is None:

            ele_find_func = self.find_element

        return self._get_locator_and_find_element(locator, locator_fun=locator_fun, ele_find_func=ele_find_func, auto_scroll=auto_scroll, **kwargs)

    def get_element(self, locator=None, locator_fun=None):
        """ get element and return it for picture save process

        Kwargs:
            | locator | locator name or tuple(method,locator) |
            | locator_fun | work with locator_name in excel file |
        Return:
            | None |
        Examples:
            | Get Element | locator=locator_network_summary |
        """
        # value = "locator_network_summary"

        # locator = ('xpath','//android.view.View[contains(@content-desc,"circuit one")]/android.widget.ImageView')

        return self._get_locator_and_find_element(locator, locator_fun=locator_fun)
