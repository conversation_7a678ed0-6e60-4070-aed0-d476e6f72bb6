import base64
from cv2 import (imread, matchTemplate, TM_SQDIFF, TM_SQDIFF_NORMED, TM_CCORR, TM_CCORR_NORMED, TM_CCOEFF, TM_CCOEFF_NORMED)
from time import (time, strftime, localtime, sleep)
from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver import Action<PERSON>hains
from selenium.webdriver.common.actions import interaction
from selenium.webdriver.common.actions.action_builder import ActionBuilder
from selenium.webdriver.common.actions.pointer_input import PointerInput
from robot.api import logger
from ..util import log_as_html


HOME = 3   # main page KEYCODE_HOME
BACK = 4  # 返回键
DIALUP = 5  # 拨号键  KEYCODE_CALL
HANGDOWN = 6  # 挂机键 KEYCODE_ENDCALL
VOLUME_UP = 24  # 音量增大键
VOLUME_DOWN = 25  # 音量减小键
POWER = 26  # 电源键
SNAP = 27  # 拍照键
EXLORER = 64  # browser
ENTER = 66
SNAP_FOCUS = 80  # 拍照对焦键
MENU = 82  # 菜单键 KEYCODE_MENU
NOTIFICATION = 83  # 通知键
SEARCH = 84  # 搜索键
PHONE_MUTE = 91  # 话筒静音键
SPEAKER_MUTE = 164  # 扬声器静音键

net_conn_dict = {0: "no_connection",
                 1: "airplane_mode",
                 2: "wifi_only",
                 4: "data_only",
                 6: "all_network_on",
                 }


class SwipeMixin(object):

    COF_CONST = 0.5
    COF_CONST_1 = 0.75
    COF_CONST_2 = 1 - COF_CONST_1

    def swipe_up(self, start_x=COF_CONST, start_y=COF_CONST_1, end_x=COF_CONST, end_y=COF_CONST_2, duration=1000, number=1):
        """ swipe up

        Kwargs:
            | start_x | float | 默认为0.5，X起始坐标相对滑动系数  |
            | start_y | float | 默认为0.75，Y起始坐标相对滑动系数  |
            | end_x | float | 默认为0.5，X结束坐标相对滑动系数  |
            | end_y | float | 默认为0.25，Y结束坐标相对滑动系数  |
            | duration | float | 默认为1000，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的时间 |
            | number | int | 默认为1，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的连续次数 |
        Return:
            | None |
        Examples:
            | swipe up |
        """
        self._swipe(start_x=start_x * self.window_width, start_y=start_y * self.window_height,
                    end_x=end_x * self.window_width, end_y=end_y * self.window_height, duration=duration, number=number)

    def swipe_down(self, start_x=COF_CONST, start_y=COF_CONST_2, end_x=COF_CONST, end_y=COF_CONST_1, duration=1000, number=1):
        """ swipe down

        Kwargs:
            | start_x | float | 默认为0.5，X起始坐标相对滑动系数  |
            | start_y | float | 默认为0.25，Y起始坐标相对滑动系数  |
            | end_x | float | 默认为0.5，X结束坐标相对滑动系数  |
            | end_y | float | 默认为0.75，Y结束坐标相对滑动系数  |
            | duration | float | 默认为1000，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的时间 |
            | number | int | 默认为1，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的连续次数 |
        Return:
            | None |
        Examples:
            | swipe down |
        """
        self._swipe(start_x=start_x * self.window_width, start_y=start_y * self.window_height, end_x=end_x * self.window_width, end_y=end_y * self.window_height, duration=duration, number=number)

    def swipe_left(self, start_x=COF_CONST_1, start_y=COF_CONST, end_x=COF_CONST_2, end_y=COF_CONST, duration=1000, number=1):
        """ swipe left

        Kwargs:
            | start_x | float | 默认为0.75，X起始坐标相对滑动系数  |
            | start_y | float | 默认为0.5，Y起始坐标相对滑动系数  |
            | end_x | float | 默认为0.25，X结束坐标相对滑动系数  |
            | end_y | float | 默认为0.5，Y结束坐标相对滑动系数  |
            | duration | float | 默认为1000，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的时间 |
            | number | int | 默认为1，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的连续次数 |
        Return:
            | None |
        Examples:
            | swipe left |
        """
        self._swipe(start_x=start_x * self.window_width, start_y=start_y * self.window_height, end_x=end_x * self.window_width, end_y=end_y * self.window_height,
                    duration=duration, number=number)

    def swipe_right(self, start_x=COF_CONST_2, start_y=COF_CONST, end_x=COF_CONST_1, end_y=COF_CONST, duration=1000, number=1):
        """ swipe right

        Kwargs:
            | start_x | float | 默认为0.25，X起始坐标相对滑动系数  |
            | start_y | float | 默认为0.5，Y起始坐标相对滑动系数  |
            | end_x | float | 默认为0.75，X结束坐标相对滑动系数  |
            | end_y | float | 默认为0.5，Y结束坐标相对滑动系数  |
            | duration | float | 默认为1000，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的时间 |
            | number | int | 默认为1，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的连续次数 |
        Return:
            | None |
        Examples:
            | swipe right |
        """
        self._swipe(start_x=start_x * self.window_width, start_y=start_y * self.window_height, end_x=end_x * self.window_width, end_y=end_y * self.window_height, duration=duration, number=number)

    def swipe(self, start_x, start_y, end_x, end_y, duration=1000):
        """ swipe

        Kwargs:
            | start_x | float | X起始绝对坐标点 |
            | start_y | float | X起始绝对坐标点 |
            | end_x | float | X介绍绝对坐标点 |
            | end_y | float | Y结束绝对坐标点 |
            | duration | float | 默认为1000，单位ms，从(X,Y)起始坐标滑动到(X,Y)结束坐标的时间 |
        Return:
            | None |
        Examples:
            | swipe right |
        """
        self.driver.swipe(start_x, start_y, end_x, end_y, duration)

    def _swipe(self, **kwargs):

        logger.info(f'the user kwargs:{kwargs}')

        start_x = kwargs.pop('start_x')
        end_x = kwargs.pop('end_x')
        start_y = kwargs.pop('start_y')
        end_y = kwargs.pop('end_y')

        duration = kwargs.pop('duration')
        number = kwargs.pop('number')

        logger.info(f'the coordinate is {start_x},{start_y}-->{end_x},{end_y}')

        for i in range(number):

            self.driver.swipe(start_x, start_y, end_x, end_y, duration)

    def scroll(self, start_ele, end_ele, duration=100):
        """scroll from one element to another element
            duration:100ms(by default)
        """
        self.driver.scroll(start_ele, end_ele, duration)

    def find_element_in_drop_down_list_by_scroll(self, target_element_value=None, general_locator=None, key='content-desc',
                                                 click=True, offset_number=None, waiting_time=None, scroll_duration=200):

        logger.info(f'the user parameters:target_element_value:{target_element_value},general_locator：{general_locator}',
                    f'key:{key},click:{click},offset_number:{offset_number},waiting_time:{waiting_time}'
                    )

        found_flag = False

        ret_ele = None

        if waiting_time is not None:

            sleep(waiting_time)

        scroll_down_flag = False

        scroll_to_end = False

        reach_to_top_times = 0

        while not found_flag:

            ele_list = self.find_element(*general_locator, multiple=True)

            logger.debug(f'the current ele_list:{ele_list}')

            if offset_number is not None:

                ele_list.pop(offset_number)

            logger.debug(f'the final ele_list:{ele_list}')

            acc_id_list = [self.get_element_attribute(i, key=key) for i in ele_list]

            logger.debug(f'the current acc id list:{acc_id_list}')

            if target_element_value in acc_id_list:

                index = acc_id_list.index(target_element_value)

                ret_ele = ele_list[index]

                if click:

                    ret_ele.click()

                found_flag = True

                logger.info(f"OK,found the expected value:{target_element_value} in the drop-down list!")

            else:

                # scroll down by default...

                if not scroll_down_flag:

                    end_flag = self._scroll_down(ele_list, scroll_direction_down=True, key=key, general_locator=general_locator, offset_number=offset_number, scroll_duration=scroll_duration)

                    if end_flag:

                        logger.debug('OK, has reached to the bottom of the page!')

                        scroll_down_flag = True

                        scroll_to_end = True

                if scroll_to_end and not found_flag:

                    end_flag = self._scroll_down(ele_list, scroll_direction_down=False, key=key, general_locator=general_locator, offset_number=offset_number, scroll_duration=scroll_duration)

                    if end_flag:
                        logger.debug('OK, has reached to the top of the page!')
                        reach_to_top_times += 1

                        if reach_to_top_times > 1:

                            raise ValueError(f"Fail to find the target element:{target_element_value} in the drop-down list!")

        return ret_ele

    def _scroll_down(self, ele_list, scroll_direction_down=None, key=None, general_locator=None, offset_number=None, scroll_duration=10):

        ele_list_old = self.find_element(*general_locator, multiple=True)

        if offset_number is not None:

            ele_list_old.pop(offset_number)

        acc_id_list_old = [self.get_element_attribute(i, key=key) for i in ele_list_old]

        logger.info(f"the old ele_list is {acc_id_list_old}")

        ele1 = ele_list[-1]

        ele2 = ele_list[0]

        # scroll down...

        if scroll_direction_down:

            self.scroll(ele1, ele2, duration=scroll_duration)
            logger.debug(f'OK,scroll down...')

        else:

            self.scroll(ele2, ele1, duration=scroll_duration)
            logger.debug(f'OK,scroll up...')

        ele_list_new = self.find_element(*general_locator, multiple=True)

        if offset_number is not None:

            ele_list_new.pop(offset_number)

        acc_id_list_new = [self.get_element_attribute(i, key=key) for i in ele_list_new]

        logger.info(f"the new ele_list is {acc_id_list_new}")

        if acc_id_list_new == acc_id_list_old:

            if scroll_direction_down:

                logger.info('Sorry, the list has reached out the bottom!')

            else:

                logger.info('Sorry, the list has reached out the top!')

            return True

        else:

            return False


class Cookie(object):
    ...


class SysMixin(object):
    def get_sys_performace_metrics(self, app, item):
        """ item: cpuinfo/mem?/network traffic?/battery?
        """
        return self.driver.get_performance_data(app, item, 5)

    def get_sys_performace_types(self):
        """
        """
        return self.driver.get_performance_data_types()

    def get_sys_bars(self):

        return self.driver.get_system_bars()

    def get_sys_time(self, format=None):
        """ get system time from mobile phone

        Kwargs:
            | format | 时间格式，默认为None |
        Return:
            | string | 时间格式值，默认'YYYY-MM-DDTHH:mm:ssZ，例如2024-07-26T16:25:59+08:00 |
        Examples:
            | ${time} | Get Sys Time |
        """
        return self.driver.device_time


class NetworkMixin(object):

    def switch_wifi(self):
        self.driver.toggle_wifi()

    def set_network_speed(self, value="NetSpeed.LTE"):
        """value: NetSpeed.LTE
        """
        self.driver.set_network_speed(value)

    def get_network_connection(self):
        """ get current network connection status

        Kwargs:
            | None |
        Return:
            | string | no_connection/airplane_mode/wifi_only/data_only/all_network_on |
        Examples:
            | ${status} | Get Network Connection |
        """
        return net_conn_dict[self.driver.network_connection]

    def set_network_connection(self, mode=None):
        """ set current network connection status??

        Kwargs:
            | mode | no_connection/airplane_mode/wifi_only/data_only/all_network_on |
        Return:
            | None |
        Examples:
            | ${status} | Get Network Connection |
        """
        _dict = {0: "no_connection",
                 1: "airplane_mode",
                 2: "wifi_only",
                 3: "data_only",
                 4: "all_network_on",
                 }

        _net_conn_dict = {v: k for (k, v) in net_conn_dict.items()}

        return _net_conn_dict[self.driver.network_connection]


class AppMixin(object):

    def app_install(self, app, uninstall=False):
        """ install app

        Kwargs:
            | app |  string | app安装包名,带文件路径 |
            | uninstall | False(缺省），为True：先卸载已安装的app再安装指定的app |
        Return:
            | float | App安装时间 |
        Examples:
            | app install | D:/test/com.franklinwh.franklinwh2 |
            | app install | com.franklinwh.franklinwh2 |  uninstall=True |

        """
        logger.info(f'the user parameters: app:{app},uninstall:{uninstall}')

        if uninstall:
            if self.driver.is_app_installed(self.app_package):
                self.driver.remove_app(self.app_package)
                logger.info('OK,already unistalled the app!')

        start = time()

        self.driver.install_app(app)

        end = time()

        delta = end - start

        logger.info(f'the install time for APP is {delta} seconds')

        return f'{delta:.3f}'

    def app_uninstall(self, app):
        """ uninstall app

        Args:
            | app |  string | app包名 |
        Return:
            | float | App卸载时间 |
        Examples:
            | app uninstall | com.franklinwh.franklinwh2 |

        """
        logger.info(f'the user parameters:{app}')

        if self.driver.is_app_installed(app):

            start = time()

            self.driver.remove_app(app)

            end = time()

            delta = end - start

            return f'{delta:.3f}'

        else:
            logger.info(f'the app:{app} does not exist!')

    def app_is_installed(self, app=None):
        """ Check if the app is installed

        Kwargs:
            | app |  string | app名 |
        Return:
            | bool | true(已安装）/false(未安装） |
        Examples:
            | ${status} | app_is_installed | com.franklinwh.franklinwh2 |
        """
        if app is None:

            app = self.app_package

        return self.driver.is_app_installed(app)

    def app_launch(self):

        self.driver.launch_app()

    def app_background(self, duration=10):
        """ set app to run in background??

        Kwargs:
            | duration|  int | 设置app运行在后台持续的时间 |
        Return:
            | None |
        Examples:
            | app_background | duration=15 |
        """
        self.driver.background_app(duration)

    def app_close(self, app=None, timeout=5):
        """ close app

        Kwargs:
            | app | string | app package name |
            | timeout | float| second, unit:0.01,1s(by default) for Android only |
        Return:
            | None |
        Examples:
            | app close | com.franklinwh.franklinwh2 |
        """
        if app is None:

            app = self.app_package

        status = self.driver.terminate_app(app, timeout=int(timeout * 1000))

    def app_reset(self):
        """clear app data cache ??
        """
        print(help(self.driver))

        self.driver.reset()

    def app_activate(self, app=None):

        if app is None:
            app = self.app_package

        self.driver.activate_app(app)

    def app_quit(self, uninstall=False):
        """ quit app

        Kwargs:
            | uninstall | False(缺省），为True：退出前，先卸载已安装的app |
        Return:
            | None |
        Examples:
            | app quit |
            | app quit | uninstall=True |
        """
        if uninstall:
            self.driver.remove_app(app)

        self.driver.quit()

    @property
    def current_package(self):
        return self.driver.current_package


class LocationMixin(object):

    def get_location(self):
        """ get location
        """
        self.driver.location()

    def set_location(self, value):
        """ set location
            value： （X,Y,Z) like (49,123,10)
        """
        self.driver.location = value

    def switch_location_services(self):
        self.driver.toggle_location_services()


class ActionMixin(object):

    def open_notifications(self):
        """ device notifictaion page
        """
        self.driver.open_notifications()

    def set_value_in_scroll_element(self, value=None, ele_find_func=None, target_ele_find_func=None, target_locator=None, locator_fun=None, key='content-desc', click=True, scroll_ratio=0.3, confirm=True, confirm_locator="locator_general_confirm", cancel_locator="locator_general_cancel"):
        """ find the value in scroll elements and set it
        """
        if ele_find_func is None:

            ele_find_func = self._get_locator_and_find_element

        if target_ele_find_func is None:

            target_ele_find_func = self.find_element

        # get the scroll element

        ele = ele_find_func(target_locator, locator_fun=locator_fun, ele_find_func=target_ele_find_func)

        # get the current value

        cur_value = self.get_element_attribute(ele, key=key)

        logger.info(f'the current value is:{cur_value}')

        if cur_value == value:

            if confirm:

                self.get_locator_and_operate_element(confirm_locator, locator_fun=locator_fun, click=True)

            return

        found_flag = False

        scroll_to_end_down = False

        while not found_flag:

            if not scroll_to_end_down:

                if cur_value != value:

                    ret = self._search_the_value_in_scroll(ele, exp_value=value, action='scroll_up', scroll_ratio=scroll_ratio, key=key)

                    if ret == 'MATCH':

                        found_flag = True

                        if confirm:

                            self.get_locator_and_operate_element(confirm_locator, locator_fun=locator_fun, click=True)

                        else:
                            self.get_locator_and_operate_element(cancel_locator, locator_fun=locator_fun, click=True)

                    if ret == 'END':

                        logger.info('scroll to the bottom of the list!')

                        scroll_to_end_down = True

            else:

                if cur_value != value:

                    ret = self._search_the_value_in_scroll(ele, exp_value=value, action='scroll_down', scroll_ratio=scroll_ratio, key=key)

                    if ret == 'MATCH':

                        found_flag = True

                        if confirm:

                            self.get_locator_and_operate_element(confirm_locator, locator_fun=locator_fun, click=True)

                        else:

                            self.get_locator_and_operate_element(cancel_locator, locator_fun=locator_fun, click=True)

                    if ret == 'END':

                        if not found_flag:

                            logger.info('scroll to the top of the list!')

                            raise ValueError(f'Fail to find the expected value:{value}!')

    def _search_the_value_in_scroll(self, ele, exp_value=None, action='scroll_up', scroll_ratio=0.3, key=None):

        cur_value = self.get_element_attribute(ele, key=key)

        logger.info(f'the current value is:{cur_value}')

        self.get_element_coordinate_and_action(ele, location='center', action=action, scroll_ratio=scroll_ratio)

        new_value = self.get_element_attribute(ele, key=key)

        logger.info(f'the new value is:{new_value}')

        if new_value == exp_value:

            logger.info(f'OK,found the matched value:{new_value}')

            return 'MATCH'

        if cur_value == new_value:

            return 'END'

    def get_element_coordinate_and_action(self, element, location='center', action='click', scroll_ratio=0.3):

        logger.info(f'the user parameters:element:{element},location:{location},action:{action}')

        x, y = element.location['x'], element.location['y']

        width, height = element.size['width'], element.size['height']

        center_x = x + width / 2

        center_y = y + height / 2

        point = None

        if location == 'center':

            point = (center_x, center_y)

        logger.info(f'the target target coordinate:{point}')

        if action == 'click':

            self.click_by_coordinate(*point)

        elif action == 'scroll_down':

            self.driver.flick(center_x, center_y, center_x, center_y + scroll_ratio * height)

        elif action == 'scroll_up':

            self.driver.flick(center_x, center_y, center_x, center_y - scroll_ratio * height)

        elif action == 'scroll_left':

            self.driver.flick(center_x, center_y, center_x - scroll_ratio * width, center_y)

        elif action == 'scroll_right':

            self.driver.flick(center_x, center_y, center_x + scroll_ratio * width, center_y)

        return point

    def click_by_coordinate(self, coord_x, coord_y, pause_duration=0.1, action='touch'):

        pause_duration = float(pause_duration)

        actions = ActionChains(self.driver)

        actions.w3c_actions = ActionBuilder(self.driver, mouse=PointerInput(interaction.POINTER_TOUCH, action))

        actions.w3c_actions.pointer_action.move_to_location(coord_x, coord_y)

        actions.w3c_actions.pointer_action.pointer_down()

        actions.w3c_actions.pointer_action.pause(pause_duration)

        actions.w3c_actions.pointer_action.release()

        actions.perform()

    def js_execute(self, code):
        # self.driver.execute_script('mobile: scroll', {'direction': 'up'})

        return self.driver.execute_script(code)

    def drag_and_drop(self, element1, element2):
        """drag one element to the 2nd element
        """
        return self.driver.drag_and_drop(element1, element2)

    def flick(self, start_x, start_y, end_x, end_y):

        return self.driver.flick(start_x, start_y, end_x, end_y)

    def tap(self, coordinates, duration=5000):
        """coordinates:tuple,muoltiple touch and tap
        """
        self.driver.tap([tuple(coordinates)], duration)

    def get_page_source(self):
        """ for debug, element location
        """
        return self.driver.page_source

    def hide_keyboard(self):

        return self.driver.hide_keyboard()

    def press_keycode(self, code):
        """ code:int, short key press for keyboard input
        """
        return self.driver.press_keycode(int(code))

    def long_press_keycode(self, code):
        """ code:int, long key press for keyboard input
        """
        return self.driver.long_press_keycode(int(code))

    def press_keyevent(self, code):
        """ code:int, short key press for physcial key
        """
        return self.driver.keyevent(int(code))

    def is_keyboard_displayed(self):

        return self.driver.is_keyboard_shown()

    def go_back(self):
        """
        """
        return self.driver.back()

    def set_page_timeout(self, value=5000):
        """ value: timeout value(5000ms by default)
        """
        return self.driver.set_page_load_timeout(value)

    def set_implicitly_wait(self, value=5):
        """ value: implicitly wait time:5s(by default)
        """
        self.driver.implicitly_wait(value)
        # self.driver.manage().timeouts().implicitlyWait(8000,Timenit.MILLISECONDS)

    def set_script_timeout(self, value=5000):
        """ value: script timeout time:5000ms(by default)
        """
        return self.driver.set_script_timeout(value)

    def get_orientation_mode(self):
        """ get orientation mode

        Kwargs:
            | None |
        Return:
            | string | LANDSCAPE(横屏)/PORTRAIT(竖屏) |
        Examples:
            | Get Orientation Mmode |
        """
        return self.driver.orientation

    def set_orientation_mode(self, value):
        """ set orientation mode(LANDSCAPE,etc)
        """
        self.driver.orientation = value

    def get_log_types(self):
        """ get log types
        """
        return self.driver.log_types

    def get_log(self, value):
        """ get log
        """
        return self.driver.get_log(value)

    def get_log_event(self, value1, value2):
        """ record log events
        """
        return self.driver.log_event(value1, value2)

    def get_event(self, *args):
        """ get events
        """
        if args:

            _value = list(args)

            return self.driver.get_events(_value)

        else:
            return self.driver.get_events()

    def update_device_settings(self, value):
        """ update device settings
            value：dict
        """
        self.driver.update_settings(value)

    def get_device_settings(self):
        """ get device settings
        """
        return self.driver.get_settings()

    def get_clipboard(self, text=True):

        if text:
            return self.driver.get_cliboard_text()
        else:
            return self.driver.get_cliboard()

    def push_file(self, dest, data):

        data = bytes(data, 'utf-8')

        self.driver.push_file(dest, base64.b64encode(data).decode('utf-8'))

    def pull_file(self, file):

        self.driver.pull_file(file)

    def pull_file_folder(self, folder):

        self.driver.pull_folder(folder)

    def shake(self):

        return self.driver.shake()

    def lock(self):

        return self.driver.lock()

    def unlock(self):

        return self.driver.unlock()

    def is_locked(self):
        """ check if screen is locked or not

        Kwargs:
            | None |

        Return:
            | bool | True(被锁屏）/False(未锁屏) |
        Examples:
            | ${status} | is_locked |
        """
        return self.driver.is_locked()

    def save_screenshot(self, file_path=None, file_name=None, element=None, picture_format='base64'):
        """ Save screenshot

        Kwargs:
            | file_path |  string | None(by default),将用当前系统保存的screenshot_path变量作为保存文件路径,如果screenshot_path变量未设置,则默认为'./' |
            | file_name | string | None(by default),将用当前系统时间生成默认的文件名 |
            | element | element object | None(by default), 对给定元素的图像进行保存|
            | picture_format | string | based64(by default)/png/file |
        Return:
            | string | 当picture_format 是 'png'或'base64' |
        Examples:
            | save_screenshot |
            | ${string} | save_screenshot | file_name=aaa.png |
            | ${ele} | find_element | acc_id | New |
            | save_screenshot | file_path=D:/  |
            | save_screenshot | file_path=D:/  | file_name=bbb.png |
            | save_screenshot | file_path=D:/  | file_name=new.png | element=${ele} |
            | save_screenshot | file_name=aaa.png  | picture_format=file |
            | ${file} | save_screenshot | picture_format=png |
        """
        logger.info(f'the user kwargs in save_screenshot,file_path:{file_path},file_name:{file_name},'
                    f'element:{element},picture_format:{picture_format}'
                    )

        if file_name is None:

            cur_time = strftime("%Y_%m_%d_%H_%M_%S", localtime(time()))

            _file_name = f'{cur_time}.png'

        else:
            _file_name = file_name

        if file_path is None:

            _file_path = self.screenshot_path + _file_name

        else:
            _file_path = file_path + _file_name

        logger.info(f'the screenshot is saved at {_file_path}')

        if element is not None:

            element.screenshot(_file_path)

            return _file_path

        else:

            if picture_format == 'base64':

                _string = self.driver.get_screenshot_as_base64()  # base64 string

                log_as_html(f'</td></tr><tr><td colspan="3">'
                            f'<img src="data:image/png;base64, {_string}" width="800px">')

                _file = base64.b64decode(_string)

                with open(_file_path, mode="wb") as f:

                    f.write(_file)

                return _string

            elif picture_format == 'png':

                _string = self.driver.get_screenshot_as_png()  # binary file

                with open(_file_path, mode="wb") as f:

                    f.write(_string)

                return _string

            else:

                self.driver.save_screenshot(_file_path)

    def start_record_screen(self, time_limit=1800):
        logger.info(f'the user parameter:time_limit:{time_limit}')

        self.driver.start_recording_screen(timeLimit=time_limit)

    def stop_record_screen(self, file_name=None):
        logger.info(f'the user parameter:file_name:{file_name}')

        record = self.driver.stop_recording_screen()

        _file = base64.b64decode(record)

        with open(file_name, mode="wb") as f:

            f.write(_file)

    def record_screen(self, file_path=None, file_name=None, duration=180):
        """ record screen

        Kwargs:
            | file_path |  string | None(by default),将用当前系统保存的screenshot_path变量作为保存文件路径,如果screenshot_path变量未设置,则默认为'./' |
            | file_name | string | None(by default),将用当前系统时间生成默认的文件名 |
            | duration | int | 1~1800,单位:秒,默认3分钟 |
        Return:
            | None |
        Examples:
            | record_screen | file_path=D:/  |
            | record_screen | file_path=D:/  | file_name=bbb.png |
            | ${file} | record_screen |
        """
        logger.info(f'the user kwargs in record_screen,file_path:{file_path},file_name:{file_name},'
                    f'duration:{duration}'
                    )
        file_suffix = 'mp4' if self.platform_name == 'Android' else 'ffmpeg'

        if file_name is None:

            cur_time = strftime("%Y_%m_%d_%H_%M_%S", localtime(time()))

            _file_name = f'{cur_time}.{file_suffix}'

        else:
            _file_name = file_name

        if file_path is None:

            _file_path = self.screenshot_path + _file_name

        else:
            _file_path = file_path + _file_name

        logger.info(f'the recorded scree file is saved at {file_path}')

        self.driver.start_recording_screen(timeLimit=1800)

        WebDriverWait(self. driver, 60)

        self.driver.hide_keyboard()

        sleep(duration)

        record = self.driver.stop_recording_screen()

        _file = base64.b64decode(record)

        with open(_file_path, mode="wb") as f:

            f.write(_file)


class ContextMixin(object):
    """ for HTML5 page
    """
    @property
    def current_context(self):
        """ current web view window
        """
        return self.driver.current_context

    def switch_to_context(self, context):

        return self.driver.switch_to.context(context)


class ActivityMixin(object):

    @property
    def current_activity(self):
        """ Get current activity

        Kwargs:
            | None |
        Return:
            | string | 当前的activity名字 |
        Examples:
        | ${app} | Connect | platform_name=Android | platform_version=12 | device_name=23f5a34c |
        | log | ${app.current_activity}  |
        """
        return self.driver.current_activity

    def wait_activity(self, activity, duration=10, interval=0.1):
        """ Wait activity to show,仅仅适用于安卓平台

        Kwargs:
            | activity | string | app activity名 |
            | duration | float |  unit: seconds, 默认为10秒，最大的activity启动出现的等待时间 |
            | interval | float |  unit: seconds,默认为0.1秒，查询周期 |
        Return:
            | None |
        Examples:
        | Wait Activity | com.franklinwh.franklinwh2.MainActivity | duration=5 |
        """
        self.driver.wait_activity(activity, duration, interval)


class ImageCompare(object):

    def compare_picture(self, target_pic, temp_pic, template=TM_SQDIFF_NORMED, threshold=0.01):

        logger.info(f'the user kwargs in compare_picture,target_pic:{target_pic},temp_pic:{temp_pic},'
                    f'template:{template},threshold:{threshold}'
                    )

        ret = False

        target = imread(target_pic)

        temp = imread(temp_pic)

        result = matchTemplate(target, temp, template)[0][0]

        logger.debug(f'OK, got the init picture comparsion result:{result}')

        if template in [TM_SQDIFF, TM_SQDIFF_NORMED]:

            # threshold shall be 0 for total match,should be <0.1?

            if result == 0.0:

                ret = True

            elif result < threshold:

                ret = True

        elif template in [TM_CCORR, TM_CCORR_NORMED, TM_CCOEFF, TM_CCOEFF_NORMED]:

            # threshold shall be 1 for total match,should be >0.95 for TM_CCORR, TM_CCORR_NORMED ？

            # threshold shall be 1 for total match, should be >0.36 for TM_CCOEFF, TM_CCOEFF_NORMED?

            if result == 1.0:

                ret = True

            elif result > threshold:

                ret = True

        logger.info(f'OK, got the final picture comparsion result:{ret}')

        return ret
