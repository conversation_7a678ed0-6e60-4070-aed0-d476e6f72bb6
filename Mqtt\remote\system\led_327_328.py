from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# aPower等开关控制

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 327

cmd_type_c2s = 328

attr_map_s2c = {

    **attr_map_common_opt,

    'lightStat': ('led_status', 'int'),
    'timeEn': ('timer_enabled', 'list'),
    'lightOpenTime': ('led_on_time', 'string'),
    'lightCloseTime': ('led_off_time', 'string'),

    'rgb': ('', 'int'),
    'bight': ('brightness', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class LedSet(object):

    def apower_led_set(self, *args, **kwargs):
        """  apower LED set-T327/328

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Apower LED Set | opt=0 |
        | ${status} = | Apower LED Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def apower_led_set_result_check(self, _response, **kwargs):
        """  apower LED set result check-T328

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Apower LED Set | opt=0 |
        | ${status} = | Apower LED Set Result Check | ${response} | result=0  | led_status=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def apower_led_set_from_aws_get(self, *args, **kwargs):
        """  apower LED set from AWS,the message get-T327

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   Apower LED Set From AWS Get | rgb |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def apower_led_set_from_aws_get_check(self, **kwargs):
        """  apower LED set from AWS,the message get check-T327

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Apower LED Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.apower_led_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apower_led_set_from_device_get(self, *args, **kwargs):
        """   apower LED set response from device,the message get-T328

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Apower LED Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def apower_led_set_from_device_get_check(self, **kwargs):
        """  apower LED set response from device,the message get check-T328

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Apower LED Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.pv_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apower_led_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T327/328

        Args for T327:
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Args for T328：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 327/328 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=327 | rx_time_window=300 |
        | ${status} | Apower LED Set From Msg Get | brightness | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apower_led_set_from_device_get, cmd_type_s2c: self.apower_led_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def apower_led_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T327/328

        Kwargs for T327:
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Kwargs for T328：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | led_status | int |  aPower等开关状态，最大15台，0：离线，1:关闭，2：开启 |
            | timer_enabled | list | 定时使能，0：不使能，1：使能 |
            | led_on_time | string | 定时开启时间 |
            | led_off_time | string | 定时关闭时间 |
            | rgb | int |  RGB颜色控制0~255，R:bit0~bit7,G:bit8~bit15,B:bit16~bit23 |
            | brightness | int | 灯带亮度控制，单位：%，颗粒度：5%，范围：30~70% |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 327/328 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=328 | rx_time_window=300 | filter_mode=and |
        | ${status} | Apower LED Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apower_led_set_from_device_get_check, cmd_type_s2c: self.apower_led_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
