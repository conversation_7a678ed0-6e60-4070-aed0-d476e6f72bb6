from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 日电量报表上传

app_name = "energy"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 411

cmd_type_s2c = 412

attr_map_s2c = {

    **attr_map_common_result,
}

attr_map_c2s = {

    'peak_grid_in': ('', 'int'),
    'peak_grid_out': ('', 'int'),
    'peak_pv_in': ('', 'int'),
    'peak_gen_in': ('', 'int'),
    'peak_fhp_discharge': ('', 'int'),
    'peak_fhp_charge': ('', 'int'),
    'peak_load': ('', 'int'),

    'shoulder_grid_in': ('', 'int'),
    'shoulder_grid_out': ('', 'int'),
    'shoulder_pv_in': ('', 'int'),
    'shoulder_gen_in': ('', 'int'),
    'shoulder_fhp_discharge': ('', 'int'),
    'shoulder_fhp_charge': ('', 'int'),
    'shoulder_load': ('', 'int'),

    'offpeak_grid_in': ('', 'int'),
    'offpeak_grid_out': ('', 'int'),
    'offpeak_pv_in': ('', 'int'),
    'offpeak_gen_in': ('', 'int'),
    'offpeak_fhp_discharge': ('', 'int'),
    'offpeak_fhp_charge': ('', 'int'),
    'offpeak_load': ('', 'int'),

    'time': ('', 'lnt'),
    'batSoh': ('bat_soh', 'list'),
    'batCyc': ('bat_cycle_number', 'list'),
    'ch': ('total_charging', 'list'),
    'disch': ('total_discharging', 'list'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DailyEnergyUpload(object):

    def daily_energy_upload_from_aws_get(self, *args, **kwargs):
        """  daily energy data upload response from AWS,the message get-T412

        Args：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Daily Energy Upload From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def daily_energy_upload_from_aws_get_check(self, **kwargs):
        """  daily energy data upload response from AWS,the message get check-T412

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Daily Energy Upload From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.daily_energy_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def daily_energy_upload_from_device_get(self, *args, **kwargs):
        """   daily energy data upload from device,the message get-T411

        Args：
            | peak_grid_in | int | 峰时段市电输入电量，单位：wh |
            | peak_grid_out | int | 峰时段市电馈网电量，单位：wh |
            | peak_pv_in | int | 峰时段光伏发电量，单位：wh |
            | peak_gen_in | int | 峰时段发电机发电量，单位：wh |
            | peak_fhp_discharge | int | 峰时段FHP放电量，单位：wh |
            | peak_fhp_charge | int | 峰时段FHP发电量，单位：wh |
            | peak_load | int | 峰时段负载用电量，单位：wh |
            | shoulder_grid_in | int | 平时段市电输入电量，单位：wh |
            | shoulder_grid_out | int | 平时段市电馈网电量，单位：wh |
            | shoulder_pv_in | int | 平时段光伏发电量，单位：wh |
            | shoulder_gen_in | int | 平时段发电机发电量，单位：wh |
            | shoulder_fhp_discharge | int | 平时段FHP放电量，单位：wh |
            | shoulder_fhp_charge | int | 平时段FHP发电量，单位：wh |
            | shoulder_load | int | 平时段负载用电量，单位：wh |
            | offpeak_grid_in | int | 谷时段市电输入电量，单位：wh |
            | offpeak_grid_out | int | 谷时段市电馈网电量，单位：wh |
            | offpeak_pv_in | int | 谷时段光伏发电量，单位：wh |
            | offpeak_gen_in | int | 谷时段发电机发电量，单位：wh |
            | offpeak_fhp_discharge | int | 谷时段FHP放电量，单位：wh |
            | offpeak_fhp_charge | int | 谷时段FHP发电量，单位：wh |
            | offpeak_load | int | 谷时段负载用电量，单位：wh |
            | time | int | 电量统计日期 |
            | bat_soh | list | 电池SOH，单位：0.1% |
            | bat_cycle_number | list | 电池循环次数 |
            | total_charging | list | 总充电量，单位：wh |
            | total_discharging | list | 总放电量，单位：wh |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Daily Energy Upload From Device Get | bat_soh |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def daily_energy_upload_from_device_get_check(self, **kwargs):
        """  daily energy data upload from device,the message get check-T411

        Kwargs:
            | peak_grid_in | int | 峰时段市电输入电量，单位：wh |
            | peak_grid_out | int | 峰时段市电馈网电量，单位：wh |
            | peak_pv_in | int | 峰时段光伏发电量，单位：wh |
            | peak_gen_in | int | 峰时段发电机发电量，单位：wh |
            | peak_fhp_discharge | int | 峰时段FHP放电量，单位：wh |
            | peak_fhp_charge | int | 峰时段FHP发电量，单位：wh |
            | peak_load | int | 峰时段负载用电量，单位：wh |
            | shoulder_grid_in | int | 平时段市电输入电量，单位：wh |
            | shoulder_grid_out | int | 平时段市电馈网电量，单位：wh |
            | shoulder_pv_in | int | 平时段光伏发电量，单位：wh |
            | shoulder_gen_in | int | 平时段发电机发电量，单位：wh |
            | shoulder_fhp_discharge | int | 平时段FHP放电量，单位：wh |
            | shoulder_fhp_charge | int | 平时段FHP发电量，单位：wh |
            | shoulder_load | int | 平时段负载用电量，单位：wh |
            | offpeak_grid_in | int | 谷时段市电输入电量，单位：wh |
            | offpeak_grid_out | int | 谷时段市电馈网电量，单位：wh |
            | offpeak_pv_in | int | 谷时段光伏发电量，单位：wh |
            | offpeak_gen_in | int | 谷时段发电机发电量，单位：wh |
            | offpeak_fhp_discharge | int | 谷时段FHP放电量，单位：wh |
            | offpeak_fhp_charge | int | 谷时段FHP发电量，单位：wh |
            | offpeak_load | int | 谷时段负载用电量，单位：wh |
            | time | int | 电量统计日期 |
            | bat_soh | list | 电池SOC，单位：0.1% |
            | bat_cycle_number | list | 电池循环次数 |
            | total_charging | list | 总充电量，单位：wh |
            | total_discharging | list | 总放电量，单位：wh |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Daily Energy Upload From Device Get Check | bat_soh=92 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.daily_energy_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def daily_energy_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T411/412

        Args for T411:
            | peak_grid_in | int | 峰时段市电输入电量，单位：wh |
            | peak_grid_out | int | 峰时段市电馈网电量，单位：wh |
            | peak_pv_in | int | 峰时段光伏发电量，单位：wh |
            | peak_gen_in | int | 峰时段发电机发电量，单位：wh |
            | peak_fhp_discharge | int | 峰时段FHP放电量，单位：wh |
            | peak_fhp_charge | int | 峰时段FHP发电量，单位：wh |
            | peak_load | int | 峰时段负载用电量，单位：wh |
            | shoulder_grid_in | int | 平时段市电输入电量，单位：wh |
            | shoulder_grid_out | int | 平时段市电馈网电量，单位：wh |
            | shoulder_pv_in | int | 平时段光伏发电量，单位：wh |
            | shoulder_gen_in | int | 平时段发电机发电量，单位：wh |
            | shoulder_fhp_discharge | int | 平时段FHP放电量，单位：wh |
            | shoulder_fhp_charge | int | 平时段FHP发电量，单位：wh |
            | shoulder_load | int | 平时段负载用电量，单位：wh |
            | offpeak_grid_in | int | 谷时段市电输入电量，单位：wh |
            | offpeak_grid_out | int | 谷时段市电馈网电量，单位：wh |
            | offpeak_pv_in | int | 谷时段光伏发电量，单位：wh |
            | offpeak_gen_in | int | 谷时段发电机发电量，单位：wh |
            | offpeak_fhp_discharge | int | 谷时段FHP放电量，单位：wh |
            | offpeak_fhp_charge | int | 谷时段FHP发电量，单位：wh |
            | offpeak_load | int | 谷时段负载用电量，单位：wh |
            | time | int | 电量统计日期 |
            | bat_soh | list | 电池SOC，单位：0.1% |
            | bat_cycle_number | list | 电池循环次数 |
            | total_charging | list | 总充电量，单位：wh |
            | total_discharging | list | 总放电量，单位：wh |
        Args for T412：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 411/412 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=411 | rx_time_window=300 |
        | ${status} | Daily Energy Upload From Msg Get | total_charging | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.daily_energy_upload_from_device_get, cmd_type_s2c: self.daily_energy_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def daily_energy_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T411/412

        Kwargs for T411:
            | peak_grid_in | int | 峰时段市电输入电量，单位：wh |
            | peak_grid_out | int | 峰时段市电馈网电量，单位：wh |
            | peak_pv_in | int | 峰时段光伏发电量，单位：wh |
            | peak_gen_in | int | 峰时段发电机发电量，单位：wh |
            | peak_fhp_discharge | int | 峰时段FHP放电量，单位：wh |
            | peak_fhp_charge | int | 峰时段FHP发电量，单位：wh |
            | peak_load | int | 峰时段负载用电量，单位：wh |
            | shoulder_grid_in | int | 平时段市电输入电量，单位：wh |
            | shoulder_grid_out | int | 平时段市电馈网电量，单位：wh |
            | shoulder_pv_in | int | 平时段光伏发电量，单位：wh |
            | shoulder_gen_in | int | 平时段发电机发电量，单位：wh |
            | shoulder_fhp_discharge | int | 平时段FHP放电量，单位：wh |
            | shoulder_fhp_charge | int | 平时段FHP发电量，单位：wh |
            | shoulder_load | int | 平时段负载用电量，单位：wh |
            | offpeak_grid_in | int | 谷时段市电输入电量，单位：wh |
            | offpeak_grid_out | int | 谷时段市电馈网电量，单位：wh |
            | offpeak_pv_in | int | 谷时段光伏发电量，单位：wh |
            | offpeak_gen_in | int | 谷时段发电机发电量，单位：wh |
            | offpeak_fhp_discharge | int | 谷时段FHP放电量，单位：wh |
            | offpeak_fhp_charge | int | 谷时段FHP发电量，单位：wh |
            | offpeak_load | int | 谷时段负载用电量，单位：wh |
            | time | int | 电量统计日期 |
            | bat_soh | list | 电池SOC，单位：0.1% |
            | bat_cycle_number | list | 电池循环次数 |
            | total_charging | list | 总充电量，单位：wh |
            | total_discharging | list | 总放电量，单位：wh |
        Kwargs for T412：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 411/412 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=412 | rx_time_window=300 | filter_mode=and |
        | ${status} | Daily Energy Upload From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.daily_energy_upload_from_device_get_check, cmd_type_s2c: self.daily_energy_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
