from APP.base import Base
from robot.api import logger
from .locator import get_locator


class Tools(Base):

    def config_debug(self, value='enabled'):
        """ config system->tools->debug

        Kwargs:
            | value | enabled(默认)/disabled |
        Return:
            | None |
        Examples:
            | Config Debug |  value=enabled |
            | Config Debug |  value=disabled |
        """
        locator = get_locator('locator_debug')

        self._config_select_element_status(locator, element_type='button', checked_type='checked', expected_value=value)

    def go_to_charging_discharging_page(self):
        """ go to system->tools->charging/Discharging

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Charging Discharging Page |
        """
        value = "locator_charge_discharge_general"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def exit_charging_discharging_page(self):
        """ exit system->tools->charging/Discharging and go back to system->tools

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Charging Discharging Page |
        """
        self._system_cancel_locator()

    def config_charging(self, power=1, confirm=True):
        """ config system->tools->debug

        Kwargs:
            | power | float | 充电功率,注意不要超限5kw |
            | confirm | bool | True(默认),False为退出该页面 |
        Return:
            | None |
        Examples:
            | Config Charging |  power=0.5 |
        """
        locator = "locator_charge"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        locator = "locator_charge_discharge_edit_general"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, first_click=True, clear=True, click=True, send_keys=power)

        self.confirm_cancel_diag_handling(value=confirm)

    def config_discharging(self, power=1, confirm=True):
        """ config system->tools->debug

        Kwargs:
            | power | float | 放电功率,注意不要超限5kw |
            | confirm | bool | True(默认),False为退出该页面 |
        Return:
            | None |
        Examples:
            | Config Discharging |  power=0.5 |
        """
        locator = "locator_discharge"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        locator = "locator_charge_discharge_edit_general"

        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, first_click=True, clear=True, click=True, send_keys=power)

        self.confirm_cancel_diag_handling(value=confirm)

    def config_apower(self, value='enabled'):

        locator = ("xpath", "//android.widget.RelativeLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View[2]/android.widget.Switch[2]")

        self.find_element(*locator, click=True)

    def config_off_grid(self, value='enabled'):
        """ config system->tools->off grid

        Kwargs:
            | value | enabled(默认)/disabled |
        Return:
            | None |
        Examples:
            | Config Off Grid |  value=enabled |
            | Config Off Grid |  value=disabled |
        """
        logger.info('Begin execute the confirm')
        locator = get_locator('locator_off_grid')

        # self._config_select_element_status(locator, element_type='button', checked_type='checked', expected_value=value)

        self.find_element(*locator, click=True)
        locator = ("xpath", '''//android.widget.Button[@content-desc="Confirm"]''')

        self.find_element(*locator, click=True)
        logger.info('Have execute the confirm')
        # self._system_confirm_locator()

    def clear_fault(self, confirm=True):
        """ config system->tools->clear fault

        Kwargs:
            | confirm | True(默认)/False |
        Return:
            | None |
        Examples:
            | Clear Fault |  confirm=True |
            | Clear Fault |  confirm=False |
        """
        locator = get_locator('locator_clear_fault')

        self.find_element(*locator, click=True)

        self.confirm_cancel_diag_handling(value=confirm)

    def reboot(self, confirm=True):
        """ config system->tools->reboot

        Kwargs:
            | confirm | True(默认)/False |
        Return:
            | None |
        Examples:
            | Reboot |  confirm=True |
            | Reboot |  confirm=False |
        """
        locator = get_locator('locator_reboot')

        self.find_element(*locator, click=True)

        self.confirm_cancel_diag_handling(value=confirm)
