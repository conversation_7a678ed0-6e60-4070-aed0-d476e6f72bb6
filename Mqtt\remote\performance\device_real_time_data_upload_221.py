from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 设备数据周期性上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 221

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {
    'batFreq': ('apower_freq', 'list'),
    'batVo': ('bat_voltage', 'list'),
    'batCur': ('bat_current', 'list'),
    'batRePw': ('bat_reactive_power', 'list'),
    'batAcPw': ('bat_active_power', 'list'),
    'batPw': ('bat_output_power', 'int'),

    'batSoc': ('bat_soc', 'list'),
    'fhpSoc': ('fhp_soc', 'float'),

    'gridRePw': ('grid_reactive_power', 'int'),
    'gridAcPw': ('grid_active_power', 'int'),
    'gridVo1': ('grid_voltage_L1', 'int'),
    'gridVo2': ('grid_voltage_L2', 'int'),
    'gridCur1': ('grid_curret_L1', 'int'),
    'gridCur2': ('grid_curret_L2', 'int'),
    'gridFreq': ('grid_freq', 'int'),
    'gridPw': ('grid_power', 'int'),

    'soVo': ('pv_voltage', 'int'),
    'soCur': ('pv_current', 'int'),
    'soAcPw': ('bat_reactive_power', 'int'),
    'soRePw': ('bat_active_power', 'int'),
    'loadPw': ('load_power', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceRealTimeDataProactiveUpload(object):

    def real_time_device_periodic_data_from_device_get(self, *args, **kwargs):
        """  real time device data periodic upload from device,the message get-T221

        Args：
            | apower_freq | int list | aPower输出备电频率，单位：0.1Hz |
            | bat_voltage | int list | BMS电池电压，单位：0.1V |
            | bat_current | int list | BMS电池总电流，单位：0.1A |
            | bat_reactive_power | int list | aPower无功功率，单位：0.1kw |
            | bat_active_power | int list | aPower有功功率，单位：0.1kw |
            | bat_output_power | int list | aPower总输出功率，单位：0.1kw |
            | bat_soc  | int list | 电池SOC，单位：% |
            | bat_soh  | int | 电池SOH，单位：% |
            | grid_reactive_power | int  | 电网无功功率，单位：0.1kvar |
            | grid_active_power | int  | 电网有功功率，单位：0.1kw |
            | grid_voltage_L1 | int | 电网电压L1，单位：0.1V |
            | grid_voltage_L2 | int | 电网电压L2，单位：0.1V |
            | grid_curret_L1 | int | 电网电流L1，单位：0.1V |
            | grid_curret_L2 | int | 电网电流L2，单位：0.1V |
            | grid_freq | int | 市电频率，单位：0.1Hz |
            | grid_power | int  | 电网功率，单位：0.1kw,正为供电，负为馈电 |
            | pv_voltage | int  | 光伏电压，单位：0.1V |
            | pv_current | int  | 光伏电流，单位：0.1A |
            | bat_reactive_power | int  | 光伏无功功率，单位：0.1kw |
            | bat_active_power | int  | 光伏有功功率，单位：0.1kw |
            | load_power | int  | 负载功率，单位：0.1kw，耗电为正 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Real Time Device Periodic Data From Device Get | load_power |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_device_periodic_data_from_device_get_check(self, **kwargs):
        """  real time device data periodic upload from device,the message get check-T221

        Kwargs:
            | apower_freq | int list | aPower输出备电频率，单位：0.1Hz |
            | bat_voltage | int list | BMS电池电压，单位：0.1V |
            | bat_current | int list | BMS电池总电流，单位：0.1A |
            | bat_reactive_power | int list | aPower无功功率，单位：0.1kw |
            | bat_active_power | int list | aPower有功功率，单位：0.1kw |
            | bat_output_power | int list | aPower总输出功率，单位：0.1kw |
            | bat_soc  | int list | 电池SOC，单位：% |
            | bat_soh  | int | 电池SOH，单位：% |
            | grid_reactive_power | int  | 电网无功功率，单位：0.1kvar |
            | grid_active_power | int  | 电网有功功率，单位：0.1kw |
            | grid_voltage_L1 | int | 电网电压L1，单位：0.1V |
            | grid_voltage_L2 | int | 电网电压L2，单位：0.1V |
            | grid_curret_L1 | int | 电网电流L1，单位：0.1V |
            | grid_curret_L2 | int | 电网电流L2，单位：0.1V |
            | grid_freq | int | 市电频率，单位：0.1Hz |
            | grid_power | int  | 电网功率，单位：0.1kw,正为供电，负为馈电 |
            | pv_voltage | int  | 光伏电压，单位：0.1V |
            | pv_current | int  | 光伏电流，单位：0.1A |
            | bat_reactive_power | int  | 光伏无功功率，单位：0.1kw |
            | bat_active_power | int  | 光伏有功功率，单位：0.1kw |
            | load_power | int  | 负载功率，单位：0.1kw，耗电为正 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Real Time Device Periodic Data From Device Get Check | load_power=34 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_device_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_device_periodic_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T221

        Args for T221：
            | apower_freq | int list | aPower输出备电频率，单位：0.1Hz |
            | bat_voltage | int list | BMS电池电压，单位：0.1V |
            | bat_current | int list | BMS电池总电流，单位：0.1A |
            | bat_reactive_power | int list | aPower无功功率，单位：0.1kw |
            | bat_active_power | int list | aPower有功功率，单位：0.1kw |
            | bat_output_power | int list | aPower总输出功率，单位：0.1kw |
            | bat_soc  | int list | 电池SOC，单位：% |
            | bat_soh  | int | 电池SOH，单位：% |
            | grid_reactive_power | int  | 电网无功功率，单位：0.1kvar |
            | grid_active_power | int  | 电网有功功率，单位：0.1kw |
            | grid_voltage_L1 | int | 电网电压L1，单位：0.1V |
            | grid_voltage_L2 | int | 电网电压L2，单位：0.1V |
            | grid_curret_L1 | int | 电网电流L1，单位：0.1V |
            | grid_curret_L2 | int | 电网电流L2，单位：0.1V |
            | grid_freq | int | 市电频率，单位：0.1Hz |
            | grid_power | int  | 电网功率，单位：0.1kw,正为供电，负为馈电 |
            | pv_voltage | int  | 光伏电压，单位：0.1V |
            | pv_current | int  | 光伏电流，单位：0.1A |
            | bat_reactive_power | int  | 光伏无功功率，单位：0.1kw |
            | bat_active_power | int  | 光伏有功功率，单位：0.1kw |
            | load_power | int  | 负载功率，单位：0.1kw，耗电为正 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 221 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=221 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time Device Periodic Data From Msg Get | grid_power | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_device_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_device_periodic_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T221

        Kwargs for T221：
            | apower_freq | int list | aPower输出备电频率，单位：0.1Hz |
            | bat_voltage | int list | BMS电池电压，单位：0.1V |
            | bat_current | int list | BMS电池总电流，单位：0.1A |
            | bat_reactive_power | int list | aPower无功功率，单位：0.1kw |
            | bat_active_power | int list | aPower有功功率，单位：0.1kw |
            | bat_output_power | int list | aPower总输出功率，单位：0.1kw |
            | bat_soc  | int list | 电池SOC，单位：% |
            | bat_soh  | int | 电池SOH，单位：% |
            | grid_reactive_power | int  | 电网无功功率，单位：0.1kvar |
            | grid_active_power | int  | 电网有功功率，单位：0.1kw |
            | grid_voltage_L1 | int | 电网电压L1，单位：0.1V |
            | grid_voltage_L2 | int | 电网电压L2，单位：0.1V |
            | grid_curret_L1 | int | 电网电流L1，单位：0.1V |
            | grid_curret_L2 | int | 电网电流L2，单位：0.1V |
            | grid_freq | int | 市电频率，单位：0.1Hz |
            | grid_power | int  | 电网功率，单位：0.1kw,正为供电，负为馈电 |
            | pv_voltage | int  | 光伏电压，单位：0.1V |
            | pv_current | int  | 光伏电流，单位：0.1A |
            | bat_reactive_power | int  | 光伏无功功率，单位：0.1kw |
            | bat_active_power | int  | 光伏有功功率，单位：0.1kw |
            | load_power | int  | 负载功率，单位：0.1kw，耗电为正 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 221 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=221 | rx_time_window=320 | filter_mode=and |
        | ${status} | Real Time Device Periodic Data From Msg Get Check | bat_soc=60 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_device_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
