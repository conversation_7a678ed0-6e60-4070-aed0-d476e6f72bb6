from appium.webdriver.common.appiumby import AppiumBy, By
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from APP.base import Base
from time import sleep
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class V2L(Base):

    def get_v2l_parameter(self):
        """get v2l config parameters
        Kwargs:
            | None|
        Return:
            | None |
        Examples:
            | ${result} | Get V2l Parameters |
        """
        ret_dict = {}
        value = "locator_v2l_parameter_page"
        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        content_desc = ele.get_attribute('content-desc')
        lines = content_desc.split('\n')

        for i in range(0, len(lines), 2):
            key = lines[i]
            value = lines[i + 1] if i + 1 < len(lines) else ""
            ret_dict[key] = value
        logger.info(f'OK,the generator parameter:{ret_dict}')
        if not ret_dict:
            self.save_screenshot()
            raise ValueError('Fail to get parameter !')
        return ret_dict

    def config_v2l_page(self, **kwargs):

        logger.info(f"the V2L parameters:kwargs:{kwargs}")

        value = 'locator_v2l_enable'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        x_ration = 928 / 1072
        y_ration = 397 / 2276

        self.click_by_coordinate(x_ration * self.window_width, y_ration * self.window_height)

        try:
            value = 'locator_confirm'
            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        except Exception as e:
            logger.info(f"Sorry, could not find the element for '{value}'. Continuing with the next steps. Error: {str(e)}")

        startup_timeout = kwargs.pop('startup_timeout', 400)
        rated_power = kwargs.pop(' rated_power', 20.0)

        if startup_timeout is not None:
            value1 = 'locator_startup_timeout'
            value2 = 'locator_gen_para_edit'
            self.handle_locator_input(value1, value2, startup_timeout)

        if rated_power is not None:
            value1 = 'locator_vehicle_output_rated_power'
            value2 = 'locator_gen_para_edit'
            self.handle_locator_input(value1, value2, rated_power)

        self._system_confirm_locator()
        self._general_backward_upper_page()
        value = 'locator_v2l_start'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        value = 'locator_confirm'
        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def handle_locator_input(self, locator, edit_locator, value):
        self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)
        locator = get_locator(edit_locator)
        eles = self.find_element(*locator, multiple=True)

        eles[0].click()
        eles[0].clear()
        eles[0].send_keys(value)
        self._system_confirm_locator()
