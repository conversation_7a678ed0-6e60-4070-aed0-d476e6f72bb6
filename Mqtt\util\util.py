import inspect
import json
from robot.api import logger


def input_data_mapping(_dict, attr_map):
    """ used for the data type converter for user input data
    """

    logger.info(f'the user parameters: _dict:{_dict},attr_map:{attr_map}')

    attr_demap = {v[0]: k for k, v in attr_map.items()}

    ret_dict = {}

    for k, v in _dict.items():

        if k in attr_demap:

            raw_key = attr_demap[k]

            key_type = attr_map[raw_key][1]

            if key_type == 'int':

                ret_dict.update({raw_key: int(v)})

            elif key_type == 'float':

                ret_dict.update({raw_key: float(v)})

            elif key_type == 'list':

                ret_dict.update({raw_key: v})

            elif key_type == 'dict':

                ret_dict.update({raw_key: eval(v)})

            else:

                ret_dict.update({raw_key: v})
        else:

            raise ValueError(f'detected the attribute:{k} which is not in the attr_map:{attr_map}!')

    return ret_dict


def output_data_mapping(_dict, attr_map):

    logger.debug(f"the dict is:{_dict}")

    logger.debug(f"the attr_map is:{attr_map}")

    ret_dict = {}

    for k, v in _dict.items():

        if k in attr_map:

            user_key = attr_map[k][0]

            if attr_map[k][1] == 'int' or attr_map[k][1] == 'float':

                ret_dict.update({user_key: str(v)})

            else:

                ret_dict.update({user_key: v})

    return ret_dict


def ret_value_processing(args, resp, user_dict=None, ret_type='auto', ret_format='list'):

    logger.debug(f"the args:{args}")

    logger.debug(f"the resp:{json.dumps(resp, indent=4)}")

    logger.debug(f"the user_dict:{user_dict}")

    ret_list = list()

    ret_dict = dict()

    if args:

        for i in args:

            if ret_format == 'list':

                ret_list.append(user_dict[i])

            else:
                try:
                    ret_dict.update({i: user_dict[i]})

                except KeyError as e:

                    logger.info(f'Fail to find the key: {i} in {user_dict}!')

                    raise e

        if ret_format == 'list':

            if ret_type == 'auto':

                if len(ret_list) == 1:

                    return ret_list[0]

            return ret_list

        else:

            return ret_dict
    else:
        return resp


def input_kwargs_processing(kwargs, para_list):

    ctrl_dict = dict()

    for i in para_list:

        if i in kwargs:

            ctrl_dict.update({i: kwargs.pop(i)})

    return ctrl_dict


def dict_compare(resp_dict, kwargs, raise_error=True):

    logger.info(f"the dict_compare parameters: resp_dict:{resp_dict},kwargs:{kwargs},raise_error:{raise_error}")

    delta = dict()

    for k, v in resp_dict.items():

        for _k, _v in kwargs.items():

            if k == _k:

                if v != _v:

                    delta.update({k: v})
    if delta:

        error_msg = f"FAIL!Found the unmatched value(s):{delta} for the expected values:{kwargs} in response:"

        if is_bool(raise_error):

            raise ValueError(error_msg)

        else:
            logger.warn(error_msg)


def is_bool(value):

    if isinstance(value, str):

        return False if value.lower() in ['0', 'false', 'off', 'null'] else True

    return False if value in [{}, [], ()] else value


def attr_map_processing(*args):

    for i in args:

        for k, v in i.items():

            if v[0] == '':

                i.update({k: (k, v[1])})


def msg_parser(*args, msg=None, cmd_type=None, **kwargs):

    logger.info(f"the user kwargs in msg_parser is:args:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

    _cmd_type = int(cmd_type)

    func_pair = kwargs.pop('funcs', None)

    get_check_mode = kwargs.pop('get_check_mode', False)

    ret = None

    if msg is not None:

        if isinstance(msg, dict):

            _msg = [msg]

        elif isinstance(msg, list):

            _msg = msg

    match_result = False

    for i in _msg:

        if int(i['cmdType']) == _cmd_type:

            kwargs.update({'direct_packet': i})

            for k, v in func_pair.items():

                if _cmd_type == int(k):

                    if get_check_mode:

                        ret = v(**kwargs)

                    else:

                        ret = v(*args, **kwargs)

                    match_result = True

                    break

    if match_result:

        return ret

    else:

        raise ValueError(f'Fail to match {cmd_type} in {_msg}!')


def build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c):

    attr_map_processing(attr_map_c2s, attr_map_s2c)

    com_dict_c2s = dict(c2s_cmd_type=cmd_type_c2s, topic_c2s=topic_c2s, attr_map=attr_map_c2s)

    com_dict_s2c = dict(s2c_cmd_type=cmd_type_s2c, c2s_cmd_type=cmd_type_c2s, topic_s2c=topic_s2c, attr_map=attr_map_s2c)

    return com_dict_c2s, com_dict_s2c


def build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c):

    attr_map_processing(attr_map_c2s, attr_map_s2c)

    com_dict_c2s = dict(c2s_cmd_type=cmd_type_c2s, attr_map=attr_map_c2s)

    com_dict_s2c = dict(s2c_cmd_type=cmd_type_s2c, attr_map=attr_map_s2c)

    return com_dict_c2s, com_dict_s2c


def get_func_name(frame_index=1, level=3):

    return inspect.stack()[frame_index][level]


def demapping_attr_map(attr_map):

    ret_dict = {}

    for k, v in attr_map.items():

        new_key = v[0]

        ret_dict.update({new_key: (k, v[1])})

    logger.info(f'the final demapping attr map:{ret_dict}')

    return ret_dict


def output_tx_data_mapping(_dict, attr_map):

    logger.debug(f"the dict is:{_dict}")

    logger.debug(f"the attr_map is:{attr_map}")

    attr_map = demapping_attr_map(attr_map)

    ret_dict = {}

    for k, v in _dict.items():

        if k in attr_map:

            user_key = attr_map[k][0]

            if attr_map[k][1] == 'int':

                ret_dict.update({user_key: int(v)})

            elif attr_map[k][1] == 'float':

                ret_dict.update({user_key: float(v)})

            elif attr_map[k][1] == 'list':

                ret_dict.update({user_key: v})

            elif attr_map[k][1] == 'dict':

                ret_dict.update({user_key: eval(v)})
            else:
                ret_dict.update({user_key: v})

    logger.info(f'the output tx data dict:{ret_dict}')

    return ret_dict


def build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s):

    tx_dict = output_tx_data_mapping(kwargs, attr_map_s2c)

    kwargs.update(tx_dict=tx_dict, **com_dict_s2c)

    kwargs.update({'attr_map_rx': com_dict_c2s['attr_map']})
