from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 厂商实时数据上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 202

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'notice': ('', 'int'),
    'mfrsfile': ('vendor_file', 'string'),
    'sign': ('', 'string'),
    'mfrsDate': ('vendor_date', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class RealTimeVendorData(object):

    def real_time_vendor_data_from_device_get(self, *args, **kwargs):
        """  the real time vendor data from device,the message get-T202

        Args：
            | notice | int | 1：通知更新厂商实时数据  |
            | vendor_file |  string | <=128Bytes,厂商文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | vendor_date | string | yyyy-mm-dd,厂商文件生成日期 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Vendor Data From Device Get | vendor_file | sign | vendor_date |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_vendor_data_from_device_get_check(self, **kwargs):
        """  the real time vendor data from device,the message get check-T202

        Kwargs:
            | notice | int | 1：通知更新厂商实时数据  |
            | vendor_file |  string | <=128Bytes,厂商文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | vendor_date | string | yyyy-mm-dd,厂商文件生成日期 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Real Time Vendor Data From Device Get Check | notice=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_vendor_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_vendor_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T202

        Args for T202：
            | notice | int | 1：通知更新厂商实时数据  |
            | vendor_file |  string | <=128Bytes,厂商文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | vendor_date | string | yyyy-mm-dd,厂商文件生成日期 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 202 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=202 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Vendor Data From Msg Get | vendor_file | msg=${packets} |
        | ${status} | Real Time Vendor Data From Msg Get | vendor_file | vendor_date | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=202 | rx_time_window=300 |
        | ${status} | Real Time Vendor Data From Msg Get | notice | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_vendor_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_vendor_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T202

        Kwargs for T202：
            | notice | int | 1：通知更新厂商实时数据  |
            | vendor_file |  string | <=128Bytes,厂商文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | vendor_date | string | yyyy-mm-dd,厂商文件生成日期 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 202 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=202 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Vendor Data From Msg Get Check | notice=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_vendor_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
