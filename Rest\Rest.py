import requests
import json
from time import sleep
from robot.api import logger
from .apps import Apps
from .version import V<PERSON><PERSON><PERSON>
from copy import deepcopy
from http.client import HTTPConnection
from hashlib import md5
from .exception import RetryAuth, ServerUnstable
from pprint import pprint
from requests import request, sessions


class Rest(Apps):

    ROBOT_LIBRARY_SCOPE = 'SUITE'

    ROBOT_LIBRARY_VERSION = VERSION

    def __init__(self):

        pass

    def _request(self, method, url, **kwargs):

        logger.info(f'the user parameters: method:{method},url:{url},kwargs:{kwargs}')

        json_return = kwargs.pop('json_return', None)

        headers = kwargs.get('headers', None)

        related_path = kwargs.pop('related_path', False)

        ret = None

        if headers is None:

            kwargs.update(headers=self.headers)

        if related_path:

            url = f'{self.ip}/{url}'

        if self.session:

            logger.debug(f'the latest kwargs:{kwargs}')

            ret = self.session.request(method, url, **kwargs)

        try:

            self._validate_ret_code(ret)

        except RetryAuth as e:

            logger.info(f'OK,got the RetryAuth exception:{e}!')

            self.aws_server_login(username=self.user, password=self.password, lang=self.lang, aws_login_server=self.aws_login_server, aws_server=self.aws_server, debug=True)

            logger.info(f'Failed token is detected,already update it to the new token:{self.token}!')

            if self.session:

                logger.info(f'OK,send a duplicated requst again')

                kwargs['headers']['loginToken'] = self.token

                ret = self.session.request(method, url, **kwargs)

        except ServerUnstable as e:
                
            logger.info(f'OK,got the AWS server stable exception:{e}!')

            for i in range(3):

                ret = self.session.request(method, url, **kwargs)

                logger.info(f'the response:{ret.text}')

                try:

                    ret_text = ret.json()

                except Exception:

                    ret_text = ret.text
            
                if 'code' not in ret_text:

                    break
        
                sleep(3)

                logger.info(f'the retry number:{i+1}')

            if ret.status_code in [500, 501, 502, 503, 504, 505]:

                raise ServerUnstable(f"Fail to get correct response:{ret_text['code']} due to server stability!")
            
        if json_return:

            try:

                ret = ret.json()

            except Exception:

                ret = ret.content

        pprint(f'the latest response:{ret}')

        return ret


    def _validate_ret_code(self, ret):

        logger.info(f'The http status code:{ret.status_code},http body:{ret.content}, status flag:{ret.ok},status:{ret.raise_for_status},error reason:{ret.reason}')

        try:

            ret_text = ret.json()

        except Exception:

            ret_text = ret.text

        if not ret.ok:

            if ret.status_code in [500, 501, 502, 503, 504, 505]:

                raise ServerUnstable(f"Fail to get correct response:{ret.text} with {ret.text} due to server stabiligety!")

            raise ValueError(f'Fail to get the correct response:{ret} with {ret.text}!')

        elif 'code' in ret_text and 'message' in ret_text:

            if ret_text['code'] == 401 and '请输入有效的用户授权令牌' in ret_text['message']:

                logger.info(f'Sorry,the current token {self.token} is invalid now!')

                raise RetryAuth('Failed token is detected!')

            elif ret_text['code'] not in [200, 201, 202, 203, 204, 205, 206, 402]:
                raise ValueError(f"Fail to get error response:{ret_text['code']} with {ret.text}")

    def post(self, url=None, data=None, json=None, headers=None, verify=False, file=None, related_path=True, json_return=True, data_format='json', auto_lang=True):
        """
            data_format:json(by default)/form/upload_file/normal
        """
        logger.info(f'the user parameters: url:{url},data:{data},json:{json},headers:{headers},',
                    f'verify:{verify}, file:{file}, related_path:{related_path},json_return:{json_return},data_format:{data_format},auto_lang:{auto_lang}'
                    )

        files = None

        _headers = self.headers if headers is None else headers
        
        if data_format == 'form':

            _headers = deepcopy(self.headers)
            
            _headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'

            headers = _headers

        elif data_format == 'upload_file':

            _headers = deepcopy(self.headers)

            _headers['Content-Type'] = 'multipart/form-data'

            headers = _headers

            if file is not None:

                files = {'file': open(file, 'rb')}

        elif data_format == 'normal':

            query_string = ''

            if isinstance(data, dict):

                for k, v in data.items():

                    query_string += f'{k}={v}&'

                url += '?' + query_string[:-1] + f"&lang={self.default_dict['lang']}"

        if data is not None:

            if auto_lang:

                data.update(self.default_dict)

        return self._request('POST', url, data=data, files=files, json=json, headers=_headers, verify=verify, json_return=json_return, related_path=related_path)

    def get(self, url=None, url_abs=None, headers=None, verify=False, related_path=True, json_return=True,  data=None, auto_lang=True):

        logger.info(f'the user parameters: url:{url},url_abs:{url_abs},headers:{headers},',
                    f'verify:{verify},related_path:{related_path},json_return:{json_return},data:{data},auto_lang:{auto_lang}'
                    )
        if url_abs:

            url = f'{url_abs}/{url}'

        if data is None:

            if auto_lang:
                    
                url += f"?lang={self.default_dict['lang']}"

        else:

            if auto_lang:

                data.update(lang=self.lang)
                    
        return self._request('GET', url, params=data, headers=headers, verify=verify, json_return=json_return, related_path=related_path)

    def delete(self, url=None, headers=None, related_path=True):

        return self._request('DELETE', url, headers=headers, verify=verify, related_path=related_path)

    def head(self, url=None, related_path=True):

        return self._request('HEAD', url, headers=None, verify=verify, related_path=related_path)

    def patch(self, url=None, data=None, headers=None, related_path=True, json_return=True):

        return self._request('PATCH', url, data=data, headers=headers, verify=verify, json_return=json_return, related_path=related_path)

    def put(self, url=None, data=None, headers=None, related_path=True, json_return=True):

        return self._request('PUT', url, data=data, headers=headers, verify=verify, json_return=json_return, related_path=related_path)

    def aws_server_login(self, username="SuperAdmin", password='1234567890T', lang='ZH_CN', aws_login_server='https://test.franklinwh.com/api-user/user/login', aws_server='https://test.franklinwh.com', debug=True):

        logger.info(f"the user kwargs is:username:{username},password:{password},lang:{lang},aws_login_server:{aws_login_server},aws_server:{aws_server},debug:{debug}")

        self.ip = aws_server

        self.headers = {"Accept": "application/json, text/plain, */*",
                        "Accept-Encoding": "gzip, deflate, br, zstd",
                        "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                        "Connection": "keep-alive",
                        "Content-Type": "application/json",
                        "lang": lang,
                        "Origin": aws_server,
                        "Referer": aws_server,
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
                        }

        self.token = None

        self.user = username

        self.password = password

        self.lang = lang

        self.aws_login_server = aws_login_server

        self.aws_server = aws_server

        self.gateway = "hes-gateway"

        self.upgrade = "hes-upgrade"

        if self.aws_server in "https://device-test.franklinwh.com":

            self.aws_login_server = "https://device-test.franklinwh.com/api-user/user/login"

            self.gateway = "hes-gateway-device"

            self.upgrade = "hes-upgrade-device"

        logger.debug(f'the current aws login server:{self.aws_login_server}')

        self.default_dict = dict(lang=self.lang)

        logger.debug(f'the headers:{self.headers}')

        if debug:

            HTTPConnection.debuglevel = 1

        _passwd = md5(password.encode('utf-8')).hexdigest()

        login_dict = {"lang": self.lang,
                      "loginName": username,
                      "password": _passwd,
                      "type": 1, }

        self.session = requests.Session()

        ret = self.post(url=self.aws_login_server, json=login_dict, headers=self.headers, related_path=False, json_return=True)

        logger.debug(f'the received json data:{ret}')

        self.token = ret['data']['token']

        logger.debug(f'the received token:{self.token}')

        self.headers.update({"loginToken": self.token})

        logger.debug(f'the updated headers:{self.headers}')
