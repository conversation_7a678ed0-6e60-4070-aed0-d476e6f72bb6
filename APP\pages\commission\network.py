from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)
from .network_ethernet import Ethernet
from .network_wifi import Wifi
from .network_bt import Bt
from .network_4g import G4
from .network_detection import Detection


class Network(Ethernet, Wifi, G4, Detection, Bt):

    def go_to_commission_network_setting_page(self):
        """ Go to system->commission->network setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Network Setting Page |
        """
        value = "locator_network_setting"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def get_current_network_connection_method(self):

        pass

    def exit_commission_network_setting_page(self, operate='next'):
        """ exit system->commission->network setting page and go back to the system->commission page or next page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Network Setting Page | operate=next |
            | Exit Commission Network Setting Page | operate=exit |
        """
        _operate = operate.lower()

        if operate == 'exit':

            self._general_backward_upper_page_type2()

        elif operate == 'next':

            self._system_next_locator()

    def config_commission_network_ethernet(self):
        """ automatically configure system->commission->Network Settings->ethernet>configure page,then go back to Network Settings page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Config Commission Network Ethernet |
        """
        self._general_configure_page()

        self._system_next_locator()

        self._system_wait_to_show(locator="locator_general_complete", click=True)

    def config_commission_network_4g(self):
        """ automatically configure system->commission->network setting->4G>configure page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Config Commission Network 4g |
        """
        self._general_configure_page()

        self._system_next_locator()

        self._system_wait_to_show(locator="locator_general_complete", click=True)

    def config_commission_network_wifi(self):
        pass
