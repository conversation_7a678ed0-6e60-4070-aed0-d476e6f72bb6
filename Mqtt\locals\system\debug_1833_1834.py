from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 调试模式-系统版本

cmd_type_c2s = 1833

cmd_type_s2c = 1834

attr_map_c2s = {

    **attr_map_common_opt,

    'id': ('', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'fhp_sn': ('', 'string'),
    'ibg_sn': ('', 'string'),
    'ibg_ve': ('', 'string'),
    'ibg_iot': ('', 'string'),
    'ibg_local': ('', 'string'),
    'pe_sn': ('', 'string'),
    'pe_ver': ('', 'string'),
    'bms_sn': ('', 'string'),
    'bms_ver': ('', 'string'),
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDebugVersion(Base):

    def local_debug_device_version_get(self, *args, **kwargs):
        """  local debug device version get-T1833
        Args：
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：SN无对应ID |
            | id | int | 设备ID |
            | fhp_sn | string | FHP 对应的SN |
            | ibg_sn | string | IBG 对应的SN |
            | ibg_ve | string | IBG main版本 |
            | ibg_iot | string | IBG iot版本 |
            | ibg_local | string | IBG local版本 |
            | pe_sn | string | PE对应的SN |
            | pe_ver | string | PE版本 |
            | bms_sn | string | BMS对应的SN |
            | bms_ver | string | BMS版本 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | id | int | 设备ID,默认填0 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Debug Device Version Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_debug_device_version_get_result_check(self, _response, **kwargs):
        """  local debug device version get result check-T1834

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：SN无对应ID |
            | id | int | 设备ID |
            | fhp_sn | string | FHP 对应的SN |
            | ibg_sn | string | IBG 对应的SN |
            | ibg_ve | string | IBG main版本 |
            | ibg_iot | string | IBG iot版本 |
            | ibg_local | string | IBG local版本 |
            | pe_sn | string | PE对应的SN |
            | pe_ver | string | PE版本 |
            | bms_sn | string | BMS对应的SN |
            | bms_ver | string | BMS版本 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Debug Device Version Get |
        | ${status} = | Local Debug Device Version Get | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
