from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 系统离网设置

cmd_type_c2s = 1723

cmd_type_s2c = 1724

attr_map_c2s = {

    **attr_map_common_opt,

    'offgridSet': ('off_grid_set', 'int'),
    'offgridSoc': ('off_grid_soc', 'float'),

}

attr_map_s2c = {

    **attr_map_common_result,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSysOffGrid(Base):

    def local_off_grid_set(self, *args, **kwargs):
        """  local system off grid set-T1723
        Args：
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | off_grid_set | int | 离网状态，0：离网不使能状态；1：离网使能状态 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ ，7:风暴，8：BB套餐 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | off_grid_soc | float |  离网设置状态，0：离网不使能状态；1：离网使能状态 |
            | off_grid_set | int | 离网状态，0：离网不使能状态；1：离网使能状态 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Off Grid Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_system_set_result_check(self, _response, **kwargs):
        """  local system off grid set result check-T1724

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | off_grid_set | int | 离网状态，0：离网不使能状态；1：离网使能状态 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹ ，7:风暴，8：BB套餐 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Off Grid Set | off_grid_set=1 |
        | ${status} = | Local Off Grid Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
