from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 网络功能检测结果查询

cmd_type_c2s = 1133

cmd_type_s2c = 1134

attr_map_c2s = {

    **attr_map_common_opt,

    'networkChannel': ('network_channel', 'int'),

}

attr_map_s2c = {

    **attr_map_c2s,

    **attr_map_common,

    'testResult': ('test_result', 'int'),
    'internetSpeed': ('Internet_Speed', 'string'),
    'internetPacketLossRate': ('Internet_Packet_Loss_Rate', 'string'),
    'signalStrength': ('signal_strength', 'int'),
    'signalValue': ('signal_value', 'int'),
    'signalDetails': ('signal_details', 'string'),
    'LANPacketLossRate': ('LAN_Packet_Loss_Rate', 'string'),
    'connectStatus': ('connect_status', 'int'),

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalNetworkDetectionResultGet(Base):

    def local_network_channel_detection_result_get(self, *args, **kwargs):
        """  local network channel detection result get-T1133
        Args：
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Network Channel Detection Result Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_network_channel_detection_result_get_result_check(self, _response, **kwargs):
        """  local network channel detection result get check-T1134

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询 |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：查询失败；2：DHCP失败 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | test_result | int | 检测结果，0：预留；1：成功；2.失败；3：检测中；4.未设置 |
            | Internet_Speed | string | 带kB/s |
            | Internet_Packet_Loss_Rate | string | 互联网丢包率, [0-100]% |
            | signal_strength | int | 信号强度, 0-100，对wifi和4G有效 |
            | signal_value | int | 信号值, 0-100，对wifi(RSSI)和4G(RSRQ)有效 | |
            | signal_details | string | 4G信号详情, 0-100,对4G有效 |
            | LAN_Packet_Loss_Rate | string | 局域网丢包率, [0-100]% |
            | connect_status | int | 连接状态，0：预留；1：已连接；2：未连接 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${status} = | Local Network Channel Detection Result Get |
        | ${status} = | Local Network Channel Detection Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
