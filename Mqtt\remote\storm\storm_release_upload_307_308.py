from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 设备端解除风暴模式上报

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 508

cmd_type_c2s = 507

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    'relieve': ('storm_mode_exit', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class StormRelease(object):

    def storm_mode_exit_from_aws_get(self, *args, **kwargs):
        """  storm mode exit from AWS,the message get-T508

        Args：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Storm Mode Exit From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def storm_mode_exit_from_aws_get_check(self, **kwargs):
        """  upgrade info upload get from AWS,the message get check-T508

        Kwargs:
            | result | int | 结果，0:成功 1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Storm Mode Exit From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.storm_mode_exit_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def storm_mode_exit_from_device_get(self, *args, **kwargs):
        """   upgrade info upload get response from device,the message get-T507

        Args:
            | storm_mode_exit | int | 1：风暴模式退出解除 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Storm Mode Exit From Device Get | storm_mode_exit |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def storm_mode_exit_from_device_get_check(self, **kwargs):
        """  upgrade info upload get response from device,the message get check-T507

        Kwargs:
            | storm_mode_exit | int | 1：风暴模式退出解除 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Storm Mode Exit From Device Get Check | storm_mode_exit=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.storm_mode_exit_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def storm_mode_exit_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T507/508

        Args for T507:
            | storm_mode_exit | int | 1：风暴模式退出解除 |
        Args for T508：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 507/508 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=508 | rx_time_window=300 |
        | ${status} | Storm Mode Exit From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.storm_mode_exit_from_device_get, cmd_type_s2c: self.storm_mode_exit_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def storm_mode_exit_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T507/508

        Kwargs for T507:
            | storm_mode_exit | int | 1：风暴模式退出解除 |
        Kwargs for T508：
            | result | int | 结果，0:成功 1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 507/508 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=507 | rx_time_window=300 | filter_mode=and |
        | ${status} | Storm Mode Exit From Msg Get Check | storm_mode_exit=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.storm_mode_exit_from_device_get_check, cmd_type_s2c: self.storm_mode_exit_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
