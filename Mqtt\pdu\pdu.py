import json
import socket
import re
from time import time
from zlib import crc32
from dataclasses import dataclass, asdict
from robot.api import logger


MSG_LIST = []


@dataclass()
class Pdu(object):
    cmdType: int
    equipNo: str
    snno: int
    dataArea: dict
    len: int
    crc: str = ''
    type: int = 0
    timeStamp: int = 0

    def __post_init__(self):
        self.timeStamp = int(time())
        self.crc = self.crc_cal()
        self.len = self.len_get()

    def json_format(self):

        return json.dumps(asdict(self), indent=4, ensure_ascii=False)

    def json_format2(self):

        return asdict(self)

    def crc_cal(self):

        data = json.dumps(self.dataArea, separators=(',', ':')).encode('utf-8')

        return f'{crc32(data):X}'

    def len_get(self):

        dataArea = json.dumps(self.dataArea, separators=(',', ':'))

        return len(dataArea)


class Msg_parser(object):

    def __init__(self, msg, filter_data=None):

        _msg = json.loads(msg.payload.decode('utf-8'))

        logger.debug(f"Topic:{msg.topic}\n")

        logger.debug(f"Payload:\n")

        for k, v in _msg.items():

            pass

        print(f'the captured msg:{_msg}')

        filter_mode = filter_data.get('__filter_mode__')

        _cmdType = filter_data.get('cmdType')

        _equip = filter_data.get('equipNo')

        _kwargs = {}  # other inner parameters

        for k, v in filter_data.items():

            if k not in ['__filter_mode__', 'cmdType', 'equipNo']:

                _kwargs.update({k: v})

        print(f'the filter kwargs:{_kwargs}')

        if _cmdType:

            _filter_data = str(_cmdType).split("/")

            _filter_data = [int(j) for j in _filter_data]

            if len(_filter_data) == 1:                                                    # single expected cmdType scenario

                expected_data = _filter_data[0]

                if (_msg['cmdType'] == expected_data) and (_msg['equipNo'] == _equip):

                    found = True

                    if _kwargs:

                        print(f'Try to match in dataArea with {_kwargs}....')

                        for k, v in _kwargs.items():

                            if _msg['dataArea'][k] != v:

                                found = False

                                print(f'fail to match with {_kwargs}!')

                                break

                        print(f'OK,found the wonderful match with {_kwargs}!')

                    if found:

                        if filter_mode == 'all':            # get all messages including duplicated msgs...

                            MSG_LIST.append(_msg)

                        elif _msg not in MSG_LIST:          # strip the dupliated messages

                            MSG_LIST.append(_msg)

                        else:

                            print(f'Detect the duplicated MQTT messages...')

                        print(f'OK,matched the msg\n:{_msg}')

                    else:
                        print('sorry,not match...')

                else:

                    logger.debug(f"""Sorry,could not find the expected cmdType:\
                                     {expected_data}""")

            elif (_msg['cmdType'] in _filter_data) and (_msg['equipNo'] == _equip):       # multiple expected cmdType scenario

                if filter_mode == 'all':

                    MSG_LIST.append(_msg)

                elif _msg not in MSG_LIST:          # strip the dupliated messages

                    MSG_LIST.append(_msg)

                else:

                    logger.info(f'Detect the duplicated MQTT messages...')

                logger.info(f'OK,matched the msg\n:{_msg} for cmdType:{_cmdType}')

        elif filter_mode == 'all':   # capture all MQTTs

            MSG_LIST.append(_msg)

            logger.info(f'OK,got all the msgs\n:{_msg}')

        logger.info(f'OK,matched the MSG_LIST\n:{MSG_LIST}')


class Base(object):

    def __init__(self, sn=None, data=None):

        self.data = data
        self.sn = sn
        self.key = None

    def get_sn(self, sn):

        self.sn = sn

        if sn is None:

            _data = self.data

            if isinstance(_data, bytes):

                _data = _data.decode('utf-8', 'ignore')

            else:
                _data = str(_data)

            _sn = re.search('equipNo":"(.*?)"', _data)

            sn = _sn[1] if _sn else "00000000"

            print(f'got the sn:{sn}')

        return sn

    def get_key(self):

        return sum([ord(i) for i in self.sn]) % 256

    def get_crc(self):
        """crc is for dataArea
        """
        data = json.dumps(self.data, separators=(',', ':')).encode('utf-8')

        return f'{crc32(data):X}'


class LocalPdu(Base):

    def __init__(self, sn=None, data=None):

        super().__init__()

        self.data = data
        self.sn = self.get_sn(sn)
        self.key = self.get_key()

    def _get_pos_enc_data(self, data, match_str='equipNo.:.*?,'):

        match = re.search(match_str, data)

        begin = match.span()[-1] if match else None

        return begin

    def encrypt(self, data):

        begin = self._get_pos_enc_data(data)

        data = bytes(data, 'utf-8')

        result = bytearray(data)

        for seq in range(begin, len(data)):

            tmp = data[seq] + self.key + seq

            result[seq] = tmp % 256

        return result

    def decrypt(self):

        _data = self.data.decode('utf-8', 'ignore')

        begin = self._get_pos_enc_data(_data)

        result = bytearray(self.data)

        for seq in range(begin, len(self.data)):

            tmp = self.data[seq] - self.key - seq

            result[seq] = tmp % 256

        ret = None

        try:

            ret = json.loads(result.decode('utf-8', errors="ignore"))

        except json.JSONDecodeError:

            pass

        return ret

    def build_local_packet(self, cmdType, snno):

        dataArea = json.dumps(self.data, separators=(',', ':'))

        data_len = len(dataArea)

        crc = self.get_crc()

        full_pdu = {
            "cmdType": cmdType,
            "equipNo": self.sn,
            "type": 0,
            "timeStamp": int(time()),
            "snno": snno,
            "len": data_len,
            "crc": crc,
            "dataArea": self.data
        }

        """
        pkt = Pdu(cmdType=cmdType, equipNo=self.sn,len=data_len, dataArea=self.data, snno=snno)

        full_pdu=pkt.json_format2()

        """
        _data = json.dumps(full_pdu, separators=(',', ':'))

        logger.info(f'the Tx packet:{_data}')

        return self.encrypt(_data)


def pkt_sn_generate(start=1, end=10000):

    while start < end:

        yield start

        start += 1

        start = 1 if start == end else start
