from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 故障录波信息查询设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 377

cmd_type_c2s = 378


attr_map_s2c = {

    'cmd': ('', 'int'),
    'data': ('', 'dict'),

}

attr_map_c2s = {

    **attr_map_common_result,

    'cmd': ('', 'int'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class FaultWaveRecordGet(object):

    def fault_wave_record_get(self, *args, **kwargs):
        """  fault wave record get-T377/378

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
        Kwargs:
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据域 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('cmd', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def fault_wave_record_result_get_check(self, _response, **kwargs):
        """  fault wave record get result check-T378

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Fault Wave Record Get  |
        | ${status} = | Fault Wave Record Get Result Check | ${response} | result=0  | cmd=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def fault_wave_record_get_from_aws_get(self, *args, **kwargs):
        """  fault wave record get from AWS,the message get-T377

        Args：
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据域 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Get From AWS Get | cmd |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_get_from_aws_get_check(self, **kwargs):
        """  fault wave record get from AWS,the message get check-T377

        Kwargs:
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据域 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Fault Wave Record Get From AWS Get Check | cmd=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_get_from_device_get(self, *args, **kwargs):
        """   fault wave record get response from device,the message get-T378

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Fault Wave Record Get From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_get_from_device_get_check(self, **kwargs):
        """  fault wave record get response from device,the message get check-T378

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Fault Wave Record Get From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T377/378

        Args for T377:
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据域 |
        Args for T378：
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 377/378 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=377 | rx_time_window=300 |
        | ${status} | Fault Wave Record Get From Msg Get | cmd | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_get_from_device_get, cmd_type_s2c: self.fault_wave_record_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def fault_wave_record_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T377/378

        Kwargs for T377:
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
            | data | dict | 数据域 |
        Kwargs for T378：
            | result | int | 结果，0:成功，1:失败 |
            | cmd | int | 0:实时数据获取；1：参数设置；2文件获取 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 377/378 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=378 | rx_time_window=300 | filter_mode=and |
        | ${status} | Fault Wave Record Get From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_get_from_device_get_check, cmd_type_s2c: self.fault_wave_record_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
