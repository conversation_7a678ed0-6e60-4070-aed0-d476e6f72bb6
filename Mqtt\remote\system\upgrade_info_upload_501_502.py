from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 设备升级后信息主动上传

app_name = "device"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 502

cmd_type_c2s = 501

attr_map_s2c = {

    **attr_map_common_result,

}

attr_map_c2s = {

    'custom': ('site_name', 'string'),
    'connType': ('connection_type', 'int'),
    'ip': ('IP', 'string'),
    'mask': ('', 'string'),
    'gateway': ('', 'string'),
    'iccid': ('ICCID', 'string'),
    'imsi': ('IMSI', 'string'),
    'mac': ('MAC', 'string'),
    'usr_account': ('user_account', 'string'),
    'reg_date': ('registration_date', 'string'),
    'GPS': ('', 'list'),

    'IBG_SN': ('', 'string'),
    'IBG_VER': ('', 'string'),
    'MAIN_VER': ('', 'string'),    # newly added
    'AWS_VER': ('', 'string'),
    'SNVA_VER': ('', 'string'),    # newly added
    'APP_VER': ('', 'string'),

    'SL_VER': ('', 'string'),
    'SL_VER_APP': ('', 'list'),  # newly added
    'SL_VER_BOOT': ('', 'list'),  # newly added
    'SL_STATE_APP': ('', 'list'),  # newly added
    'SL_STATE_BOOT': ('', 'list'),  # newly added

    'FHP_NUM': ('', 'int'),
    'FHP_SN': ('', 'list'),
    'PE_SN': ('', 'list'),

    'FPGA_VER': ('', 'list'),
    'DCDC_VER': ('', 'list'),
    'DCDC_VER_APP': ('', 'list'),  # newly added
    'DCDC_VER_BOOT': ('', 'list'),  # newly added
    'DCDC_VER_APP_BK': ('', 'list'),  # newly added
    'DCDC_VER_BOOT_BK': ('', 'list'),  # newly added
    'DCDC_STATE_APP': ('', 'list'),  # newly added
    'DCDC_STATE_BOOT': ('', 'list'),  # newly added

    'INV_VER': ('', 'list'),
    'INV_VER_APP': ('', 'list'),  # newly added
    'INV_VER_BOOT': ('', 'list'),  # newly added
    'INV_VER_APP_BK': ('', 'list'),  # newly added
    'INV_VER_BOOT_BK': ('', 'list'),  # newly added
    'INV_STATE_APP': ('', 'list'),  # newly added
    'INV_STATE_BOOT': ('', 'list'),  # newly added

    'FPGA_VER': ('', 'list'),  # newly added
    'FPGA_VER_BK': ('', 'list'),  # newly added
    'FPGA_STATE': ('', 'list'),  # newly added

    'BMS_SN': ('', 'list'),
    'BMS_VER': ('', 'list'),
    'BMS_VER_BK': ('', 'list'),  # newly added
    'BMS_STATE': ('', 'list'),  # newly added

    'BL_VER': ('', 'list'),
    'BL_VER_BK': ('', 'list'),  # newly added
    'BL_STATE': ('', 'list'),  # newly added

    'TH_VER': ('', 'list'),
    'TH_VER_BK': ('', 'list'),  # newly added
    'TH_STATE': ('', 'list'),  # newly added

    'BATT_CELL_VER': ('', 'list'),    # newly added
    'METER_VER': ('', 'string'),
    'PE_HW_VER': ('', 'list'),    # newly added
    'BMS_MAIN_HW_VER': ('', 'list'),    # newly added

    'BMS_BL_HW_VER': ('', 'list'),    # newly added
    'BMS_TH_HW_VER': ('', 'list'),    # newly added
    'MB_VER': ('', 'string'),    # newly added

    'mpptAppVer': ('', 'list'),    # newly added
    'mpptBootVer': ('', 'list'),    # newly added
    'mpptAppBakVer': ('', 'list'),    # newly added
    'mpptBootBakVer': ('', 'list'),    # newly added
    'mpptAppBakSta': ('', 'list'),    # newly added
    'mpptBootBakSta': ('', 'list'),    # newly added

    'apbox20Num': ('', 'list'),    # newly added
    'apbox20Sn': ('', 'list'),    # newly added
    'apbox20HardwareVer': ('', 'list'),    # newly added
    'apbox20AppVer': ('', 'list'),    # newly added
    'apbox20BootVer': ('', 'list'),    # newly added
    'apbox20AppBakVer': ('', 'list'),    # newly added
    'apbox20BootBaktVer': ('', 'list'),    # newly added
    'apbox20AppBakSta': ('', 'list'),    # newly added
    'apbox20BootBakSta': ('', 'list'),    # newly added
    'msaModel': ('', 'list'),    # newly added

    'order': ('', 'string'),   # newly added
    'fileName': ('upgraded_file_name', 'string'),   # newly added
    'upgradeType': ('upgrade_type', 'int'),   # newly added
    'ratedPowerBuf': ('aPower_rated_power', 'list'),   # newly added

    'timezone': ('', 'float'),
    'timezoneStr': ('timezone_in_string', 'int'),
    'DST': ('dst', 'int'),
    'zoneinfo': ('zone_info', 'string'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class UpgradeInfoUpload(object):

    def upgrade_info_upload_get_from_aws_get(self, *args, **kwargs):
        """  upgrade info upload get from AWS,the message get-T502

        Args：
            | result | int | 结果，1:成功 2:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Info Upload Get From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_info_upload_get_from_aws_get_check(self, **kwargs):
        """  upgrade info upload get from AWS,the message get check-T502

        Kwargs:
            | result | int | 结果，1:成功 2:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Info Upload Get From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_info_upload_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_info_upload_get_from_device_get(self, *args, **kwargs):
        """   upgrade info upload get response from device,the message get-T501

        Args:
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string | <=32Bytes |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | SNVA_VER | string | <=32Bytes, for AWS Sunnova |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | BATT_CELL_VER | string list | BMS电芯硬件版本 |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | string | Modbus软件版本号 |
            | order | string | 升级工单号，<=32Bytes |
            | upgraded_file_name | string | 升级包名字 |
            | upgrade_type | int | 0:云平台触发升级；1：U盘升级；2：强制升级 |
            | aPower_rated_power | int list | Apower额定功率 |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | 时区，<=32Bytes,area |
            | DCDC_VER_APP | string list) |
            | DCDC_VER_BOOT | string list |
            | DCDC_VER_APP_BK | string list |
            | DCDC_VER_BOOT_BK | string list |
            | INV_VER_APP | string list |
            | INV_VER_BOOT | string list |
            | INV_VER_APP_BK | string list |
            | INV_VER_BOOT_BK | string list |
            | FPGA_VER | string list |
            | FPGA_VER_BK | string list |
            | SL_VER_APP | string list |
            | SL_VER_BOOT | string list |
            | BMS_VER_BK | string list |
            | BL_VER_BK | string list |
            | TH_VER_BK | string list |
            | DCDC_STATE_APP | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | DCDC_STATE_BOOT | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | INV_STATE_APP | string list |
            | INV_STATE_BOOT | string list |
            | FPGA_STATE | string list |
            | BMS_STATE | string list |
            | BL_STATE | string list |
            | TH_STATE | string list |
            | SL_STATE_APP | string list |
            | SL_STATE_BOOT | string list |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Upgrade Info Upload Get From Device Get | timezone |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def upgrade_info_upload_get_from_device_get_check(self, **kwargs):
        """  upgrade info upload get response from device,the message get check-T501

        Kwargs:
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string | <=32Bytes |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | SNVA_VER | string | <=32Bytes, for AWS Sunnova |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | BATT_CELL_VER | string list | BMS电芯硬件版本 |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | string | Modbus软件版本号 |
            | order | string | 升级工单号，<=32Bytes |
            | upgraded_file_name | string | 升级包名字 |
            | upgrade_type | int | 0:云平台触发升级；1：U盘升级；2：强制升级 |
            | aPower_rated_power | int list | Apower额定功率 |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | 时区，<=32Bytes,area |
            | DCDC_VER_APP | string list) |
            | DCDC_VER_BOOT | string list |
            | DCDC_VER_APP_BK | string list |
            | DCDC_VER_BOOT_BK | string list |
            | INV_VER_APP | string list |
            | INV_VER_BOOT | string list |
            | INV_VER_APP_BK | string list |
            | INV_VER_BOOT_BK | string list |
            | FPGA_VER | string list |
            | FPGA_VER_BK | string list |
            | SL_VER_APP | string list |
            | SL_VER_BOOT | string list |
            | BMS_VER_BK | string list |
            | BL_VER_BK | string list |
            | TH_VER_BK | string list |
            | DCDC_STATE_APP | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | DCDC_STATE_BOOT | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | INV_STATE_APP | string list |
            | INV_STATE_BOOT | string list |
            | FPGA_STATE | string list |
            | BMS_STATE | string list |
            | BL_STATE | string list |
            | TH_STATE | string list |
            | SL_STATE_APP | string list |
            | SL_STATE_BOOT | string list |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Upgrade Info Upload Get From Device Get Check | mask=255.255.255.0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.upgrade_info_upload_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def upgrade_info_upload_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T501/502

        Args for T501:
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string | <=32Bytes |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | SNVA_VER | string | <=32Bytes, for AWS Sunnova |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | BATT_CELL_VER | string list | BMS电芯硬件版本 |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | string | Modbus软件版本号 |
            | order | string | 升级工单号，<=32Bytes |
            | upgraded_file_name | string | 升级包名字 |
            | upgrade_type | int | 0:云平台触发升级；1：U盘升级；2：强制升级 |
            | aPower_rated_power | int list | Apower额定功率 |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | 时区，<=32Bytes,area |
            | DCDC_VER_APP | string list) |
            | DCDC_VER_BOOT | string list |
            | DCDC_VER_APP_BK | string list |
            | DCDC_VER_BOOT_BK | string list |
            | INV_VER_APP | string list |
            | INV_VER_BOOT | string list |
            | INV_VER_APP_BK | string list |
            | INV_VER_BOOT_BK | string list |
            | FPGA_VER | string list |
            | FPGA_VER_BK | string list |
            | SL_VER_APP | string list |
            | SL_VER_BOOT | string list |
            | BMS_VER_BK | string list |
            | BL_VER_BK | string list |
            | TH_VER_BK | string list |
            | DCDC_STATE_APP | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | DCDC_STATE_BOOT | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | INV_STATE_APP | string list |
            | INV_STATE_BOOT | string list |
            | FPGA_STATE | string list |
            | BMS_STATE | string list |
            | BL_STATE | string list |
            | TH_STATE | string list |
            | SL_STATE_APP | string list |
            | SL_STATE_BOOT | string list |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Args for T502：
            | result | int | 结果，1:成功 2:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 501/502 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=502 | rx_time_window=300 |
        | ${status} | Upgrade Info Upload Get From Msg Get | result | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_info_upload_get_from_device_get, cmd_type_s2c: self.upgrade_info_upload_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def upgrade_info_upload_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T501/502

        Kwargs for T501:
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string | <=32Bytes |
            | IBG_VER | string | <=32Bytes |
            | MAIN_VER | string |  <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | SNVA_VER | string | <=32Bytes, for AWS Sunnova |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS设备编号 |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | BATT_CELL_VER | string list | BMS电芯硬件版本 |
            | METER_VER | string | <=32Bytes | 电表版本 |
            | PE_HW_VER | int list | PE硬件版本号，0:1.0；1:1.1；2:1.2/1.3；3:1.3；20:2.0 |
            | BMS_MAIN_HW_VER | int list | BMS MAIN硬件版本号 |
            | BMS_BL_HW_VER | int list | BMS BL硬件版本号 |
            | BMS_TH_HW_VER | int list | BMS TH硬件版本号 |
            | MB_VER | string | Modbus软件版本号 |
            | order | string | 升级工单号，<=32Bytes |
            | upgraded_file_name | string | 升级包名字 |
            | upgrade_type | int | 0:云平台触发升级；1：U盘升级；2：强制升级 |
            | aPower_rated_power | int list | Apower额定功率 |
            | timezone |  float | -12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | 时区，<=32Bytes,area |
            | DCDC_VER_APP | string list) |
            | DCDC_VER_BOOT | string list |
            | DCDC_VER_APP_BK | string list |
            | DCDC_VER_BOOT_BK | string list |
            | INV_VER_APP | string list |
            | INV_VER_BOOT | string list |
            | INV_VER_APP_BK | string list |
            | INV_VER_BOOT_BK | string list |
            | FPGA_VER | string list |
            | FPGA_VER_BK | string list |
            | SL_VER_APP | string list |
            | SL_VER_BOOT | string list |
            | BMS_VER_BK | string list |
            | BL_VER_BK | string list |
            | TH_VER_BK | string list |
            | DCDC_STATE_APP | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | DCDC_STATE_BOOT | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | INV_STATE_APP | string list |
            | INV_STATE_BOOT | string list |
            | FPGA_STATE | string list |
            | BMS_STATE | string list |
            | BL_STATE | string list |
            | TH_STATE | string list |
            | SL_STATE_APP | string list |
            | SL_STATE_BOOT | string list |
            | mpptAppVer | string list |
            | mpptBootVer | string list |
            | mpptAppBakVer | string list |
            | mpptBootBakVer | string list |
            | mpptAppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | mpptBootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20Num  | int | aPbox2.0数量，1~4 |
            | apbox20Sn | string list |
            | apbox20HardwareVer | string list |
            | apbox20AppVer | string list |
            | apbox20BootVer | string list |
            | apbox20AppBakVer | string list |
            | apbox20BootBaktVer | string list |
            | apbox20AppBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | apbox20BootBakSta | string list | 备份固件状态信息，0x00：无固件 0x01：下载中 0x02：下载完成 0x03：校验完成 0x04：保存中 0x05：保存完成 0x06：升级中 0x07：升级完成 0x08: 读取头文件完成 0x09: 读取文件完成 0x0A: 块数据保存完成 0x10：原有固件 -> 已有固件 0xFE：校验错误 0xFF：升级失败 |
            | msaModel | int | MSA型号，0：未接入；1：CND；2：EQB;15: 未定义型号 |
        Kwargs for T502：
            | result | int | 结果，1:成功 2:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 501/502 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=501 | rx_time_window=300 | filter_mode=and |
        | ${status} | Upgrade Info Upload Get From Msg Get Check | IP=************* | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.upgrade_info_upload_get_from_device_get_check, cmd_type_s2c: self.upgrade_info_upload_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
