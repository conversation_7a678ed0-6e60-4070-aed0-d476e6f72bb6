from robot.api import logger


class Device(object):

    def get_dev_info(self, *args, aGate='ULG_TEST_215'):
        """
        args:
        id
        name
        installerAccount
        installerName
        activeTime
        localActiveTime
        onLineTime
        offLineTime
        deviceIp
        subnet
        mac
        gatewayIp
        signal
        iccId
        imei
        firmUpgradeTime
        timeZone
        version
        protocolVer
        deviceTime
        sysHdVersion
        realSysHdVersion
        wifiSignal
        connType
        site
        """

        logger.info(f'the user parameters:args:{args},aGate:{aGate}')

        uri = f'manage/selectIotGatewayList'

        data = {'countryId': '',
                'provinceId': '',
                'cityId': '',
                'alarmStatus': 0,
                'activeStatus': '',
                'search': {aGate},
                'searchFlag': 0,
                'status': '',
                'connType': '',
                'runStatus': '',
                'emsModel': '',
                'userId': '',
                'electricityType': '',
                'financierId': '',
                'relationStatus': '',
                'gridPhaseConSet': '',
                'pageSize': 10,
                'pageNum': 1,
                }

        ret = self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

        if args:

            ret_list = []

            for i in args:

                ret_list.append(ret['result'][0][i])

            if len(ret_list) == 1:

                return ret_list[0]

            return ret_list

        else:

            return ret

    def get_sys_version_info(self, *args, aGate='ULG_TEST_215', aPower=None):

        uri = f'manage/selectVersionDetail'

        data = {'gatewayId': {aGate}}

        ret = self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

        ret_list = []

        if args:

            for i in args:

                apower = ret['result']['sonNewDevices']

                if aPower is not None:

                    for j in apower:

                        if j['apowerSn'] == aPower:

                            ret_list.append(j)

                            break
                else:

                    ret_list.append(ret['result']['sonNewDevices'])

            if len(ret_list) == 1:

                return ret_list[0]

            return ret_list

        elif aPower is not None:

            apower = ret['result']['sonNewDevices']

            if aPower is not None:

                for j in apower:

                    if j['apowerSn'] == aPower:

                        ret_list.append(j)

                        break

                return ret_list
        else:

            return ret

    def do_fault_record_wave(self, aGate='ULG_TEST_215', aPower=None):

        uri = 'manage/manualWaveRecording'

        apower = aPower if aPower else ""

        json = {'apowerIds': apower,
                'gatewayId': aGate,
                'lang': self.lang,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.gateway}/{uri}', json=json, headers=self.headers)

    def get_global_parameters_setting(self, aGate='ULG_TEST_215'):

        uri = f'/manage/selectGlobalParameterSettings'

        data = {'id': {aGate},
                'paraType': 5,
                }

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def get_tou_list(self, aGate='ULG_TEST_215'):

        uri = f'manage/tou/getTouDispatchDetail'

        data = {'gatewayId': aGate,
                'showType': "1",
                }

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def get_tou_list_by_area(self, aGate='ULG_TEST_215'):

        uri = f'manage/getProvincesListBYGatewayId'

        json = {'gatewayId': aGate,
                'lang': self.lang,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.gateway}/{uri}', data=json, data_format='form', headers=self.headers)

    def get_pcs_setting(self, aGate='ULG_TEST_215'):

        uri = f'manage/tou/getPowerControlSetting'

        data = {'gatewayId': aGate}

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def get_energy_incentive(self, aGate='ULG_TEST_215', data='2025-02-10'):

        uri = f'manage/tou/getSgipInfo'

        data = {'gatewayId': aGate,
                'signingDate': data,
                }

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def clear_device_fault(self, aGate='ULG_TEST_215'):

        uri = f'manage/control'

        json = {'paraType': '2',
                'gatewayId': aGate,
                'opt': '1',
                'type': '1',
                'cleanLockAlarm': '1',
                'lang': self.lang,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.gateway}/{uri}', data=json, data_format='form', headers=self.headers)

    def get_real_time_alarm(self, aGate='ULG_TEST_215', alarm_name=False):
        """
        aGate: aGate SN
        alarm_name: bool,return alarm name list only if it is True
        """

        uri = f'manage/iotDeviceRunLogs'

        if self.user == 'SuperAdmin':

            userId = 1

        data = {'userId': userId,
                'searchName': aGate,
                'logType': 2,
                'pageSize': 50,
                'pageNum': 1,
                }

        alarm_list = self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

        if alarm_name:

            return [i['logName'] for i in alarm_list['result']]

        return alarm_list

    def set_grid_compliance(self, aGate='ULG_TEST_215', compliance_profile='user_defined', grid_mode='China', **kwargs):

        uri = 'manage/newCompliance/updateCompliance'

        specific_profile_dict = dict()
        
        if  compliance_profile.lower()=='user_defined':

            if grid_mode.lower()=='china':

                specific_profile_dict ={
		   "ES_DELAY_AS": "10",    #3
		   "ES_F_HIGH_AS": "50.3", #2
		   "ES_F_LOW_AS": "49.5",  #1

		   "OF1_TRIP_F_AS": "51.2",#4
		   "OF2_TRIP_F_AS": "52.0",#5

		   "UF1_TRIP_F_AS": "48.5", #6
		   "UF2_TRIP_F_AS": "46.5", #7

                    }
            elif grid_mode.lower()=='na':

                specific_profile_dict ={
		   "ES_DELAY_AS": "300",    #3
		   "ES_F_HIGH_AS": "60.1", #2
		   "ES_F_LOW_AS": "59.5",  #1

		   "OF1_TRIP_F_AS": "61.2",#4
		   "OF2_TRIP_F_AS": "62.0",#5

		   "UF1_TRIP_F_AS": "58.5", #6
		   "UF2_TRIP_F_AS": "56.5", #7

                    }
                
        
        content= { "CONST_PF_AS": "1.0",
		   "CONST_PF_EXCITATION": "INJ",
                   "CONST_PF_MODE_ENABLE_AS": 1,
		   "CONST_PF_OLRT_AS": "10.0",
		   "CONST_Q_AS": "44.0",
		   "CONST_Q_EXCITATION": "INJ",
		   "CONST_Q_MODE_ENABLE_AS": 1,
		   "CONST_Q_OLRT_AS": "10.0",

		   "ES_PERMIT_SERVICE_AS": 1,
		   "ES_RAMP_RATE_AS": "50",
		   "ES_V_HIGH_AS": "105.0",
		   "ES_V_LOW_AS": "91.7",
		   "gridFastEnable": 0,
		   "NORMAL_RAMP_AS": "1",
		   "OF1_TRIP_T_AS": "300.0",
		   "OF2_TRIP_T_AS": "0.16",
		   "OV1_TRIP_T_AS": "13.0",
		   "OV1_TRIP_V_AS": "110.0",
		   "OV2_TRIP_T_AS": "0.16",
		   "OV2_TRIP_V_AS": "120.0",
		   "PF_DBOF_AS": "0.036",
		   "PF_DBUF_AS": "0.036",
		   "PF_KOF_AS": "0.05",
		   "PF_KUF_AS": "0.05",
		   "PF_MODE_ENABLE_AS": 1,
		   "PF_OLRT_AS": "5.0",
		   "PV_CURVE_P1_AS": "100.0",
		   "PV_CURVE_P2_GEN_AS": "20.0",
		   "PV_CURVE_P2_LOAD_AS": "0.0",
		   "PV_CURVE_V1_AS": "106.0",
		   "PV_CURVE_V2_AS": "110.0",
		   "PV_MODE_ENABLE_AS": 1,
		   "PV_OLRT_AS": "10.0",
		   "QP_CURVE_P1_GEN_AS": "20.0",
		   "QP_CURVE_P1_LOAD_AS": "-20.0",
		   "QP_CURVE_P2_GEN_AS": "50.0",
		   "QP_CURVE_P2_LOAD_AS": "-50.0",
		   "QP_CURVE_P3_GEN_AS": "100.0",
		   "QP_CURVE_P3_LOAD_AS": "-100.0",
		   "QP_CURVE_Q1_GEN_AS": "0.0",
		   "QP_CURVE_Q1_LOAD_AS": "0.0",
		   "QP_CURVE_Q2_GEN_AS": "0.0",
		   "QP_CURVE_Q2_LOAD_AS": "0.0",
		   "QP_CURVE_Q3_GEN_AS": "-44.0",
		   "QP_CURVE_Q3_LOAD_AS": "44.0",
		   "QP_MODE_ENABLE_AS": 1,
		   "QP_OLRT_AS": "10.0",
		   "QV_CURVE_Q1_AS": "44.0",
		   "QV_CURVE_Q2_AS": "0.0",
		   "QV_CURVE_Q3_AS": "0.0",
		   "QV_CURVE_Q4_AS": "-44.0",
		   "QV_CURVE_V1_AS": "92.0",
		   "QV_CURVE_V2_AS": "98.0",
		   "QV_CURVE_V3_AS": "102.0",
		   "QV_CURVE_V4_AS": "108.0",
		   "QV_MODE_ENABLE_AS": 1,
		   "QV_OLRT_AS": "5.0",
		   "QV_VREF_AS": "100.0",
		   "QV_VREF_AUTO_MODE_AS": 1,
		   "QV_VREF_OLRT_AS": "2000",
		   "UF1_TRIP_T_AS": "300.0",
		   "UF2_TRIP_T_AS": "0.16",
		   "UI_MODE_ENABLE_AS": 1,
		   "UV1_TRIP_T_AS": "21.0",
		   "UV1_TRIP_V_AS": "88.0",
		   "UV2_TRIP_T_AS": "2.0",
		   "UV2_TRIP_V_AS": "50.0",
		   "UV3_TRIP_T_AS": "2.0",
		   "UV3_TRIP_V_AS": "50.0",

                   **specific_profile_dict,
	}

        json = {'content': {**content, **kwargs},
                'systemId': 2,
                'gatewayId': aGate,
                'lang': self.lang,
                'templateFromType': 2,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.gateway}/{uri}', json=json, headers=self.headers)

    def reboot_ems(self, aGate='ULG_TEST_215'):

        uri = f'manage/control'

        json = {'paraType': '1',
                'gatewayId': aGate,
                'opt': '1',
                'type': '0',
                'reboot': '1',
                'lang': self.lang,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.post(url=f'{self.gateway}/{uri}', data=json, data_format='form', headers=self.headers)

    def get_network_info(self, aGate='ULG_TEST_215'):

        uri = f'manage/getNetWorkSetting'

        data = {'gatewayId': aGate,
                }

        logger.info(f'the current headers:{self.headers}')

        return self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

    def get_apower_status(self, aGate='ULG_TEST_215'):

        uri = f'manage/selectGatewayPower'

        data = {'gatewayId': aGate,
                }

        logger.info(f'the current headers:{self.headers}')

        status = self.get(url=f'{self.gateway}/{uri}', data=data, headers=self.headers)

        return status['result']['iotAPowervos'][0]
