from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 网络通道检测设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 361

cmd_type_c2s = 362

attr_map_s2c = {

    **attr_map_common_opt,

    'networkChannel': ('network_channel', 'int'),

}

attr_map_c2s = {

    **attr_map_s2c,

    **attr_map_common,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class NetworkDetectionSet(object):

    def network_detection_channel_set(self, *args, **kwargs):
        """  network detection channel set-T361/362

        Args：
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Detection Channel Set |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def network_detection_channel_set_result_check(self, _response, **kwargs):
        """ network detection channel set result check-T362

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Network Detection Channel Set | opt=0 |
        | ${status} = | Network Detection Channel Get Result Check | ${response} | current_network=2  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def network_detection_channel_set_from_aws_get(self, *args, **kwargs):
        """  network detection channel set from AWS,the message get-T361

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Detection Channel Get From AWS Set | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def network_detection_channel_set_from_aws_get_check(self, **kwargs):
        """  network detection channel set from AWS,the message get check-T361

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Detection Channel Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_detection_channel_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_detection_channel_set_from_device_get(self, *args, **kwargs):
        """   network detection channel set response from device,the message get-T362

        Args：
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Detection Channel Set From Device Get | network_channel |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def network_detection_channel_set_from_device_get_check(self, **kwargs):
        """  network detection channel set response from device,the message get check-T362

        Kwargs:
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Network Detection Channel Set From Device Get Check | current_network=2 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_detection_channel_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_detection_channel_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T361/362

        Args for T361:
            | opt | int | 操作类型，0:查询，1:设置 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Args for T362：
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 361/362 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=361 | rx_time_window=300 |
        | ${status} | Network Detection Channel Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_detection_channel_set_from_device_get, cmd_type_s2c: self.network_detection_channel_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def network_detection_channel_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T361/362

        Kwargs for T361:
            | opt | int | 操作类型，0:查询，1:设置 |
            | network_channel | int | 路由检测通道，0：预留；1：ETH1；2：ETH2；3：wifi；4：4G |
        Kwargs for T362:
            | opt | int | 操作类型，0:查询,1：设置 |
            | network_channel | int | 路由检测通道，0：未检测；1：ETH1；2：ETH2；3：wifi；4：4G |
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因：0：操作成功；1：设置失败；2.当前网络未连接(网线未连接、wifi未连接完成、4G无信号)；3.正在检测中；4.通道未定义 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 361/362 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=362 | rx_time_window=300 | filter_mode=and |
        | ${status} | Network Detection Channel Set From Msg Get Check | network_channel=2 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_detection_channel_set_from_device_get_check, cmd_type_s2c: self.network_detection_channel_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
