from .pair import Pair
from .network import Network
from .location import Location
from .sysparameter import Sysparameter
from .utilityplan import <PERSON>tilityplan
from .grid import Grid
from .bind import Bind
from .locator import get_locator


class Commission(Pair, Network, Location, Sysparameter, Utilityplan, Grid, Bind):

    def go_to_commission_new_page(self):
        """ Go to commission main page from installer New main page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission New Page |
        """
        # skip for possible GPS warning window

        locator = ('id', "com.lbe.security.miui:id/permission_allow_foreground_only_button")

        self.find_element(*locator, click=True, throw_exception=False, timeout=0.5)

        value = "locator_commission_new"

        locator = get_locator(value)

        self.find_element(*locator, click=True)

    def show_commission_checklist_summary(self):
        """ Show commission checklist summary

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Show Commission Checklist Summary |
        """
        value = "locator_network_summary"

        locator = get_locator(value)

        self.find_element(*locator, click=True)

        for i in range(5):

            self.swipe_up()

    def save_commission_checklist_summary(self):
        """ Save commission checklist summary to album

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Save Commission Checklist Summary |
        """
        value = "locator_network_summary_save"

        locator = get_locator(value)

        self.find_element(*locator, click=True)

    def exit_commission_checklist_summary_page(self):
        """ Exit commission checklist summary page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Checklist Summary Page |
        """
        self._general_backward_upper_page_type2()
