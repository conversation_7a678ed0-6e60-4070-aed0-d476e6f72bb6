from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common

# 设备故障录波日志召唤

app_name = "debug"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 608

cmd_type_s2c = 607

common_dict = {

    'fileName': ('file_name', 'string'),
    'faultRecordID': ('fault_record_id', 'int'),

}

attr_map_s2c = {

    'url': ('', 'string'),
    'logTime': ('log_time', 'string'),

    **common_dict,

}

attr_map_c2s = {

    **attr_map_common,

    **common_dict,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class FaultWaveRecording(object):

    def fault_wave_record_set(self, *args, **kwargs):
        """  fault wave record set-T607/T608

        Args:
            | result | int | 结果，0：成功，1：失败 |
            | reason | int | 原因 |
            | fault_record_id | int | 日志ID |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | fault_record_id | int | 日志ID |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Fault Wave Record Set | fault_record_id=4 | url=http://abc.com | file_name=123 | log_time=2024-12-26 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def fault_wave_record_set_result_check(self, _response, **kwargs):
        """  fault wave record set result check-T608

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Fault Wave Record Set | fault_record_id=4 | url=http://abc.com | file_name=123 | log_time=2024-12-26 |
        | ${status} = | Fault Wave Record Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def fault_wave_record_set_from_aws_get(self, *args, **kwargs):
        """  fault wave record set from AWS,the message get-T607

        Args：
            | fault_record_id | int | 日志ID |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Fault Wave Record Set From AWS Get | fault_record_id |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_set_from_aws_get_check(self, **kwargs):
        """  fault wave record set from AWS,the message get check-T607

        Kwargs:
            | fault_record_id | int | 日志ID |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Fault Wave Record Set From AWS Get Check | log_time=2024-12-26 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_set_from_device_get(self, *args, **kwargs):
        """  fault wave record set from device,the message get-T608

        Args：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Fault Wave Record Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def fault_wave_record_set_from_device_get_check(self, **kwargs):
        """  fault wave record set from device,the message get check-T608

        Kwargs:
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Fault Wave Record Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.fault_wave_record_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def fault_wave_record_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T607/608

        Args for T607:
            | fault_record_id | int | 日志ID |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Args for T608：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 607/608 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=608 | rx_time_window=300 |
        | ${status} | Fault Wave Record Set From Msg Get | file_name | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_set_from_device_get, cmd_type_s2c: self.fault_wave_record_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def fault_wave_record_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T607/608

        Kwargs for T607:
            | fault_record_id | int | 日志ID |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for T608：
            | result | int | 结果，0：成功，1：失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 607/608 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=608 | rx_time_window=300 | filter_mode=and |
        | ${status} | Fault Wave Record Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.fault_wave_record_set_from_device_get_check, cmd_type_s2c: self.fault_wave_record_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
