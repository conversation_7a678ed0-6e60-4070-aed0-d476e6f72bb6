from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator


class Pair(Base):

    def go_to_commission_connecting_apowers_page(self):
        """ Go to system->commission->connecting aPowers page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Connecting Apowers Page |
        """
        value = "locator_connect_apower"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_connecting_apowers_page(self, operate='next'):
        """ Go to system->commission->connecting aPowers page and go back to the system->commission page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Connecting Apowers Page |
        """
        _operate = operate.lower()

        if operate == 'exit':

            value = "locator_general_up_2"

            try:
                self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            except Exception as e:

                logger.info(f'Got the exception:{e}')

                locator = "locator_connect_apower_exit2"

                self._get_locator_and_find_element(locator, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        elif operate == 'next':

            timeout = 120

            self._system_wait_to_show(locator="locator_general_next", timeout=timeout, click=True)

    def commission_connecting_apowers(self, apower_number=1):
        """ connect aPower to aGate-pairing

        Kwargs:
            | apower_number | int | 默认为1，the aPower number  |
        Return:
            | None |
        Examples:
            | Commission Connecting Apowers |  apower_number=8 |
        """
        self._system_select_locator()

        # drop_down list for aPower number to select

        value = "locator_general_seekbar"

        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self.set_seekbar(ele, expected_value=apower_number)

        self._system_confirm_locator()

        locator = ('acc_id', "Pairing")

        self.find_element(*locator, click=True, timeout=120)

        # error for paring handling
        """
        xpath
        //android.widget.ImageView[@content-desc="Cannot find any PE\nPlease retry pairing\nIf the above operation fails three times, contact customer service: 888-851-3188"]
        """
        locator = ('acc_id', "Next")

        self.find_element(*locator, timeout=30, click=True)

        # confirm the possible Google location service query dialog
        value = "locator_location_google_service_query"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, throw_exception=False, timeout=2, click=True)

        # switch to WIFI page to disconnect local FHP
        self._system_confirm_locator()

    def config_commission_connecting_apowers(self, apower_number=1):
        """ connect aPower to aGate-pairing

        Kwargs:
            | apower_number | int | 1~15, 默认为1，the aPower number  |
        Return:
            | None |
        Examples:
            | Config Commission Connecting Apowers |  apower_number=1 |
        """
        self._system_select_locator()

        # drop_down list for aPower number to select

        value = "locator_general_seekbar"

        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self.set_seekbar(ele, expected_value=apower_number)

        self._system_confirm_locator()

        locator = ('acc_id', "Pairing")

        self.find_element(*locator, click=True, timeout=120)

        # error for paring?
