from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 日功率曲线电量报表上传

app_name = "energy"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 407

cmd_type_s2c = 408

attr_map_s2c = {

    **attr_map_common_result,
}

attr_map_c2s = {

    'powerFile': ('power_file', 'string'),
    'sign': ('', 'string'),
    'date': ('', 'string'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DailyPowerUpload(object):

    def daily_power_upload_from_aws_get(self, *args, **kwargs):
        """  daily power data upload response from AWS,the message get-T408

        Args：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Daily Power Upload From AWS Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def daily_power_upload_from_aws_get_check(self, **kwargs):
        """  daily power data upload response from AWS,the message get check-T408

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Daily Power Upload From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.daily_power_upload_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def daily_power_upload_from_device_get(self, *args, **kwargs):
        """   daily power data upload from device,the message get-T407

        Args：
            | power_file |  string | <=128Bytes,每日功率日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | yyyy-mm-dd,日志文件文件生成日期 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Daily Power Upload From Device Get | power_file |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def daily_power_upload_from_device_get_check(self, **kwargs):
        """  daily power data upload from device,the message get check-T407

        Kwargs:
            | power_file |  string | <=128Bytes,每日功率日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | yyyy-mm-dd,日志文件文件生成日期 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Daily Power Upload From Device Get Check | date=2024-12-17 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.daily_power_upload_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def daily_power_upload_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T407/408

        Args for T407:
            | power_file |  string | <=128Bytes,每日功率日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | yyyy-mm-dd,日志文件文件生成日期 |
        Args for T408：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 405/406 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=407 | rx_time_window=300 |
        | ${status} | Daily Power Upload From Msg Get | power_file | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.daily_power_upload_from_device_get, cmd_type_s2c: self.daily_power_upload_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def daily_power_upload_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T407/408

        Kwargs for T407:
            | power_file |  string | <=128Bytes,每日功率日志文件上传路径 |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | yyyy-mm-dd,日志文件文件生成日期 |
        Kwargs for T408：
            | result | int | 结果，0:成功，1:失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 407/408 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=408 | rx_time_window=300 | filter_mode=and |
        | ${status} | Daily Power Upload From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.daily_power_upload_from_device_get_check, cmd_type_s2c: self.daily_power_upload_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
