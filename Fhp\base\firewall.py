from time import sleep

try:

    from pywinauto.application import Application

except Exception as e:

    pywinauto = None


class Firewall(object):

    def close_ems_firewall(self, IP='*************', file_path=None, port=9000, user_name='garrin', password='garrin2024'):

        app = Application(backend='uia').start(file_path)

        dlg = app.window(found_index=0, title_re='EmsDevFW', class_name='#32770')

        dlg.print_control_identifiers()

        dlg.set_focus()

        dlg.Edit1.set_edit_text(user_name)

        dlg.Edit2.set_edit_text(password)

        dlg.Edit3.set_edit_text(IP)

        dlg.Edit4.set_edit_text(9000)

        dlg.LoginButton.click()

        dlg.ConnectButton.click()

        dlg.Button4.click()

        sleep(3)

        app.kill()
