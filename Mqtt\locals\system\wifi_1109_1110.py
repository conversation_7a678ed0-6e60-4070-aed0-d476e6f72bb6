from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# WiFi热点扫描

cmd_type_c2s = 1109

cmd_type_s2c = 1110

attr_map_s2c = {

    **attr_map_common,

    'wifi_Info': ('reason', 'list'),

}

attr_map_c2s = {

    'wifi_ScanTime': ('scan_time', 'int'),

}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalWiFiScan(Base):

    def local_network_wifi_scan(self, *args, **kwargs):
        """  local wifi scan-T1109
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
        Kwargs:
            | scan_time | int | wifi 扫描等待时间 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Network Wifi Scan |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('scan_time', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_network_wifi_scan_result_check(self, _response, **kwargs):
        """  local wifi scan result-T1110

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Network Wifi Scan |
        | ${status} = | Local Network Wifi Scan Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
