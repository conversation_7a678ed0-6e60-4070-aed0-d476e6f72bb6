from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result, attr_map_common_opt

# 主动离网结果上传

app_name = "mng"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 350

cmd_type_s2c = 349

common_dict = {

    'offgridSet': ('off_grid_enabled', 'int'),
    **attr_map_common_opt,
}

attr_map_s2c = {

    'offgridSoc': ('off_grid_soc', 'float'),

    **common_dict,

}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

    'offgridState': ('off_grid_state', 'int'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class OffGrid(object):

    def off_grid_set(self, *args, **kwargs):
        """  off grid set-T349/T350

        Args:
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
        Kwargs:
            | opt | int | 操作，0：查询，1：设置 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_soc | float | 离网截止soc，默认5% |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Off Grid Set | opt=0 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def off_grid_set_result_check(self, _response, **kwargs):
        """  off grid set result check-T350

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Off Grid  Set |
        | ${status} = | Off Grid Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def off_grid_set_from_aws_get(self, *args, **kwargs):
        """  off grid set from AWS,the message get-T349

        Args：
            | opt | int | 操作，0：查询，1：设置 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_soc | float | 离网截止soc，默认5% |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Off Grid Set From AWS Get | off_grid_soc |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def off_grid_set_from_aws_get_check(self, **kwargs):
        """  off grid set from AWS,the message get check-T349

        Kwargs:
            | opt | int | 操作，0：查询，1：设置 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_soc | float | 离网截止soc，默认5% |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Off Grid  Set From AWS Get Check | off_grid_soc=10 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.off_grid_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def off_grid_set_from_device_get(self, *args, **kwargs):
        """  off grid set from device,the message get-T350

        Args：
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Off Grid Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def off_grid_set_from_device_get_check(self, **kwargs):
        """  off grid set from device,the message get check-T350

        Kwargs:
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Off Grid Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.off_grid_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def off_grid_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T349/350

        Args for T349:
            | opt | int | 操作，0：查询，1：设置 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_soc | float | 离网截止soc，默认5% |
        Args for T350：
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 349/350 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=349 | rx_time_window=300 |
        | ${status} | Off Grid Set From Msg Get | off_grid_enabled | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.off_grid_set_from_device_get, cmd_type_s2c: self.off_grid_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def off_grid_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T349/350

        Kwargs for T349:
            | opt | int | 操作，0：查询，1：设置 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_soc | float | 离网截止soc，默认5% |
        Kwargs for T350：
            | opt | int | 操作，0：查询，1：设置 |
            | result | int | 结果，0：成功； 1.：失败 |
            | off_grid_enabled | int | 离网设置状态，0：离网不使能，1：离网使能 |
            | off_grid_state | int | 离网状态，0:⽆，1:主动离⽹状态，2:响应指令退出，3:SOC低，4:负载⼤于FHP额定功率，5:PE退出主动离⽹，6:离⽹，7：风暴，8：BB套餐 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 349/350 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=350 | rx_time_window=300 | filter_mode=and |
        | ${status} | Off Grid Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.off_grid_set_from_device_get_check, cmd_type_s2c: self.off_grid_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
