from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 实时数据被动召唤上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 204

cmd_type_s2c = 203

attr_map_s2c = {

    **attr_map_common_opt,
    'refreshData': ('refresh_data', 'int'),
    'refreshDate': ('refresh_date', 'string'),
}

attr_map_c2s = {

    **attr_map_common_opt,
    'refreshData': ('refresh_data', 'int'),
    **attr_map_common,
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class RealTimeData(object):

    def real_time_data_upload(self, *args, **kwargs):
        """  the real time data upload-T203

        Args：
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 成功(停止周期发送也必须响应召唤上传)；1连接失败；2：上传失败；3：消息发送失败；4：数据类型错误；5：文件不存在 |
        Kwargs:
            | opt | int | 操作数据来源，1：APP，2(by default)：Web  |
            | refresh_data |  string | 实时数据刷新，1(by default)：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据,7：设备功率数据   |
            | refresh_date | string | yyyy-mm-dd,不适用于用户实时数据和日电量实时统计数据 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Data Upload | opt=2 | refresh_data=1 |
        | ${status} = |  Real Time Data Upload | opt=2 | refresh_data=2 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', int(kwargs.pop('opt', 2)))

        kwargs.setdefault('refresh_data', int(kwargs.pop('refresh_data', 1)))

        rx_filter_dict = {'rx_filter_dict': {'opt': kwargs.get('opt'), 'refreshData': kwargs.get('refresh_data')}}

        kwargs.update(rx_filter_dict)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def real_time_data_upload_result_check(self, _response, **kwargs):
        """  the real time data upload result check-T204

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据(以237报文回应)  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 成功(停止周期发送也必须响应召唤上传)；1连接失败；2：上传失败；3：消息发送失败；4：数据类型错误；5：文件不存在 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Real Time Data Upload | opt=2 | refresh_data=2 |
        | ${status} = |  Real Time Data Upload Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def real_time_data_from_aws_get(self, *args, **kwargs):
        """  the real time data from AWS,the message get-T203

        Args：
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据,7：设备功率数据   |
            | refresh_date | string | yyyy-mm-dd,不适用于用户实时数据和日电量实时统计数据 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Data From AWS Get | opt | refresh_data |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_data_from_aws_get_check(self, **kwargs):
        """  the real time data from AWS,the message get check-T203

        Kwargs:
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据,7：设备功率数据   |
            | refresh_date | string | yyyy-mm-dd,不适用于用户实时数据和日电量实时统计数据 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Real Time Data From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_data_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_data_from_device_get(self, *args, **kwargs):
        """  the real time data from device,the message get-T204

        Args：
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据(以237报文回应)  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 0：成功，1：连接失败，2：上传失败，3：消息发送失败，4：数据类型错误，5：文件不存在 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Data From Device Get | reason |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_data_from_device_get_check(self, **kwargs):
        """  the real time data from device,the message get check-T204

        Kwargs:
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据(以237报文回应)  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 0：成功，1：连接失败，2：上传失败，3：消息发送失败，4：数据类型错误，5：文件不存在 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Real Time  Data From Device Get Check | reason=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T203/204

        Args for T203:
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据,7：设备功率数据   |
            | refresh_date | string | yyyy-mm-dd,不适用于用户实时数据和日电量实时统计数据 |
        Args for T204：
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 0：成功，1：连接失败，2：上传失败，3：消息发送失败，4：数据类型错误，5：文件不存在 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 203/204 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=203 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Data From Msg Get | opt | msg=${packets} |
        | ${status} | Real Time Data From Msg Get | opt | refresh_data | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=204 | rx_time_window=300 |
        | ${status} | Real Time Data From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_data_from_device_get, cmd_type_s2c: self.real_time_data_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T203/204

        Kwargs for T203:
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据,7：设备功率数据   |
            | refresh_date | string | yyyy-mm-dd,不适用于用户实时数据和日电量实时统计数据 |
        Kwargs for T204：
            | opt | int | 操作数据来源，1：APP，2：Web  |
            | refresh_data |  string | 实时数据刷新，1：用户实时数据，2.厂商实时数据，3：告警日志文件，4.告警监控快照文件，5.日电量实时统计数据，7：设备功率数据  |
            | result | string | 0：成功，1：失败 |
            | reason | string | 0：成功，1：连接失败，2：上传失败，3：消息发送失败，4：数据类型错误，5：文件不存在 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 203/204 |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=203 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Data From Msg Get Check| opt=1 | refresh_data=1 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_data_from_device_get_check, cmd_type_s2c: self.real_time_data_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
