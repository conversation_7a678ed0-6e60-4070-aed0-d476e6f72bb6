from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 智能负载开关

cmd_type_c2s = 1409

cmd_type_s2c = 1410

attr_map_c2s = {

    **attr_map_common_opt,

    'SwMerge': ('payload_switch_merge_enabled', 'int'),

    'Sw1Name': ('payload_switch1_name', 'string'),
    'Sw1MsgType': ('payload_switch1_message_type', 'string'),
    'Sw1SocLowSet': ('payload_switch1_lowest_threshold', 'string'),
    'Sw1Mode': ('payload_switch1_mode', 'string'),
    'Sw1ProLoad': ('payload_switch1_programmed_status', 'string'),
    'Sw1AtuoEn': ('payload_switch1_auto_enabled', 'string'),
    'Sw1Freq': ('payload_switch1_freq', 'string'),
    'Sw1TimeEn': ('payload_switch1_timer_enabled', 'list'),
    'Sw1Time': ('payload_switch1_time', 'list'),
    'Sw1TimeSet': ('payload_switch1_time_set', 'list'),

    'Sw2Name': ('payload_switch2_name', 'string'),
    'Sw2MsgType': ('payload_switch2_message_type', 'string'),
    'Sw2SocLowSet': ('payload_switch2_lowest_threshold', 'string'),
    'Sw2Mode': ('payload_switch2_mode', 'string'),
    'Sw2ProLoad': ('payload_switch2_programmed_status', 'string'),
    'Sw2AtuoEn': ('payload_switch2_auto_enabled', 'string'),
    'Sw2Freq': ('payload_switch2_freq', 'string'),
    'Sw2TimeEn': ('payload_switch2_timer_enabled', 'list'),
    'Sw2Time': ('payload_switch2_time', 'list'),
    'Sw2TimeSet': ('payload_switch2_time_set', 'list'),

    'Sw3Name': ('payload_switch3_name', 'string'),
    'Sw3MsgType': ('payload_switch3_message_type', 'string'),
    'Sw3SocLowSet': ('payload_switch3_lowest_threshold', 'string'),
    'Sw3Mode': ('payload_switch3_mode', 'string'),
    'Sw3ProLoad': ('payload_switch3_programmed_status', 'string'),
    'Sw3AtuoEn': ('payload_switch3_auto_enabled', 'string'),
    'Sw3Freq': ('payload_switch3_freq', 'string'),
    'Sw3TimeEn': ('payload_switch3_timer_enabled', 'list'),
    'Sw3Time': ('payload_switch3_time', 'list'),
    'Sw3TimeSet': ('payload_switch3_time_set', 'list'),

    'gridVoltCheck': ('generator_control_type', 'int'),
    'genStat': ('generator_status', 'int'),
    'CarSwConsSupEnable': ('car_constant_charging_enabled', 'int'),
    'CarSwConsSupEnerge': ('car_constant_charging_energy', 'int'),
    'CarSwConsSupStartTime': ('car_constant_charging_start_time', 'list'),
}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,
}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSmartLoadSet(Base):

    def local_smart_load_set(self, *args, **kwargs):
        """  local smart load set-T1409
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0：操作成功；1：查询失败；2：设置失败 |
            | payload_switch_merge_enabled | int | 负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 负载开关1名称 |
            | payload_switch1_message_type | string | 负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | string | 负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | string | 负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | string | 负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | string | 负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | string | 负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 负载开关2名称 |
            | payload_switch2_message_type | string | 负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | string | 负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | string | 负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | string | 负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | string | 负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | string | 负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 负载开关3名称 |
            | payload_switch3_message_type | string | 负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | string | 负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | string | 负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | string | 负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | string | 负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | string | 负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
        Kwargs:
            | opt | int | 操作，0:查询，1:设置 |
            | payload_switch_merge_enabled | int | 负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 负载开关1名称 |
            | payload_switch1_message_type | string | 负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | string | 负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | string | 负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | string | 负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | string | 负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | string | 负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 负载开关2名称 |
            | payload_switch2_message_type | string | 负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | string | 负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | string | 负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | string | 负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | string | 负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | string | 负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 负载开关3名称 |
            | payload_switch3_message_type | string | 负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | string | 负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | string | 负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | string | 负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | string | 负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | string | 负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Smart Load Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_smart_load_set_result_check(self, _response, **kwargs):
        """  local smart load set result check-T1410

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作，0:查询，1:设置 |
            | result | int | 结果，0：操作成功；1：查询失败；2：设置失败 |
            | payload_switch_merge_enabled | int | 负载开关合并使能，0：分离，1：合并 |
            | payload_switch1_name | string | 负载开关1名称 |
            | payload_switch1_message_type | string | 负载开关1消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch1_lowest_threshold | string | 负载开关1SOC低限设置,0~100 |
            | payload_switch1_mode | string | 负载开关1模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch1_programmed_status | string | 负载开关1编程状态,0：断开，1：闭合 |
            | payload_switch1_auto_enabled | string | 负载开关1备电时开关,0：不需要；1：需要 |
            | payload_switch1_freq | string | 负载开关1频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch1_timer_enabled | list | 负载开关1定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch1_time | list | 负载开关1定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch1_time_set | list | 负载开关1动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch2_name | string | 负载开关2名称 |
            | payload_switch2_message_type | string | 负载开关2消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch2_lowest_threshold | string | 负载开关2SOC低限设置,0~100 |
            | payload_switch2_mode | string | 负载开关2模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch2_programmed_status | string | 负载开关2编程状态,0：断开，1：闭合 |
            | payload_switch2_auto_enabled | string | 负载开关2备电时开关,0：不需要；1：需要 |
            | payload_switch2_freq | string | 负载开关2频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch2_timer_enabled | list | 负载开关2定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch2_time | list | 负载开关2定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch2_time_set | list | 负载开关2动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | payload_switch3_name | string | 负载开关3名称 |
            | payload_switch3_message_type | string | 负载开关3消息下发类型，0：未设置，1：手动开关，2：参数设置 |
            | payload_switch3_lowest_threshold | string | 负载开关3SOC低限设置,0~100 |
            | payload_switch3_mode | string | 负载开关3模式,0：手动关；1：手动开；2：定时计划 |
            | payload_switch3_programmed_status | string | 负载开关3编程状态,0：断开，1：闭合 |
            | payload_switch3_auto_enabled | string | 负载开关3备电时开关,0：不需要；1：需要 |
            | payload_switch3_freq | string | 负载开关3频率,0：1次性；1：1天；2：2天；... ；60：60天 |
            | payload_switch3_timer_enabled | list | 负载开关3定时计划使能,0：定时计划失能；1：定时计划使能 |
            | payload_switch3_time | list | 负载开关3定时时间点,最多4个点，数组元素不超过21字节 |
            | payload_switch3_time_set | list | 负载开关3动作,0：定时关；1：定时开；2：时间点未设置，无效控制 |
            | generator_control_type | int | 发电机启动控制类型，0：未选择；1：电压型；2：ATS型；3：干接点型 |
            | generator_status | int | 发电机状态，0：不使能；1：停机；2：启动；3：运行；4：退出；5：故障 |
            | car_constant_charging_enabled | int | 车充定量供电使能，0：不使能；1：使能 |
            | car_constant_charging_energy | int | 车充定量供电电量，单位：wh |
            | car_constant_charging_start_time | list | 车充定量供电开始时间 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Smart Load Set |
        | ${status} = | Local Smart Load Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
