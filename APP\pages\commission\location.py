from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Location(Base):

    def go_to_commission_location_page(self):
        """ Go to system->commission->location page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Location Page |
        """
        value = "locator_location"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

        # confirm the possible Google location service query dialog
        value = "locator_location_google_service_query"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, timeout=2, throw_exception=False)

        # confirm any possible device alarm like firmware update
        value = "locator_location_alarms"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, timeout=1, throw_exception=False)

    def commission_location(self, **kwargs):
        """ configure commission->location parameters

        Kwargs:
            | address | string |
            | address2 | string |
            | country | string | 出现限定的国家值 |
            | state | string | 对美澳等国家出现限定的省份/州名 |
            | city | string |
            | zip_code | string |
            | auto_mode | bool | 缺省为True,只输入address时，如果出现唯一匹配，则APP自动填充country/state/city/zip_code,不需要用户输入 |
        Return:
            | None |
        Examples:
            | Config Commission Location | address2=GKW | zip_code=61111 | city=SHENZHEN2 | country=United States | state=Colorado | address=santana Row plaza |
            | Config Commission Location | address2=GKW | zip_code=61111 | city=SHENZHEN2 |country=Australia | state=Victoria | address=ACD |
            | Config Commission Location | address=RongCHEN | address2=NANSHAN District  | city=SHENZHEN | country=China | state=Guangdong  | zip_code=518004 |
        """

        self.config_commission_location(**kwargs)
        self._system_next_locator()

    def config_commission_location(self, **kwargs):
        """ configure commission->location parameters

        Kwargs:
            | address | string |
            | address2 | string |
            | country | string | 出现限定的国家值 |
            | state | string | 对美澳等国家出现限定的省份/州名 |
            | city | string |
            | zip_code | string |
            | auto_mode | bool | 缺省为True,只输入address时，如果出现唯一匹配，则APP自动填充country/state/city/zip_code,不需要用户输入 |
        Return:
            | None |
        Examples:
            | Config Commission Location | address2=GKW | zip_code=61111 | city=SHENZHEN2 | country=United States | state=Colorado | address=santana Row plaza |
            | Config Commission Location | address2=GKW | zip_code=61111 | city=SHENZHEN2 | country=Australia | state=Victoria | address=ACD |
            | Config Commission Location | address=RongCHEN | address2=NANSHAN District  | city=SHENZHEN | country=China | state=Guangdong  | zip_code=518004 |
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        auto_mode = kwargs.pop('auto_mode', True)

        # confirm the possible Google location service query dialog
        value = "locator_location_google_service_query"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, throw_exception=False, timeout=1)

        # confirm any possible device alarm like firmware update
        value = "locator_location_alarms"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, throw_exception=False, timeout=1)

        smart_input = False

        # try to check Address firstly,it's auto fill

        _address = kwargs.pop('address', None)

        # try to get Address/Country/State,etc.

        eles_1_dict = self._get_location_general_1()

        logger.info(f'the ele dict:{eles_1_dict}')

        # must deal with country firstly

        _country = kwargs.pop('country', None)

        ele = eles_1_dict['country']

        ele.click()

        value = "locator_location_country_state_seek_bar"

        ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self.set_seekbar(ele, expected_value=_country)

        self._system_confirm_locator()

        if _address is not None:

            ele = eles_1_dict['address']

            ele.click()

            value = "locator_location_address_after_clear"

            locator = get_locator(value)

            eles = self.find_element(*locator, multiple=True)

            eles[0].clear()

            eles[0].send_keys(_address)

            value = "locator_location_address"

            locator = get_locator(value)

            eles = self.find_element(*locator, multiple=True)

            if len(eles) == 2:

                logger.info("OK, detect two matched results.")

                if auto_mode:

                    smart_input = True

                    eles[0].click()

            else:
                logger.info("OK, select the manual address to fill in.")

                eles[-1].click()

        # deal with other parameters

        if not smart_input:

            # handle with state

            for k, v in kwargs.items():

                if k in ['state']:

                    _state = kwargs['state']

                    ele = eles_1_dict[k]

                    ele.click()

                    value = "locator_location_country_state_seek_bar"

                    ele = self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

                    self.set_seekbar(ele, expected_value=_state)

                    self._system_confirm_locator()

            # handle with Address2/City/ZIP_code

            for k, v in kwargs.items():

                if k in ['address2', 'city', 'zip_code']:

                    if smart_input:

                        continue

                    eles_2_dict = self._get_location_general_2()

                    ele = eles_2_dict[k]

                    ele.click()

                    ele.clear()

                    ele.send_keys(v)

    def _get_location_general_1(self):
        """Address/Country/State"""

        value = "locator_location_part1"

        locator = get_locator(value)

        eles_1 = self.find_element(*locator, multiple=True)

        eles_1_dict = {'address': eles_1[0],
                       'country': eles_1[1],
                       'state': eles_1[2],
                       }

        return eles_1_dict

    def _get_location_general_2(self):
        """Address2/City/ZIP_code"""

        value = "locator_location_part2"

        locator = get_locator(value)

        eles_2 = self.find_element(*locator, multiple=True)

        logger.info(f'the eles_2:{eles_2}')

        eles_2_dict = {'address2': eles_2[0],
                       'city': eles_2[1],
                       'zip_code': eles_2[2],
                       }

        return eles_2_dict

    def get_commission_location(self, *args, **kwargs):
        """ get commission->location parameters

        args:
            | address | string |
            | address2 | string |
            | country | string | 出现限定的国家值 |
            | state | string | 对美澳等国家出现限定的省份/州名 |
            | city | string |
            | zip_code | string |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Return:
            | dict |
        Examples:
            | ${result}	| Get Commission Location  |
            | ${result}	| Get Commission Location  |  country	| ret_format=list |
            | ${result}	| Get Commission Location  |  country	| ret_format=list |  ret_type=list |
            | ${result}	| Get Commission Location  |  country	| ret_format=dict |
            | ${result}	| Get Commission Location  |  country	| address   | ret_format=list |
            | ${result}	| Get Commission Location  |  country	| address   | ret_format=dict |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        # Address/Country/State,etc.

        value = "locator_location_part1"

        locator = get_locator(value)

        eles_1 = self.find_element(*locator, multiple=True)

        eles_1_dict = {'address': eles_1[0],
                       'country': eles_1[1],
                       'state': eles_1[2],
                       }
        r_eles_1_dict = reverse_dict(eles_1_dict)

        eles = self.find_element(*locator, multiple=True)

        for index, i in enumerate(eles):

            logger.debug(f"""the value is :{self.get_element_attribute(i, "content-desc")}""")

            ret_dict.update({r_eles_1_dict[eles_1[index]]: self.get_element_attribute(i, "content-desc")})

        # Address2/City/ZIP_code,etc.

        value = "locator_location_part2"

        locator = get_locator(value)

        eles_2 = self.find_element(*locator, multiple=True)

        eles_2_dict = {'address2': eles_2[0],
                       'city': eles_2[1],
                       'zip_code': eles_2[2],
                       }

        r_eles_2_dict = reverse_dict(eles_2_dict)

        eles = self.find_element(*locator, multiple=True)

        for index, i in enumerate(eles):

            logger.debug(f"""the value is :{self.get_element_attribute(i, "text")}""")
            ret_dict.update({r_eles_2_dict[eles_2[index]]: self.get_element_attribute(i, "text")})

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def exit_commission_location_page(self, operate='next'):
        """ Go to system->commission->location page and go back to the system->commission page or next page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Location Page | operate=next |
            | Exit Commission Location Page | operate=exit |
        """
        _operate = operate.lower()

        if operate == 'exit':

            self._general_backward_upper_page_type2()

        elif operate == 'next':

            self._system_next_locator()
