from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 点表增删查数据

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 373

cmd_type_c2s = 374

common_dict = {

    **attr_map_common_opt,

    'id': ('', 'int'),
    'mqttQos': ('mqtt_qos', 'int'),
    'mqttTopic': ('mqtt_topic', 'string'),
    'mqttSubTopic': ('mqtt_subtopic', 'string'),
    'offlineEnable': ('offline_message_enabled', 'int'),

    'type': ('data_table_type', 'int'),
    'interval': ('', 'int'),
    'status': ('', 'int'),
    'startTime': ('start_time', 'int'),
    'endTime': ('end_time', 'int'),

    'listenData': ('listen_data_fields', 'list'),
    'dataArea': ('uploaded_data_table_fields', 'list'),
    'order': ('', 'string'),

}
attr_map_s2c = {

    **common_dict,

    'dataTableVer': ('data_table_version', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

    'data': ('data_list', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DataTableSet(object):

    def data_table_set(self, *args, **kwargs):
        """  data table set-T373/374

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |

        Kwargs:
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | order | string | 工单号 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Set | opt=3 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def data_table_set_result_check(self, _response, **kwargs):
        """  data table set result check-T374

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Data Table Set  | opt=3 |
        | ${status} = | Data Table Set Result Check | ${response} | result=0  | opt=3 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def data_table_set_from_aws_get(self, *args, **kwargs):
        """  data table set from AWS,the message get-T373

        Args：
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | order | string | 工单号 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_set_from_aws_get_check(self, **kwargs):
        """  data table set from AWS,the message get check-T373

        Kwargs:
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | order | string | 工单号 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Data Table Set From AWS Get Check | opt=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_set_from_device_get(self, *args, **kwargs):
        """   data table set response from device,the message get-T374

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_set_from_device_get_check(self, **kwargs):
        """  data table set response from device,the message get check-T374

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Data Table Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T373/374

        Args for T373:
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | order | string | 工单号 |
        Args for T374：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 373/374 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=373 | rx_time_window=300 |
        | ${status} |Data Table Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_set_from_device_get, cmd_type_s2c: self.data_table_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def data_table_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T373/374

        Kwargs for T373:
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_table_version | int | 此配置表对应的点表版本号 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传 |
            | interval | int | 执行间隔，最小1000，单位：ms |
            | status | int | 执行状态，0：运行，1：取消 |
            | start_time | int | 开始执行的时间戳 |
            | end_time | int | 结束执行的时间戳 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送 |
            | uploaded_data_table_fields | list | 需要上送的点表字段 |
            | order | string | 工单号 |
        Kwargs for T374：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0：新增 1：删除此点表配置 2：查询此点表配置表内容 3：查询当前设备所有的点表配置表ID |
            | data_list | list | 点表配置表id集合，仅适用于opt=3 |
            | id | int | 点表配置表的唯一标识 |
            | mqtt_qos | int | 点表响应协议的mqtt消息等级，仅适用于opt=2 |
            | mqtt_topic | string | 点表响应协议的mqtt消息主题，仅适用于opt=2 |
            | mqtt_subtopic | string | 点表响应协议的mqtt消息子主题，仅适用于opt=2 |
            | offline_message_enabled | int | 离线消息处理，0：离线无需缓存消息 1：离线缓存消息，在上线后补传，仅适用于opt=2 |
            | data_table_type | int | 配置表类型，0：实时 1：仅上传一次? 2：监听，字段数据发生变化时上传 3：被请求时上传，仅适用于opt=2 |
            | interval | int | 执行间隔，最小1000，单位：ms，仅适用于opt=2 |
            | status | int | 执行状态，0：运行，1：取消，仅适用于opt=2 |
            | start_time | int | 开始执行的时间戳，仅适用于opt=2 |
            | end_time | int | 结束执行的时间戳，仅适用于opt=2 |
            | listen_data_fields | list | 监听的字段，如果它们发送变化就按照执行间隔上送，仅适用于opt=2 |
            | uploaded_data_table_fields | list | 需要上送的点表字段，仅适用于opt=2 |
            | order | string | 工单号 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 373/374 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=374 | rx_time_window=300 | filter_mode=and |
        | ${status} | Data Table Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_set_from_device_get_check, cmd_type_s2c: self.data_table_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
