from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name
from ..constant import attr_map_common

# 设备参数更新数据，废弃

app_name = "auth"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_c2s = 107

cmd_type_s2c = 108

attr_map_c2s = {
    'distributor': ('', 'string'),
    'installer': ('', 'string'),
    'maint': ('maintainer', 'string'),

    'usr_account': ('user_account', 'string'),
    'reg_date': ('registration_date', 'string'),
    'GPS': ('', 'list'),

    'timezone': ('', 'float'),
    'timezoneStr': ('timezone_in_string', 'int'),
    'DST': ('dst', 'int'),
    'zoneinfo': ('zone_info', 'string'),
    'postcode': ('', 'string'),
}

attr_map_s2c = {
    'result': ('', 'int'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DeviceRefresh(object):

    def device_parameter_from_device_get(self, *args, **kwargs):
        """  Device Bonding Info get-T107

        Args：
            | opt | int | 1:bonding,0:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string |
            | IBG_VER | string | <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS device number |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes |
            | timezone |  float |-12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Login Info From Device Get | device | protocol_version | device_version | hardware_version |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def device_parameter_from_device_get_check(self, **kwargs):
        """  Device Bonding Info get check-T107

        Kwargs:
            | opt | int | 1:bonding,0:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string |
            | IBG_VER | string | <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS device number |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes |
            | timezone |  float |-12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Login Info From Device Get Check | device_type=0 | device_version=1 | hardware_version=103 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_parameter_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_parameter_from_aws_get(self, *args, **kwargs):
        """  AWS to Device Login Info get-T108

        Args：
            | opt | int | 1:bonding,0:debonding  |
            | result |  int | 0:success,1:already bonded, 2:fail  |
            | reason | int | 0:operate successfully,1: device is not registered,3:the packet Rxis abnormal,4:the terminal account doesn't exist,5:the gateway has been bonded 6:the distributor doesn't exist,7: the installer doesn't exist,8: the maintainer doesn't exist,9:user fails to register |
            | app_result | int | 1: user has registered and successfully bonded |

        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Login Info From AWS Get | device | protocol_version | device_version | hardware_version |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def device_parameter_from_aws_get_check(self, **kwargs):
        """  AWS to Device Login Info get check-T108

        Kwargs:

            | opt | int | 1:bonding,0:debonding  |
            | result |  int | 0:success,1:already bonded, 2:fail  |
            | reason | int | 0:operate successfully,1: device is not registered,3:the packet Rxis abnormal,4:the terminal account doesn't exist,5:the gateway has been bonded 6:the distributor doesn't exist,7: the installer doesn't exist,8: the maintainer doesn't exist,9:user fails to register |
            | app_result | int | 1: user has registered and successfully bonded |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Login Info From AWS Get Check | result=0 | reason=0 | upload_url=http://18.144.74.200:9005/v1/file/upload |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.device_parameter_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def device_parameter_info_from_msg_get(self, *args, msg=None, cmd_type=107, **kwargs):
        """  Device to/from AWS Bonding Info get T107/108

        Args for T107：
            | opt | int | 1:bonding,0:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string |
            | IBG_VER | string | <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS device number |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes |
            | timezone |  float |-12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
        Args for T108：
            | opt | int | 1:bonding,0:debonding  |
            | result |  int | 0:success,1:already bonded, 2:fail  |
            | reason | int | 0:operate successfully,1: device is not registered,3:the packet Rxis abnormal,4:the terminal account doesn't exist,5:the gateway has been bonded 6:the distributor doesn't exist,7: the installer doesn't exist,8: the maintainer doesn't exist,9:user fails to register |
            | app_result | int | 1: user has registered and successfully bonded |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 107 or 108 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=102/101 | rx_time_window=300 | filter_mode=and |
        | ${status} | Login Info Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets} | cmd_type=101 |
        | ${status} | Login Info Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets} | cmd_type=101 | ret_format=dict |
        | ${status} | Login Info Get | upload_url | msg=${packets} | cmd_type=102 |
        | ${status} | Login Info Get | upload_url | msg=${packets} | cmd_type=102 | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=101 | rx_time_window=300 |
        | ${status} | Login Info Get | device | data_table_version | device_type | protocol_version | hardware_version | msg=${packets}[0] | cmd_type=101 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_parameter_from_device_get, cmd_type_s2c: self.device_parameter_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def device_parameter_info_from_msg_get_check(self, msg=None, cmd_type=107, **kwargs):
        """  Device to/from AWS Bonding Info get check T107/108

        Kwargs for T107：
            | opt | int | 1:bonding,0:debonding  |
            | distributor | string |  <=64Bytes,distributor name |
            | installer | string | <=64Bytes,installer name |
            | maintainer | string | <=64Bytes,maintainer name |
            | site_name | string | <=64Bytes,site name  |
            | connection_type | int | 0:disconnect,1:eth0,2:eth1,3:WiFi,4:4G |
            | IP | string | <=20Bytes,IP address |
            | mask | string | <=20Bytes,IP network mask |
            | gateway | string | <=20Bytes,IP gateway |
            | ICCID | string | <=20Bytes,ICCD |
            | IMSI | string | <=20Bytes,IMSI |
            | MAC | string | <=20Bytes,MAC address |
            | user_account | string | <=64Bytes,user account |
            | registration_date | string | <=20Bytes,registration date |
            | GPS | float list | the format:[float_number1,float_number2],WGS-84 format |
            | IBG_SN | string |
            | IBG_VER | string | <=32Bytes |
            | AWS_VER | string | <=32Bytes |
            | APP_VER | string | <=32Bytes |
            | SL_VER | string | <=32Bytes |
            | FHP_NUM | int | 1~15 |
            | FHP_SN | string list | <=32Bytes |
            | PE_SN | string list | <=32Bytes |
            | FPGA_VER | string list | <=32Bytes |
            | DCDC_VER | string list | <=32Bytes |
            | INV_VER | string list | <=32Bytes |
            | BMS_SN | string list | <=32Bytes,BMS device number |
            | BMS_VER | string list |
            | BL_VER | string list | <=32Bytes |
            | TH_VER | string list | <=32Bytes |
            | METER_VER | string | <=32Bytes |
            | timezone |  float |-12~+14,may contain 0.5 |
            | timezone_in_string |  string | the example format "EST5EDT,M3.2.0,M11.1.0" |
            | dst |  int | 0:UTC,1: auto for DST |
            | zone_info |  string | <=32Bytes,area |
        Kwargs for T108：
            | opt | int | 1:bonding,0:debonding  |
            | result |  int | 0:success,1:already bonded, 2:fail  |
            | reason | int | 0:operate successfully,1: device is not registered,3:the packet Rxis abnormal,4:the terminal account doesn't exist,5:the gateway has been bonded 6:the distributor doesn't exist,7: the installer doesn't exist,8: the maintainer doesn't exist,9:user fails to register |
            | app_result | int | 1: user has registered and successfully bonded |

        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 107 or 108 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=102/101 | rx_time_window=300 | filter_mode=and |
        | ${status} | Login Info Get Check | device=ULG_EMSTEST_T004 | device_type=0 | protocol_version=V1.11.00 | hardware_version=103 | msg=${packets} | cmd_type=101 |
        | ${status} | Login Info Get Check | upload_url=http://52.53.190.143:11001/manage/file/upload | msg=${packets} | cmd_type=102 |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.device_parameter_from_device_get_check, cmd_type_s2c: self.device_parameter_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
