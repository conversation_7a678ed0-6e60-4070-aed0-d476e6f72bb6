from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 系统设置

cmd_type_c2s = 1721

cmd_type_s2c = 1722

attr_map_c2s = {

    **attr_map_common_opt,

    'reboot': ('', 'int'),
    'reset': ('', 'int'),
    'update': ('', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalSysSet(Base):

    def local_system_set(self, *args, **kwargs):
        """  local system set-T1721
        Args：
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功 1：设置失败 |
            | reboot | int | 重启，1：生效，目前未实现 |
            | reset | int | 重置，1：生效，目前未实现 |
            | update | int | 本地升级，1：生效，目前未实现 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local System Set | reboot=1 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_system_set_result_check(self, _response, **kwargs):
        """  local system set result check-T1722

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询，1：设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败，2：设置失败 |
            | id | int | PE所属ID |
            | out_power| int | 输出功率，单位：w |
            | run_status| int | 运行状态 |
            | pv_voltage| float list | PV1~PV4电压，单位：V |
            | pv_current| float list | PV1~PV4电流，单位：A |
            | pv_arc_strength| int list | PV1~PV4拉弧强度 |
            | pv_afci_current| int list | PV1~PV4 AFCI电流，单位： mA |
            | cavity_temp| float | 腔温，单位：摄氏度 |
            | heater1_temp| float | 散热器1温度，单位：摄氏度 |
            | heater1_tem2| float | 散热器2温度，单位：摄氏度 |
            | insu_res_conduct| int | 绝缘阻抗电导，单位：us |
            | aux_voltage| float | 12V辅源电压，单位：V |
            | pv_side_bus_voltage| float | PV侧BUS电压，单位：V |
            | pe_side_bus_voltage| float | PE侧BUS电压，单位：V |
            | afci_arc_hw_version| int | AFCI拉弧硬件版本 |
            | afci_arc_sw_version| int | AFCI拉弧软件版本 |
            | afci_arc_algriothm_version| int | AFCI 拉弧算法版本 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local System Set |
        | ${status} = | Local System Set Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
