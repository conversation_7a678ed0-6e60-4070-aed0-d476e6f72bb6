from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# Sunnova数据主动上传

app_name = "sunnova"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 801

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'apowerVO': ('power_group', 'list'),
    'frequencyVO': ('frequency_group', 'list'),
    'voltageVO': ('voltage_group_grid', 'list'),
    'apowerVoltageList': ('apower_voltage_group', 'list'),
    'currentVO': ('current_group_grid_load', 'list'),
    'apowerCurrentList': ('apower_current_group', 'list'),
    'reactivePowerVO': ('reactive_power_group', 'list'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class sunnovaDataUpload(object):

    def sunnova_data_from_device_get(self, *args, **kwargs):
        """  sunnova date upload from device,the message get-T801

        Args：
            | power_group | list | 功率组数据 |
            | frequency_group | list | 频率组数据 |
            | voltage_group_grid | list | 电网电压组数据 |
            | apower_voltage_group | list | aPower电压组数据 |
            | current_group_grid_load | list | 电网/电流组数据 |
            | apower_current_group | list | aPower电流组数据 |
            | reactive_power_group | list | 无功功率及其他组数据 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Sunnova Data From Device Get | soc | soh |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def sunnova_data_from_device_get_check(self, **kwargs):
        """  sunnova date upload data from device,the message get check-T801

        Kwargs:
            | power_group | list | 功率组数据 |
            | frequency_group | list | 频率组数据 |
            | voltage_group_grid | list | 电网电压组数据 |
            | apower_voltage_group | list | aPower电压组数据 |
            | current_group_grid_load | list | 电网/电流组数据 |
            | apower_current_group | list | aPower电流组数据 |
            | reactive_power_group | list | 无功功率及其他组数据 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Sunnova Data From Device Get Check | soc=75 | run_status=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sunnova_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sunnova_data_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T801

        Args for T801：
            | power_group | list | 功率组数据 |
            | frequency_group | list | 频率组数据 |
            | voltage_group_grid | list | 电网电压组数据 |
            | apower_voltage_group | list | aPower电压组数据 |
            | current_group_grid_load | list | 电网/电流组数据 |
            | apower_current_group | list | aPower电流组数据 |
            | reactive_power_group | list | 无功功率及其他组数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 801 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=801 | rx_time_window=320 | filter_mode=and |
        | ${status} | Sunnova Data From Msg Get | soc | msg=${packets} | ret_format=dict |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunnova_data_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def sunnova_data_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T801

        Kwargs for T801：
            | power_group | list | 功率组数据 |
            | frequency_group | list | 频率组数据 |
            | voltage_group_grid | list | 电网电压组数据 |
            | apower_voltage_group | list | aPower电压组数据 |
            | current_group_grid_load | list | 电网/电流组数据 |
            | apower_current_group | list | aPower电流组数据 |
            | reactive_power_group | list | 无功功率及其他组数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 801 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=801 | rx_time_window=320 | filter_mode=and |
        | ${status} | Sunnova Data From Msg Get Check | soc=56 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sunnova_data_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
