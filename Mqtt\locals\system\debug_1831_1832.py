from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 调试模式-设备编号

cmd_type_c2s = 1831

cmd_type_s2c = 1832

attr_map_c2s = {

    **attr_map_common_opt,

    'devNum': ('device_number', 'int'),
    'devMap': ('device_map', 'dict'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDebugAddressing(Base):

    def local_debug_device_addressing_get(self, *args, **kwargs):
        """  local debug device addressing get-T1831
        Args：
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：总台数匹配异常；2：ID编址失败；3：#超时失败；4：IBG未在编址状态 |
            | device_number | int | 设备数量 |
            | device_map | int | 设备编址列表 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | device_number | int | 设备数量 |
            | device_map | int | 设备编址列表 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Debug Device Addressing Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_debug_device_addressing_get_result_check(self, _response, **kwargs):
        """  local debug device addressing get result check-T1832

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：总台数匹配异常；2：ID编址失败；3：#超时失败；4：IBG未在编址状态 |
            | device_number | int | 设备数量 |
            | device_map | int | 设备编址列表 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Debug Device Addressing Get |
        | ${status} = | Local Debug Device Addressing Get | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
