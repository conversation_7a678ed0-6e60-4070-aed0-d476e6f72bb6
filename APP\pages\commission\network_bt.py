from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException, TimeoutException
from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Bt(Base):

    def commission_network_bt_pair(self, pair="FHP_Samuel", password="12345678"):
        """  Commission->Network Setting Page-User BT to connect system

        Kwargs:
            | pair | string | aGate BT name  |
            | password | string | aGate BT name  |
        Return:
            | None |
        Examples:
            | Commission network Bt Pair |  pair=BT_123 | password=1234567 |
        """

        logger.info(f"the user parameters:pair:{pair},password:{password}")

        value = "locator_network_setting_bt"

        locator = get_locator(value)

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        # Xiaomi, request permission from user to enable BT
        value = "locator_network_setting_bt_xiaomi"

        locator = get_locator(value)

        self.find_element(*locator, throw_exception=False, click=True, timeout=1)

        result, already_connected = self._search_bt_pair(pair=pair)

        logger.info(f'the BT search result:{result},already_connected:{already_connected}')

        if result:

            if not already_connected:

                self._bt_password_input(password, already_connected=already_connected)

            else:
                logger.info('system has been in paired!')
        else:

            raise ValueError(f'Sorry,not found the target BT pair:{pair}')

        # Scenario 1, first commission after pair the bt the below button is next
        # self._system_next_locator()

        # Scenario 2, second commission after pair the bt the below button is complete

        value = "locator_bt_completed"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def _search_bt_state(self, bt_name_list=None, already_connected=None, retry=None):

        value = "locator_network_setting_bt_search"

        locator = get_locator(value)

        eles = self.find_element(*locator, throw_exception=False, multiple=True)

        if eles is not None:

            try:
                _list = [self.get_element_attribute(x, key='content-desc') for x in eles]

                logger.debug(f"Found the content-desc list:{_list}!")

                bt_name_list = [i.split('\n')[0] for i in _list]

                bt_status_list = [i.split('\n')[-1] for i in _list]

                if 'Pairing' in bt_status_list:

                    logger.debug(f"OK,found the pairing BT pair for {pair}!")

                    sleep(30)

                    already_connected = True

                if 'Connected' in bt_status_list:

                    already_connected = True

                    logger.debug(f"OK,found the connected BT pair!")

            except StaleElementReferenceException:

                sleep(3)

                retry = True

        else:

            bt_name_list = list()

        return eles, bt_name_list, already_connected, retry

    def _search_bt_pair(self, pair=None, timeout=200):

        found = False

        retry = False

        swipe_up_number = 0

        already_connected = False

        start = time()

        while not found:

            eles, bt_name_list, already_connected, retry = self._search_bt_state(retry=retry)

            logger.debug(f"the current BT pair list:{bt_name_list}")

            if bt_name_list and (pair in bt_name_list):

                logger.info(f"OK,found the target BT pair:{pair}!")

                index = bt_name_list.index(pair)

                found_pair = eles[index]

                logger.info(f'ok,got the pair:{found_pair}')

                found_pair.click()

                found = True

                break

            elif not retry:

                before = self.get_page_source()

                self.swipe_up()

                swipe_up_number += 1

                logger.debug(f'the current swipe up number:{swipe_up_number}')

                after = self.get_page_source()

                if before == after:

                    logger.info('OK,already scroll to the bottom of the page!')

                    value = "locator_network_setting_bt_general"

                    locator = get_locator(value)

                    ele = None

                    while not ele:

                        ele = self.find_element(*locator, throw_exception=False, timeout=1)

                        self.swipe_down()

                    logger.info('OK,go back to the top of the page!')

            end = time()

            if (end - start) > timeout:

                raise ValueError('Fail to search the BT pair:{pair}!')

        return found, already_connected

    def _bt_password_input(self, password="12345678", already_connected=False, timeout=45):

        logger.info(f'OK,the user parameters in _bt_password_input:password:{password},timeout:{timeout}')

        if not already_connected:

            value = "locator_network_setting_bt_password"

            locator = get_locator(value)

            available = False

            start = time()

            while not available:

                try:
                    ele = self.find_element(*locator)
                    ele.click()
                    ele.send_keys(password)
                    available = True

                except TimeoutException:

                    sleep(2)

                end = time()

                if (end - start) > timeout:

                    eles, bt_name_list, already_connected, retry = self._search_bt_state()

                    if already_connected:

                        logger.info('OK,has connected to the system!')

                        break

                    else:
                        raise ValueError('Fail to input password for BT pair!')

            self._system_confirm_locator()

    def commission_network_bt_unpair(self, pair="FHP_Samuel"):
        """  Commission->Network Setting Page-User BT to disconnect system

        Kwargs:
            | pair | string | aGate BT name  |
        Return:
            | None |
        Examples:
            | Commission network Bt Unpair |  pair=BT_123 |
        """

        result, already_connected = self._search_bt_pair(pair=pair)

        if result:

            value = "locator_network_setting_bt_unpair"

            locator = get_locator(value)

            self.find_element(*locator, click=True)

            self._system_confirm_locator()

        else:
            raise ValueError(f'Sorry,not found the target BT pair:{pair}')
