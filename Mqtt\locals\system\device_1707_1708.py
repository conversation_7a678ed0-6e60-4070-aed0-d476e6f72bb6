from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 设备信息

cmd_type_c2s = 1707

cmd_type_s2c = 1708

attr_map_c2s = {

    **attr_map_common_opt,

    'ibgRunStatus': ('ibg_run_status', 'int'),
    'name': ('', 'string'),
    'electricity_type': ('', 'int'),
    'dspRunStatus': ('dsp_run_status', 'int'),
    'gridVol1': ('grid_voltage_L1', 'float'),
    'gridVol2': ('grid_voltage_L2', 'float'),
    'gridCurr1': ('grid_curret_L1', 'float'),
    'gridCurr2': ('grid_curret_L2', 'float'),
    'loadCurr1': ('load_curret_L1', 'float'),
    'loadCurr2': ('load_curret_L2', 'float'),

    'mainRelay1Status': ('grid_relay1_status', 'int'),
    'mainRelay2Status': ('grid_relay2_status', 'int'),
    'solarRelayStatus': ('solar_relay_status', 'int'),
    'smartRelay1Status': ('load_relay1_status', 'int'),
    'smartRelay2Status': ('load_relay2_status', 'int'),
    'smartRelay3Status': ('ev_relay_status', 'int'),
    'loadRelay1Stat': ('load_solar_relay1_status', 'int'),
    'loadRelay2Stat': ('load_solar_relay2_status', 'int'),

    'gridLineVol': ('grid_line_voltage', 'int'),
    'blackStartRelay': ('black_start_relay_status', 'int'),
    'BFPVApboxRelay': ('bfpv_apbox_relay', 'int'),

    'gridFreq': ('grid_freq', 'float'),
    'dspSettingFreq': ('dsp_freq', 'float'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDeviceInfo(Base):

    def local_device_info_get(self, *args, **kwargs):
        """  local device info get-T1707
        Args：
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay1_status | int | 市电继电器1状态，
            | grid_relay2_status | int | 市电继电器2状态
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Device Info Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_device_info_get_result_check(self, _response, **kwargs):
        """  local device info get result check-T1708

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0:操作成功，1:查询失败 |
            | ibg_run_status | int | IBG运行状态，0：初始状态；1：待机状态；2：自发自用；3：TOU(平衡)；4：TOU(经济)；5：并网状态下的仅备电状态；6：风暴等待模式；7：离网，电池备电中；8：离网，发电机备电中；9：VPP调度；10：紧急停止；11：故障进入安全模式；12：电池维护；13：调试；14：发电机备电演习测试模式；15：电池备电演习测试模式；16：升级状态；17：停机维护模式；18：禁止进入电网；19：PCS运行检测；20：BB&NEM；21：BB&CSS；22：BB&CGS+ |
            | name | string | 参数表名称 |
            | electricity_type | int | 电费类型，1:分时电价；2：阶梯电价；3：固定电价；4：分时&阶梯 5：BB套餐；6：peak demand |
            | dsp_run_status | int | DSP运行状态，0：初始化；1：获取参数；2：监听；3：待机；4：市电并网运行；5：离网运行；6：离网油机接入运行；7：故障 |
            | grid_voltage_L1 | float | 市电电压L1，单位：V |
            | grid_voltage_L2 | float | 市电电压L2，单位：V |
            | grid_curret_L1 | float | 市电电流L1，单位：A |
            | grid_curret_L2 | float | 市电电流L2，单位：A |
            | load_curret_L1 | float | 负载电流L1，单位：A |
            | load_curret_L2 | float | 负载电流L2，单位：A |
            | grid_freq | float | 市电频率，单位：Hz |
            | dsp_freq | float | DSP频率设定 |
            | grid_relay1_status | int | 市电继电器1状态，
            | grid_relay2_status | int | 市电继电器2状态
            | solar_relay_status | int | 光伏继电器状态，0：断开，1：闭合 |
            | load_relay1_status | int | 负载1继电器状态，0：断开，1：闭合 |
            | load_relay2_status | int | 负载2继电器状态，0：断开，1：闭合 |
            | ev_relay_status | int | EV继电器状态，0：断开，1：闭合 |
            | load_solar_relay1_status | int | 负载侧光伏继电器1状态，0：断开，1：闭合 |
            | load_solar_relay2_status | int | 负载侧光伏继电器2状态，0：断开，1：闭合 |
            | grid_line_voltage | int | 市电线电压L1-L2，单位：0.1V |
            | black_start_relay_status | int | 黑启动继电器状态，0：断开，1：闭合 |
            | bfpv_apbox_relay | int | 光伏电表前abox继电器状态，0：断开，1：闭合 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Device Info Get |
        | ${status} = | Local Device Info Get Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
