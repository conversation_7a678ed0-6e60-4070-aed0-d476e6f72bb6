from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Ethernet(Base):

    def go_to_commission_network_setting_ethernet_page(self, timeout=None):
        """ Go to system->commission->network setting->Ethernet page

        Kwargs:
            | timeout | 单位：秒，等待时间 |
        Return:
            | None |
        Examples:
            | Go To Commission Network Setting Ethernet Page |
        """
        value = "locator_network_setting_ethernet"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_network_setting_ethernet_page(self):
        """ exit system->commission->network setting->Ethernet page and go back to the system->commission->network setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Network Setting Ethernet Page |
        """
        self._general_backward_upper_page_type2()

    def config_commission_network_ethernet_parameters(self, **kwargs):
        """ config commission->Network->Ethernet parameters

        Kwargs:
            | ethernet_enabled | bool |
            | auto_connect | bool |
            | manual_connect | bool |
            | IP | string | IPv4 address |
            | DNS | string | IPv4 address |
            | gateway | string | IPv4 address |
        Return:
            | None |
        Examples:
            | Config Commission network Ethernet Parameters |  ethernet_enabled=false |
            | Config Commission network Ethernet Parameters |  auto_connect=true |
            | Config Commission network Ethernet Parameters |  manual_connect=true | IP=**************	| DNS=******* | gateway=*************
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        self._set_network_parameters(**kwargs)

    def get_commission_network_ethernet_parameters(self, *args, **kwargs):
        """ get commission->Network->Ethernet parameters

        args:
            | ethernet_enabled | bool |
            | auto_connect | bool |
            | manual_connect | bool |
            | mac | string |  | MAC address |
            | IP | string | IPv4 address |
            | DNS | string | IPv4 address |
            | gateway | string | IPv4 address |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples:
            | ${result}	| Get Commission network Ethernet Parameters  |
            | ${result}	| Get Commission network Ethernet Parameters |  mac	| ethernet_enabled | gateway |
        """
        logger.info(f"the user parameters:args:{args},kwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        ret_dict = {}

        self._get_ethernet_parameters(ret_dict)

        return ret_value_processing(args, user_dict=ret_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_ethernet_parameters(self, ret_dict):

        locator_name = "locator_network_setting_ethernet_id"

        value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

        ret_dict.update({"ethernet_enabled": value})

        if value == 'true':

            locator_name = "locator_network_setting_ethernet_auto_connect"

            value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            ret_dict.update({"auto_connect": value})

            locator_name = "locator_network_setting_ethernet_manual_connect"

            manual_value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='checked', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True)

            ret_dict.update({"manual_connect": manual_value})

            if manual_value == 'true':

                locator_name = "locator_network_setting_ethernet_mac"

                value = self._get_attribute_value_by_locator(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, attr_get_func=self.get_element_attribute, attr_name='text', ctrl_enter_strip=True, ctrl_empty_strip=True, replace_empty_to_dash=True, ctrl_lower_case=False)

                ret_dict.update({"MAC": value})

                locator_name = "locator_network_setting_ethernet_general"

                eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                eles_dict = {'IP': eles[0],
                             'DNS': eles[1],
                             'gateway': eles[2]}
            else:
                locator_name = "locator_network_setting_ethernet_general_2"

                eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

                eles = eles[-4:]

                eles_dict = {'MAC': eles[0],
                             'IP': eles[1],
                             'DNS': eles[2],
                             'gateway': eles[3]}

            r_eles_dict = reverse_dict(eles_dict)

            dict_len = len(eles_dict)

            for index, i in enumerate(eles):

                logger.debug(f"""the value is :{self.get_element_attribute(i, "content-desc")}""")

                _value = self.get_element_attribute(i, "text")

                if dict_len == 4 and index == 0:

                    value = _value.split('\n')[-1].replace(' ', '-')

                else:

                    value = _value.split('\n')[-1].lower().replace(' ', '-')

                ret_dict.update({r_eles_dict[eles[index]]: value})
