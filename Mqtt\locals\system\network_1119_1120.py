from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 网络终端开关查询设置

cmd_type_c2s = 1119

cmd_type_s2c = 1120

attr_map_c2s = {

    **attr_map_common_opt,

    'ethernet0NetSwitch': ('Eth0', 'int'),
    'ethernet1NetSwitch': ('Eth1', 'int'),
    'wifiNetSwitch': ('WiFi', 'int'),
    '4GNetSwitch': ('4G', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalNetworkSwitch(Base):

    def local_network_switch_set(self, *args, **kwargs):
        """  local network switch set-T1119
        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Network Switch Set |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_network_switch_set_result_check(self, _response, **kwargs):
        """  local network switch set result check-T1120

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因, 0：操作成功；1：查询失败；2：设置失败 |
            | Eth0 | int | 0:关闭，1：打开 |
            | Eth1 | int | 0:关闭，1：打开 |
            | WiFi | int | 0:关闭，1：打开 |
            | 4G   | int | 0:关闭，1：打开 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Network Switch Set |
        | ${status} = | Local Network Switch Set Get Result Check | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
