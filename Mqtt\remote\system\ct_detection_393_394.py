from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# CT检测设置查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 393

cmd_type_c2s = 394

attr_map_s2c = {

    **attr_map_common_opt,

    'ctCheckUnit': ('ct_check_list', 'list'),

}

attr_map_c2s = {

    **attr_map_common_result,
    **attr_map_s2c,

    'errStr': ('error_string', 'string'),
    'fastCheckRet': ('ct_check_result', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class CT(object):

    def ct_set(self, *args, **kwargs):
        """  ct set-T393/394

        Args：
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
        Kwargs:
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list |CT检测字段列表 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | CT Set |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def ct_set_result_check(self, _response, **kwargs):
        """  ct set result check-T394

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  CT Set |
        | ${status} = | CT Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def ct_set_from_aws_get(self, *args, **kwargs):
        """  ct set from AWS,the message get-T393

        Args：
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   CT Set From AWS Get | opt |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def ct_set_from_aws_get_check(self, **kwargs):
        """  ct set from AWS,the message get check-T393

        Kwargs:
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | CT Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.ct_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def ct_set_from_device_get(self, *args, **kwargs):
        """   ct set response from device,the message get-T394

        Args：
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
            Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | CT Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def ct_set_from_device_get_check(self, **kwargs):
        """  ct set response from device,the message get check-T394

        Kwargs:
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  CT Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.ct_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def ct_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T393/394

        Args for T393:
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
        Args for T394：
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 393/394 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=393 | rx_time_window=300 |
        | ${status} | CT Set From Msg Get | opt | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.ct_set_from_device_get, cmd_type_s2c: self.ct_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def ct_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T393/394

        Kwargs for T393:
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
        Kwargs for T394：
            | result | int | 结果，0:下发成功 1:无法检测 |
            | opt | int | 操作，0:CT查询和初检, 1:CT单检, 2:CT全检（预留功能） |
            | ct_check_list | list | CT检测字段列表 |
            | error_string | string | 错误提示信息 |
            | ct_check_result | list | CT初检结果 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 393/394 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=394 | rx_time_window=300 | filter_mode=and |
        | ${status} | CT Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.ct_set_from_device_get_check, cmd_type_s2c: self.ct_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
