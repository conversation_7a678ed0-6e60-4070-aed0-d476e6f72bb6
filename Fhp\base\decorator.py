from time import time

from robot.api import logger


class Decorator(object):

    def TimeMeasure(self, func):

        def wrapper(*args, **kwargs):

            start_time = time()

            result = func(*args, **kwargs)

            end_time = time()

            logger.info(f"Function {func.__name__} took {end_time - start_time} seconds to run")

            return result

        return wrapper

    def cache(self, func):

        cached_results = {}

        def wrapper(*args, **kwargs):

            key = (args, tuple(kwargs.items()))

            if key not in cached_results:

                cached_results[key] = func(*args, **kwargs)

            return cached_results[key]

        return wrapper
