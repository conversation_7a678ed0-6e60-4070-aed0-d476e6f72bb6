from APP.base import Base
from time import (sleep, time)
from robot.api import logger
from .locator import get_locator
from APP.util import (ret_value_processing, reverse_dict)


class Utilityplan(Base):

    def go_to_commission_utility_plan_and_incentives_page(self):
        """ Go to system->commission->utility plan&incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Utility Plan And Incentives Page |
        """

        value = "locator_utility_plan"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_utility_plan_and_incentives_page(self, operate='next'):
        """ Go to system->commission->utility plan&incentives page and go back to the system->commission page or next page

        Kwargs:
            | operate | string | exit or next(默认） |
        Return:
            | None |
        Examples:
            | Exit Commission Utility Plan And Incentives Page | operate=next |
            | Exit Commission Utility Plan And Incentives Page | operate=exit |
        """
        _operate = operate.lower()

        if operate == 'exit':

            self._general_backward_upper_page_type2()

        elif operate == 'next':

            self._system_next_locator()

    def go_to_commission_utility_plan_and_incentives_mode_page(self):
        """ Go to system->commission->utility plan&incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Utility Plan And Incentives Page |
        """
        value = "locator_utility_plan_mode"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def exit_commission_utility_plan_and_incentives_mode_page(self):
        """ Go to system->commission->utility plan&incentives page and go back to the system->commission page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Utility Plan And Incentives Page |
        """
        self._general_backward_upper_page_type2()

    def go_to_commission_utility_plan_and_incentives_grid_charge_export_page(self):
        """ Go to system->commission->utility plan&incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Utility Plan And Incentives Page |
        """
        value = "locator_utility_plan_grid_charge_export"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def exit_commission_utility_plan_and_incentives_grid_charge_export_page(self):
        """ Go to system->commission->utility plan&incentives page and go back to the system->commission page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Utility Plan And Incentives Page |
        """
        self._general_backward_upper_page_type2()

    def go_to_commission_utility_plan_and_incentives_energy_page(self):
        """ Go to system->commission->utility plan&incentives page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Utility Plan And Incentives Page |
        """
        value = "locator_utility_plan_energy_incentives"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def exit_commission_utility_plan_and_incentives_energy_page(self):
        """ Go to system->commission->utility plan&incentives page and go back to the system->commission page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Utility Plan And Incentives Page |
        """
        try:
            self._general_backward_upper_page()

        except Exception as e:

            logger.info(f'Got the exception:{e}')

            value = "locator_utility_plan_energy_incentives_no_policy"

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

    def commission_utility_plan_and_incentives(self, **kwargs):
        """ configure commission->system parameters

        Kwargs:
            | mode | string | self-consumption/emergency-backup/tou |
            | grid_charging | bool | yes/no |
            | max_grid_charging_allowed_rated_power | 0.1~10.0kW,颗粒度0.1kW,仅当grid_charging为yes时有效 |
            | energy_export | string | only_solar/solar+apower |
            | max_energy_export_allowed_rated_power_from_apower | 0.1~100000.0kW,颗粒度0.1kW, |
        Return:
            | None |
        Examples:
            | Config Commission Utility Plan And Incentives | mode=tou |
        """
        self.config_commission_utility_plan_and_incentives(**kwargs)

        self.exit_commission_utility_plan_and_incentives_page(operate='next')

    def config_commission_utility_plan_and_incentives(self, **kwargs):
        """ configure commission->system parameters

        Kwargs:
            | mode | string | self-consumption/emergency-backup/tou |
            | grid_charging | bool | yes/no |
            | max_grid_charging_allowed_rated_power | 0.1~10.0kW,颗粒度0.1kW,仅当grid_charging为yes时有效 |
            | energy_export | string | only_solar/solar+apower |
            | max_energy_export_allowed_rated_power_from_apower | 0.1~100000.0kW,颗粒度0.1kW, |
        Return:
            | None |
        Examples:
            | Config Commission Utility Plan And Incentives | mode=tou |
        """
        logger.info(f"the user parameters:kwargs:{kwargs}")

        # Mode

        mode = kwargs.pop('mode', None)

        if mode is not None:

            mode_dict = {'self-consumption': 2, 'emergency-backup': 1, 'tou': 0}

            self.go_to_commission_utility_plan_and_incentives_mode_page()

            locator_name = "locator_utility_plan_mode_values"

            eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

            eles[mode_dict[mode]].click()

            locator_name = "locator_system_general_confirm"

            self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, retry=2, will_do_loading_detect=True, click=True)

            self.exit_commission_utility_plan_and_incentives_mode_page()

        # Grid charge&export
        grid_charging = kwargs.pop('grid_charging', None)
        grid_max_power = kwargs.pop('max_grid_charging_allowed_rated_power', None)

        energy_export = kwargs.pop('energy_export', None)
        energy_export_max_power = kwargs.pop('max_energy_export_allowed_rated_power_from_apower', None)

        grid_list = [grid_charging, grid_max_power, energy_export, energy_export_max_power]

        result_list = [i is not None for i in grid_list]

        if any(result_list):

            self.go_to_commission_utility_plan_and_incentives_grid_charge_export_page()

            if grid_charging is not None:

                if grid_charging == 'yes':

                    locator_name = "locator_utility_plan_grid_charging_yes"

                elif grid_charging == 'no':

                    locator_name = "locator_utility_plan_grid_charging_no"

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

            if grid_max_power is not None:

                locator_name = "locator_utility_plan_grid_charging_allowed_rated_power"

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, first_click=True, clear=True, send_keys=grid_max_power)

            if energy_export is not None:

                if energy_export == 'only_solar':

                    locator_name = "locator_utility_plan_energy_export_only_solar"

                elif energy_export == 'solar+apower':

                    locator_name = "locator_utility_plan_energy_export_solar_apower"

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True, auto_scroll=True, timeout=2)

            if energy_export_max_power is not None:

                locator_name = "locator_utility_plan_energy_export_allowed_rated_power"

                self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, first_click=True, clear=True, timeout=2, send_keys=energy_export_max_power, auto_scroll=True)

            locator_name = "locator_system_general_confirm"

            self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, click=True, auto_scroll=True, timeout=2)

            self.exit_commission_utility_plan_and_incentives_grid_charge_export_page()
