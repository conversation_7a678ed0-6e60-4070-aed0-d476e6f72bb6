from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common

# 调试模式-单设备状态

cmd_type_c2s = 1835

cmd_type_s2c = 1836

attr_map_c2s = {

    **attr_map_common_opt,

    'id': ('', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

    'ibgMainState': ('device_state', 'int'),
    'ibgDspState': ('dsp_state', 'int'),
    'peState': ('pe_state', 'int'),
    'bmsState': ('bms_state', 'int'),


}

com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalDebugSingleDevice(Base):

    def local_debug_single_device_state_get(self, *args, **kwargs):
        """  local debug single device state get-T1835
        Args：
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：ID不存在 |
            | id | int | 设备ID |
            | device_state | int | IBG状态,0：初始态，启动时的过度态 1：待机状态 2：自发自用模式 3：TOU平衡 4：TOU经济 5：并网状态下的仅备电状态 6：风暴等待模式 7：离网，电池备电中 8：离网，发电机备电中 9：vpp模式 10：紧急停机 11：故障进入安全模式 12：电池维护 13：调试 14：发电机备电演习测试模式 15：电池备电演习测试模式 16：升级状态 17：停机维护模式 18：禁止进入电网 19：PCS运行检测 20：BB&NEM 21：BB&CSS 22：BB&CGS+ |
            | dsp_state | int | DSP状态，0：从DSP初始化 1：获取参数 2：监听 3：待机 4：市电并网运行 5：离网运行 6：离网油机接入运行 7：故障 |
            | pe_state | int | PE状态，0：初始化 1：参数初始化 2：监听 3：待机 4：预充检测 5：预充 6：启机延时 7：逆变继电器吸合 8：运行 9：故障 |
            | bms_state | int | BMS状态，格式例子：[0, 0, 0, 3], 1=初始化；2=自检；3=待机；4=预充；5=运行；6=充电；7=放电；8=充电保护；9=放电保护；10=充放电保护；11=故障；12=异常充电准备；13=异常充电；14=异常充电停止；15=工装模式；16=补电模式；17=补电故障 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | id | int | 设备ID |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Debug Single Device State Get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_debug_single_device_version_get_result_check(self, _response, **kwargs):
        """  local debug single device state get result check-T1836

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | opt | int | 操作，0:查询 |
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：ID不存在 |
            | id | int | 设备ID |
            | device_state | int | IBG状态,0：初始态，启动时的过度态 1：待机状态 2：自发自用模式 3：TOU平衡 4：TOU经济 5：并网状态下的仅备电状态 6：风暴等待模式 7：离网，电池备电中 8：离网，发电机备电中 9：vpp模式 10：紧急停机 11：故障进入安全模式 12：电池维护 13：调试 14：发电机备电演习测试模式 15：电池备电演习测试模式 16：升级状态 17：停机维护模式 18：禁止进入电网 19：PCS运行检测 20：BB&NEM 21：BB&CSS 22：BB&CGS+ |
            | dsp_state | int | DSP状态，0：从DSP初始化 1：获取参数 2：监听 3：待机 4：市电并网运行 5：离网运行 6：离网油机接入运行 7：故障 |
            | pe_state | int | PE状态，0：初始化 1：参数初始化 2：监听 3：待机 4：预充检测 5：预充 6：启机延时 7：逆变继电器吸合 8：运行 9：故障 |
            | bms_state | int | BMS状态，格式例子：[0, 0, 0, 3], 1=初始化；2=自检；3=待机；4=预充；5=运行；6=充电；7=放电；8=充电保护；9=放电保护；10=充放电保护；11=故障；12=异常充电准备；13=异常充电；14=异常充电停止；15=工装模式；16=补电模式；17=补电故障 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Debug Single Device State Get |
        | ${status} = | Local Debug Single Device State Get | ${response} | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
