from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# 点表响应设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 375

cmd_type_c2s = 376

common_dict = {

    'id': ('', 'int'),
}

attr_map_s2c = {

    **common_dict,

    'status': ('', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

    'data': ('data_list', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class DataTableResponseSet(object):

    def data_table_response_set(self, *args, **kwargs):
        """  data table response set-T375/376

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
        Kwargs:
            | id | int | 点表配置表的唯一标识 |
            | status | int | 控制执行状态 | 0：运行，1：取消 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Response Set | id=3 | status=1 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def data_table_response_set_result_check(self, _response, **kwargs):
        """  data table response set result check-T376

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Data Table Response Set  | id=17 |
        | ${status} = | Data Table Response Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def data_table_response_set_from_aws_get(self, *args, **kwargs):
        """  data table response set from AWS,the message get-T375

        Args：
            | id | int | 点表配置表的唯一标识 |
            | status | int | 控制执行状态 | 0：运行，1：取消 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Response Set From AWS Get | status |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_response_set_from_aws_get_check(self, **kwargs):
        """  data table response set from AWS,the message get check-T375

        Kwargs:
            | id | int | 点表配置表的唯一标识 |
            | status | int | 控制执行状态 | 0：运行，1：取消 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Data Table Response Set From AWS Get Check | status=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_response_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_response_set_from_device_get(self, *args, **kwargs):
        """   data table response set response from device,the message get-T376

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Data Table Response Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def data_table_response_set_from_device_get_check(self, **kwargs):
        """  data table response set response from device,the message get check-T376

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Data Table Response Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.data_table_response_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def data_table_response_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T375/376

        Args for T375:
            | id | int | 点表配置表的唯一标识 |
            | status | int | 控制执行状态 | 0：运行，1：取消 |
        Args for T376：
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 375/376 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=375 | rx_time_window=300 |
        | ${status} | Data Table Response Set From Msg Get | status | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_response_set_from_device_get, cmd_type_s2c: self.data_table_response_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def data_table_response_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T375/376

        Kwargs for T375:
            | id | int | 点表配置表的唯一标识 |
            | status | int | 控制执行状态 | 0：运行，1：取消 |
        Kwargs for T376：
            | result | int | 结果，0:成功，1:失败 |
            | id | int | 点表配置表的唯一标识 |
            | data_list | list | 需要上送的点表数据 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 375/376 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=376 | rx_time_window=300 | filter_mode=and |
        | ${status} | Data Table Response Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.data_table_response_set_from_device_get_check, cmd_type_s2c: self.data_table_response_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
