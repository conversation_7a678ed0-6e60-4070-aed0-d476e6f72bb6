from j<PERSON><PERSON> import <PERSON>
from time import sleep

URL = 'http://192.168.0.66:8080'    # Jenkins server

USERNAME = "git"

PASSWORD = "git"


class Job(object):

    def __init__(self, url="http://192.168.0.66:8080", username="git", password="git"):

        self.server = <PERSON>(url, username=username, password=password)

    def _fetch_all_builds_from_job(self, job=None):

        return self.server.get_job_info(job, fetch_all_builds=True)

    def get_job_build_info(self, job=None):

        builds = self._fetch_all_builds_from_job(job=job)

        build_list = builds['builds']

        print(f'{job} has {len(build_list)} builds now!')

        for i in build_list:

            build_number = i['number']

            test_result = self.server.get_build_info(job, build_number)

            print(f"""the build_number:{build_number},"""
                  f"""its test result:{test_result['result']}""")

    def delete_job_build(self, job=None, build_number=None, waiting_time=4):

        self.server.delete_build(job, build_number)

        sleep(waiting_time)


JOB_NAME = "通用测试/OTA/HPM/V10R82B11/整包升级/出厂版本测试/aGate1.3/V10R10B13D00/aPower1.0+1.3+2.0+S/版本组合3/离网"

jobs = Job(url=URL, username=USERNAME, password=PASSWORD)

# jobs.get_job_build_info(job=JOB_NAME)


JOB_MIN = 1
JOB_MAX = 18

for i in range(JOB_MAX, JOB_MIN-1, -1):

    try:

        jobs.delete_job_build(job=JOB_NAME, build_number=i)

        print(f'ok,successfully deleted job:{i}')

    except Exception:

        pass
