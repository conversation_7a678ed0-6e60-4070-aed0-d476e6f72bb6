from Mqtt.util.base import Base
from Mqtt.util.util import build_local_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common

# 设备编址

cmd_type_c2s = 1103

cmd_type_s2c = 1104

attr_map_c2s = {

    'devNum': ('device_number', 'int'),

}

attr_map_s2c = {

    **attr_map_common,

    **attr_map_c2s,

}


com_dict_c2s, com_dict_s2c = build_local_s2c_c2s_dict(cmd_type_c2s, attr_map_c2s, cmd_type_s2c, attr_map_s2c)


class LocalAddressing(Base):

    def local_addressing_set(self, *args, **kwargs):
        """  local addressing set-T1103
        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
        Kwargs:
            | device_number | int | 识别到的最大aPower设备数量,最大15，0：自动编址 |
            | ret_type | string enum | 'auto'(by default) or 'list' | 库控制参数 |
            | ret_format | string enum | list(by default) or dict | 库控制参数 |
            | rx_time_window | int | 300(by default),等待接收MQTT消息的时间（秒) | 库控制参数 |
        Return:
            | string | args里只有一个参数且ret_format=list时返回 |
            | list   | args里有多于一个参数且ret_format=list时返回 |
            | dict   | args里没有参数或者ret_format=dict时返回 |
        Examples：
        | ${status} = | Local Addressing Set | device_number |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        kwargs.update(attr_map_rx=attr_map_c2s, c2s_cmd_type=int(cmd_type_c2s))

        build_tx_dict(kwargs, attr_map_c2s, com_dict_s2c, com_dict_s2c)

        return self._local_get(*args, **kwargs)

    def local_addressing_set_result_check(self, _response, **kwargs):
        """  local addressing set result check-T1104

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 0：操作成功；1：IBG已在编址中；2：设置数量过大；3：设置数量为0 |
            | device_number | int | 识别到的最大aPower设备数量,最大15 |
            | raise_error | true(by default):测试结果不匹配时抛异常, false:测试结果不匹配时禁止抛异常 | 库控制参数 |
        Return:
            | bool | True:测试结果匹配, False:在raise_error=False条件下测试结果不匹配 |
        Examples：
        | ${response} = | Local Addressing Set | device_number |
        | ${status} = | Local Addressing Set Result Check | ${response} | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_s2c

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)
