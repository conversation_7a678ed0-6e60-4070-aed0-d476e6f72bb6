from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name

# 系统资源监控数据上传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = None

cmd_type_c2s = 220

cmd_type_s2c = -1

attr_map_s2c = {}

attr_map_c2s = {

    'SysCpuUsage': ('sys_cpu_usage', 'float'),
    'SysCpuLoadRate': ('sys_cpu_load', 'float'),
    'SysCpuTemp': ('sys_cpu_temp', 'float'),
    'SysRamUsage': ('sys_memory_usage', 'float'),
    'SysDiskUsage': ('sys_disk_usage', 'float'),
    'SysRunTime': ('sys_run_time', 'string'),

    'IbgMainCpuUsage': ('ibg_main_cpu_usage', 'float'),
    'IbgMainRamUsage': ('ibg_main_memory_usage', 'float'),
    'IbgMainRunTime': ('ibg_main_run_time', 'string'),
    'IbgAwsCpuUsage': ('ibg_aws_cpu_usage', 'float'),
    'IbgAwsRamUsage': ('ibg_aws_memory_usage', 'float'),
    'IbgAwsRunTime': ('ibg_aws_run_time', 'string'),
    'IbgAppCpuUsage': ('ibg_app_cpu_usage', 'float'),
    'IbgAppRamUsage': ('ibg_app_memory_usage', 'float'),
    'IbgAppRunTime': ('ibg_app_run_time', 'string'),
    'LogFileSize': ('log_file_size', 'float'),
    'MysqlDbSize': ('mysql_db_size', 'float'),

    'date': ('', 'string'),
    'nsc_main': ('', 'list'),
    'nsc_aws': ('', 'list'),
    'ProtocolServer': ('', 'list'),
    'ibgslavedsp': ('', 'list'),
    'nsc_iqload': ('', 'list'),
    'AlarmServer': ('', 'list'),
    'NETWORK_MGMT': ('', 'list'),
    'MPU_RES_MGMT': ('', 'list'),
}

"""
'Status': ('', 'string'),
'CpuUsage': ('cpu_usage', 'float'),
'RamUsage': ('memory_usage', 'float'),
'RunTime': ('run_time', 'int'),
"""

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class ResourceMonitorFileUpload(object):

    def sys_resource_monitor_file_from_device_get(self, *args, **kwargs):
        """  system resource monitor file from device,the message get-T220

        Args：
            | sys_cpu_usage | float | CPU利用率，单位：% |
            | sys_cpu_load | float |  CPU负载，单位：% |
            | sys_cpu_temp | float |   CPU温度，单位：摄氏度 |
            | sys_memory_usage | float | 内存利用率，单位：% |
            | sys_disk_usage | float | 磁盘利用率，单位：% |
            | sys_run_time | string | 系统累计运行时间，单位：秒 |
            | ibg_main_cpu_usage |  float | IBG main进程CPU利用率，单位：% |
            | ibg_main_memory_usage |  float | IBG main进程内存利用率，单位：% |
            | ibg_main_run_time | string |  IBG main进程累计运行时间，单位：秒 |
            | ibg_aws_cpu_usage |  float | IBG AWS进程CPU利用率，单位：% |
            | ibg_aws_memory_usage |  float | IBG AWS进程内存利用率，单位：% |
            | ibg_aws_run_time | string | IBG AWS进程累计运行时间，单位：秒 |
            | ibg_app_cpu_usage |  float | IBG APP进程CPU利用率，单位：% |
            | ibg_app_memory_usage |  float | IBG APP进程内存利用率，单位：% |
            | ibg_app_run_time | string | IBG APP进程累计运行时间，单位：秒 |
            | log_file_size |  float | 日志占用大小，单位：MB |
            | mysql_db_size |  float | 数据库DB占用大小，单位：MB |
            | date | string | 日期 |
            | nsc_main | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_aws | list | nsc_aws进程信息:Status/CpuUsage/RamUsage/RunTime  |
            | ProtocolServer | list | ProtocolServer进程信息:Status/CpuUsage/RamUsage/RunTime |
            | ibgslavedsp | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_iqload | list | ibgslavedsp进程信息:Status/CpuUsage/RamUsage/RunTime |
            | AlarmServer | list | nsc_iqload进程信息:Status/CpuUsage/RamUsage/RunTime |
            | NETWORK_MGMT | list | NETWORK_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
            | MPU_RES_MGMT | list | MPU_RES_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Sys Resource Monitor File From Device Get | sys_cpu_usage |date |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def sys_resource_monitor_file_from_device_get_check(self, **kwargs):
        """   system resource monitor file  from device,the message get check-T220

        Kwargs:
            | sys_cpu_usage | float | CPU利用率，单位：% |
            | sys_cpu_load | float |  CPU负载，单位：% |
            | sys_cpu_temp | float |   CPU温度，单位：摄氏度 |
            | sys_memory_usage | float | 内存利用率，单位：% |
            | sys_disk_usage | float | 磁盘利用率，单位：% |
            | sys_run_time | string | 系统累计运行时间，单位：秒 |
            | ibg_main_cpu_usage |  float | IBG main进程CPU利用率，单位：% |
            | ibg_main_memory_usage |  float | IBG main进程内存利用率，单位：% |
            | ibg_main_run_time | string |  IBG main进程累计运行时间，单位：秒 |
            | ibg_aws_cpu_usage |  float | IBG AWS进程CPU利用率，单位：% |
            | ibg_aws_memory_usage |  float | IBG AWS进程内存利用率，单位：% |
            | ibg_aws_run_time | string | IBG AWS进程累计运行时间，单位：秒 |
            | ibg_app_cpu_usage |  float | IBG APP进程CPU利用率，单位：% |
            | ibg_app_memory_usage |  float | IBG APP进程内存利用率，单位：% |
            | ibg_app_run_time | string | IBG APP进程累计运行时间，单位：秒 |
            | log_file_size |  float | 日志占用大小，单位：MB |
            | mysql_db_size |  float | 数据库DB占用大小，单位：MB |
            | date | string | 日期 |
            | nsc_main | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_aws | list | nsc_aws进程信息:Status/CpuUsage/RamUsage/RunTime  |
            | ProtocolServer | list | ProtocolServer进程信息:Status/CpuUsage/RamUsage/RunTime |
            | ibgslavedsp | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_iqload | list | ibgslavedsp进程信息:Status/CpuUsage/RamUsage/RunTime |
            | AlarmServer | list | nsc_iqload进程信息:Status/CpuUsage/RamUsage/RunTime |
            | NETWORK_MGMT | list | NETWORK_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
            | MPU_RES_MGMT | list | MPU_RES_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Sys Resource Monitor File From Device Get Check | sys_cpu_usage=12 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.sys_resource_monitor_file_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def sys_resource_monitor_file_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T220

        Args for T220：
            | sys_cpu_usage | float | CPU利用率，单位：% |
            | sys_cpu_load | float |  CPU负载，单位：% |
            | sys_cpu_temp | float |   CPU温度，单位：摄氏度 |
            | sys_memory_usage | float | 内存利用率，单位：% |
            | sys_disk_usage | float | 磁盘利用率，单位：% |
            | sys_run_time | string | 系统累计运行时间，单位：秒 |
            | ibg_main_cpu_usage |  float | IBG main进程CPU利用率，单位：% |
            | ibg_main_memory_usage |  float | IBG main进程内存利用率，单位：% |
            | ibg_main_run_time | string |  IBG main进程累计运行时间，单位：秒 |
            | ibg_aws_cpu_usage |  float | IBG AWS进程CPU利用率，单位：% |
            | ibg_aws_memory_usage |  float | IBG AWS进程内存利用率，单位：% |
            | ibg_aws_run_time | string | IBG AWS进程累计运行时间，单位：秒 |
            | ibg_app_cpu_usage |  float | IBG APP进程CPU利用率，单位：% |
            | ibg_app_memory_usage |  float | IBG APP进程内存利用率，单位：% |
            | ibg_app_run_time | string | IBG APP进程累计运行时间，单位：秒 |
            | log_file_size |  float | 日志占用大小，单位：MB |
            | mysql_db_size |  float | 数据库DB占用大小，单位：MB |
            | date | string | 日期 |
            | nsc_main | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_aws | list | nsc_aws进程信息:Status/CpuUsage/RamUsage/RunTime  |
            | ProtocolServer | list | ProtocolServer进程信息:Status/CpuUsage/RamUsage/RunTime |
            | ibgslavedsp | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_iqload | list | ibgslavedsp进程信息:Status/CpuUsage/RamUsage/RunTime |
            | AlarmServer | list | nsc_iqload进程信息:Status/CpuUsage/RamUsage/RunTime |
            | NETWORK_MGMT | list | NETWORK_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
            | MPU_RES_MGMT | list | MPU_RES_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 220 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=220 | rx_time_window=300 | filter_mode=and |
        | ${status} | Sys Resource Monitor File From Msg Get | date | msg=${packets} |
        | ${status} | RSys Resource Monitor File From Msg Get | nsc_aws | log_file_size | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=220 | rx_time_window=300 |
        | ${status} | Sys Resource Monitor File From Msg Get | sys_memory_usage | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sys_resource_monitor_file_from_device_get, cmd_type_s2c: {}}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def sys_resource_monitor_file_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T220

        Kwargs for T220：
            | sys_cpu_usage | float | CPU利用率，单位：% |
            | sys_cpu_load | float |  CPU负载，单位：% |
            | sys_cpu_temp | float |   CPU温度，单位：摄氏度 |
            | sys_memory_usage | float | 内存利用率，单位：% |
            | sys_disk_usage | float | 磁盘利用率，单位：% |
            | sys_run_time | string | 系统累计运行时间，单位：秒 |
            | ibg_main_cpu_usage |  float | IBG main进程CPU利用率，单位：% |
            | ibg_main_memory_usage |  float | IBG main进程内存利用率，单位：% |
            | ibg_main_run_time | string |  IBG main进程累计运行时间，单位：秒 |
            | ibg_aws_cpu_usage |  float | IBG AWS进程CPU利用率，单位：% |
            | ibg_aws_memory_usage |  float | IBG AWS进程内存利用率，单位：% |
            | ibg_aws_run_time | string | IBG AWS进程累计运行时间，单位：秒 |
            | ibg_app_cpu_usage |  float | IBG APP进程CPU利用率，单位：% |
            | ibg_app_memory_usage |  float | IBG APP进程内存利用率，单位：% |
            | ibg_app_run_time | string | IBG APP进程累计运行时间，单位：秒 |
            | log_file_size |  float | 日志占用大小，单位：MB |
            | mysql_db_size |  float | 数据库DB占用大小，单位：MB |
            | date | string | 日期 |
            | nsc_main | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_aws | list | nsc_aws进程信息:Status/CpuUsage/RamUsage/RunTime  |
            | ProtocolServer | list | ProtocolServer进程信息:Status/CpuUsage/RamUsage/RunTime |
            | ibgslavedsp | list | nsc_main进程信息:Status/CpuUsage/RamUsage/RunTime |
            | nsc_iqload | list | ibgslavedsp进程信息:Status/CpuUsage/RamUsage/RunTime |
            | AlarmServer | list | nsc_iqload进程信息:Status/CpuUsage/RamUsage/RunTime |
            | NETWORK_MGMT | list | NETWORK_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
            | MPU_RES_MGMT | list | MPU_RES_MGMT进程信息:Status/CpuUsage/RamUsage/RunTime |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 220 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=220 | rx_time_window=300 | filter_mode=and |
        | ${status} |Sys Resource Monitor File Get Check | sys_cpu_usage=10 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.sys_resource_monitor_file_from_device_get_check, cmd_type_s2c: {}}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
