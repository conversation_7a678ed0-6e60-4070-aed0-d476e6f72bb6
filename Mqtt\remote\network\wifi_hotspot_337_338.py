from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# WiFi 热点账号设置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 337

cmd_type_c2s = 338

attr_map_s2c = {

    **attr_map_common_opt,

    'wifi_SSID': ('wifi_SSID', 'string'),
    'wifi_Pw': ('wifi_password', 'string'),
    'ap_SSID': ('AP_SSID', 'string'),
    'ap_Pw': ('AP_password', 'string'),
    'wifi_Safety': ('wifi_safety', 'int'),

}

attr_map_c2s = {

    **attr_map_common_result,

    'reason': ('', 'int'),

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class WifiHotSpot(object):

    def wifi_hotspot_set(self, *args, **kwargs):
        """  WiFi hotspot set-T337/338

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | WiFi Hotspot Set | opt=0 |
        | ${status} = | WiFi Hotspot Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def wifi_hotspot_set_result_check(self, _response, **kwargs):
        """  WiFi set result check-T338

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | WiFi Hotspot Set | opt=0 |
        | ${status} = | WiFi Hotspot Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def wifi_hotspot_set_from_aws_get(self, *args, **kwargs):
        """  WiFi set from AWS,the message get-T337

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   WiFi Hotspot Set From AWS Get | wifi_SSID | wifi_password |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def wifi_hotspot_set_from_aws_get_check(self, **kwargs):
        """  WiFi set from AWS,the message get check-T337

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | WiFi Hotspot Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.wifi_hotspot_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def wifi_hotspot_set_from_device_get(self, *args, **kwargs):
        """   WiFi set response from device,the message get-T338

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | WiFi Hotspot Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def wifi_hotspot_set_from_device_get_check(self, **kwargs):
        """  WiFi set response from device,the message get check-T338

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  WiFi Hotspot Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.wifi_hotspot_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def wifi_hotspot_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T337/338

        Args for T337:
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Args for T338：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 337/338 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=337 | rx_time_window=300 |
        | ${status} | WiFi Hotspot Set From Msg Get | AP_SSID | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.wifi_hotspot_set_from_device_get, cmd_type_s2c: self.wifi_hotspot_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def wifi_hotspot_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T337/338

        Kwargs for T337:
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs for T338:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因,0：操作成功；1：wifi_SSID格式错误；2：wifi_Pw格式错误；3：ap_SSID格式错误；4：ap_Pw格式错误；5：参数读取失败；6：系统设置失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | wifi_SSID | string | 用户wifi路由器的SSID，最大长度：64字节 |
            | wifi_password | string | 用户wifi路由器的密码，最大长度：64字节 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~32字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~32字节 |
            | wifi_safety | int | 用户wifi路由器安全性，0：未加密，1：加密 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 337/338 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=338 | rx_time_window=300 | filter_mode=and |
        | ${status} | WiFi Hotspot Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.wifi_hotspot_set_from_device_get_check, cmd_type_s2c: self.wifi_hotspot_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
