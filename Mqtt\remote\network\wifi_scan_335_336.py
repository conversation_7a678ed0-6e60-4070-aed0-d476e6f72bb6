from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common

# WiFi 热点扫描

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 335

cmd_type_c2s = 336

attr_map_s2c = {

    'wifi_ScanTime': ('scan_time', 'int'),

}

attr_map_c2s = {

    **attr_map_common,

    'wifi_Info': ('SSID_info', 'list'),

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class WifiScan(object):

    def network_wifi_scan(self, *args, **kwargs):
        """  Wifi scan-T335/336

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
        Kwargs:
            | scan_time | int | wifi 扫描等待时间 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Wifi Scan |
        | ${status} = | Network Wifi Scan | result | scan_time=20 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('scan_time', 0)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def network_wifi_scan_result_check(self, _response, **kwargs):
        """  Wifi scan result check-T336

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | Network Wifi Scan | opt=1 | debug_mode_enabled=1 |
        | ${status} = | Network Wifi Scan Result Check | ${response} | result=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def network_wifi_scan_from_aws_get(self, *args, **kwargs):
        """  Wifi scan from AWS,the message get-T335

        Args：
            | scan_time | int | wifi 扫描等待时间 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Wifi Scan From AWS Get | scan_time |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def network_wifi_scan_from_aws_get_check(self, **kwargs):
        """  Wifi scan from AWS,the message get check-T335

        Kwargs:
            | scan_time | int | wifi 扫描等待时间 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Wifi Scan From AWS Get Check | scan_time=10 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_wifi_scan_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_wifi_scan_from_device_get(self, *args, **kwargs):
        """   Wifi scan response from device,the message get-T336

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Network Wifi Scan From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def network_wifi_scan_from_device_get_check(self, **kwargs):
        """  Wifi scan response from device,the message get check-T336

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | Network Wifi Scan From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.network_wifi_scan_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def network_wifi_scan_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T335/336

        Args for T335:
            | scan_time | int | wifi 扫描等待时间 |
        Args for T336：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 335/336 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=335 | rx_time_window=300 |
        | ${status} | Network Wifi Scan From Msg Get | scan_time | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_wifi_scan_from_device_get, cmd_type_s2c: self.network_wifi_scan_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def network_wifi_scan_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T335/336

        Kwargs for T335:
            | scan_time | int | wifi 扫描等待时间 |
        Kwargs for T336：
            | result | int | 结果，0:成功，1:失败 |
            | reason | int | 原因，0：操作成功；1：未扫描到可用热点信息；2：Wi-Fi网络处于关闭状态（要使用Wi-Fi功能，必须打开Wi-Fi功能）；3：扫描超时(aws未收到本地进程的回复 |
            | SSID_info | list | WIFI信息列表，包含SSID、RSSI和safey |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 335/336 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=336 | rx_time_window=300 | filter_mode=and |
        | ${status} | Network Wifi Scan From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.network_wifi_scan_from_device_get_check, cmd_type_s2c: self.network_wifi_scan_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
