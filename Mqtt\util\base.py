from robot.api import logger
from .util import (input_data_mapping, output_data_mapping, ret_value_processing, input_kwargs_processing, dict_compare, msg_parser)


class Base(object):

    def _get(self, *args, **kwargs):
        """ general get operation"""

        logger.debug(f"the _get parameter:\nargs:{args}\nkwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        tx_dict = kwargs.pop('tx_dict', {})

        rx_dict = kwargs.pop('rx_filter_dict', {})

        downstream = kwargs.pop('topic_s2c', None)

        topic = downstream or kwargs.pop('topic_c2s', None)

        s2c_cmdType = kwargs.pop('s2c_cmd_type', None)

        c2s_cmdType = kwargs.pop('c2s_cmd_type', None)

        if not downstream:

            s2c_cmdType, c2s_cmdType = c2s_cmdType, s2c_cmdType

        attr_map_rx = kwargs.pop('attr_map_rx', None)

        # for direct packet
        """ for Rx message to parser"""

        direct_packet_check = kwargs.pop('direct_packet_check', False)

        direct_packet = kwargs.pop('direct_packet', None)

        ctrl_para_list = ['rx_time_window', 'qos', 'retain', 'properties']

        if not direct_packet_check:

            contrl_dict = input_kwargs_processing(kwargs, ctrl_para_list)

            para_dict = dict(cmdType=s2c_cmdType, equipNo=self.client_id, **tx_dict)

            payload = self.pdu_build(**para_dict)

            resp = self.publish_and_receive(topic=f"{topic}", data=payload, mode='couple', expected_cmd_type=c2s_cmdType, filter_kwargs=rx_dict, **contrl_dict)

        else:
            resp = direct_packet

        if resp:

            if isinstance(resp, list):

                _resp = resp[0]

            else:

                _resp = resp

            if 'dataArea' in _resp:

                user_dict = output_data_mapping(_resp['dataArea'], attr_map_rx)

            else:

                raise ValueError(f'Sorry, no dataArea field is found in {_resp}')
        else:

            raise ValueError(f'Sorry, no response packets are found for the resp:{resp}!')

        return ret_value_processing(args, resp, user_dict=user_dict, ret_type=ret_type, ret_format=ret_format)

    def _local_get(self, *args, **kwargs):
        """ general get operation for local access"""

        logger.debug(f"the _local_get parameter:\nargs:{args}\nkwargs:{kwargs}")

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        tx_dict = kwargs.pop('tx_dict', {})

        s2c_cmdType = kwargs.pop('s2c_cmd_type', None)

        c2s_cmdType = kwargs.pop('c2s_cmd_type', None)

        topic_s2c = kwargs.pop('topic_s2c', None)

        topic_c2s = kwargs.pop('topic_c2s', None)

        attr_map_rx = kwargs.pop('attr_map_rx', None)

        # for direct packet
        """ for Rx message to parser"""

        direct_packet_check = kwargs.pop('direct_packet_check', False)

        direct_packet = kwargs.pop('direct_packet', None)

        ctrl_para_list = ['rx_time_window', 'qos', 'retain', 'properties']

        if not direct_packet_check:

            resp = self.build_local_packet_to_send(cmdtype=c2s_cmdType, payload=tx_dict, snno='1')

        else:
            resp = direct_packet

        if resp:

            if 'dataArea' in resp:

                if attr_map_rx:

                    user_dict = output_data_mapping(resp['dataArea'], attr_map_rx)
            else:

                raise ValueError(f'Sorry, no dataArea field is found in {resp}')
        else:

            raise ValueError(f'Sorry, no response packets are found for the resp:{resp}!')

        return ret_value_processing(args, resp, user_dict=user_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_check(self, **kwargs):
        """ general get check operation"""

        logger.debug(f"the _get_check parameter kwargs:{kwargs}")

        raise_error = kwargs.pop('raise_error', True)

        raise_error = False if raise_error in [0, '0', 'false', 'False'] else raise_error

        func = kwargs.pop('func_get', None)

        # for direct packet
        """ for Rx message to parser"""

        direct_packet = kwargs.pop('direct_packet', False)

        direct_packet_check = kwargs.pop('direct_packet_check', False)

        ctrl_para_list = ['rx_time_window', 'subscribe_time_window']

        contrl_dict = input_kwargs_processing(kwargs, ctrl_para_list)

        contrl_dict.update(ret_type='list')

        args = list(kwargs)

        if direct_packet:

            contrl_dict['direct_packet'] = direct_packet

            contrl_dict['direct_packet_check'] = direct_packet_check

        if not args:

            raise ValueError(f"sorry, no expected key values are given to get check!")

        resp = func(*args, **contrl_dict)

        resp_dict = dict(zip(args, resp))

        result = (resp_dict == kwargs)

        if result:

            logger.info(f"OK!Found the matched value(s) in response:{resp_dict} for the expected values:{kwargs}")

        else:

            dict_compare(resp_dict, kwargs, raise_error=raise_error)

        return result

    def _set(self, **kwargs):
        """ general set operation"""

        logger.debug(f"the _set parameter kwargs:{kwargs}")

        tx_dict = kwargs.pop('tx_dict', {})

        attr_map = kwargs.pop('attr_map', None)

        topic_s2c = kwargs.pop('topic_s2c', None)

        topic_c2s = kwargs.pop('topic_c2s', None)

        s2c_cmdType = kwargs.pop('s2c_cmd_type', None)

        c2s_cmdType = kwargs.pop('c2s_cmd_type', None)

        ctrl_para_list = ['rx_time_window', 'qos', 'retain', 'properties']

        contrl_dict = input_kwargs_processing(kwargs, ctrl_para_list)

        user_dict = {**input_data_mapping(kwargs, attr_map), **tx_dict}

        logger.info(f"the user dict is:{user_dict}")

        payload = self.pdu_build(cmdType=s2c_cmdType, equipNo=self.client_id, **user_dict)

        logger.info(f"the payload is:{payload}")

        return self.publish_and_receive(topic=f"{topic_s2c}", data=payload, expected_cmd_type=c2s_cmdType, mode='couple', **contrl_dict)

    def _set_check(self, **kwargs):
        """ general set check operation"""

        logger.debug(f"the _set_check parameter kwargs:{kwargs}")

        func_set = kwargs.pop('func_set', None)

        func_get_check = kwargs.pop('func_get_check', None)

        func_set(**kwargs)

        func_get_check(**kwargs)

    def _get_expected_msgs(self, *args, **kwargs):
        """ general get the given mesg to process for monitoring msgs between device and aws cloud"""

        logger.debug(f"the _get_expected_msg parameter:\nargs:{args}\nkwargs:{kwargs}")

        kwargs.setdefault('rx_time_window', 300)

        ret_type = kwargs.pop('ret_type', 'auto')

        ret_format = kwargs.pop('ret_format', 'list')

        expected_cmd_type = kwargs.pop('expected_cmd_type', None)

        attr_map = kwargs.pop('attr_map', None)

        ctrl_para_list = ['rx_time_window']

        # for direct packet
        """ for Rx message to parser"""

        direct_packet_check = kwargs.pop('direct_packet_check', False)

        if direct_packet_check:

            resp = kwargs.pop('direct_packet', None)

        else:

            contrl_dict = input_kwargs_processing(kwargs, ctrl_para_list)

            para_dict = dict(cmdType=expected_cmd_type, equipNo=self.client_id)

            payload = self.pdu_build(**para_dict)

            resp = self.receive(expected_cmd_type=expected_cmd_type, **contrl_dict)

        user_dict = output_data_mapping(resp['dataArea'], attr_map)

        return ret_value_processing(args, resp, user_dict=user_dict, ret_type=ret_type, ret_format=ret_format)

    def _get_from_msg(self, func_pair, msg, cmd_type, *args, **kwargs):

        kwargs.update({'funcs': func_pair, 'direct_packet_check': True})

        return msg_parser(*args, msg=msg, cmd_type=cmd_type, **kwargs)

    def _get_check_from_msg(self, func_pair, msg, cmd_type, **kwargs):

        args = tuple(kwargs.keys())

        kwargs.update({'funcs': func_pair, 'direct_packet_check': True, 'get_check_mode': True})

        return msg_parser(*args, msg=msg, cmd_type=cmd_type, **kwargs)

    def _check_dict_response(self, msg, mapping_dict=None, expected_dict=None):

        logger.info(f'the user parameters: msg:{msg},mapping_dict:{mapping_dict},expected_dict:{expected_dict}')

        _exp_dict = input_data_mapping(expected_dict, mapping_dict)

        target_dict = msg['dataArea']

        for k, v in _exp_dict.items():

            if k in target_dict:

                if v != target_dict[k]:

                    raise ValueError(f'FAIL to find {k}:{v} in {msg}')

            else:

                raise ValueError(f'FAIL to find {k} in {msg}')
        logger.info(f'OK,found the wonderful match for {expected_dict} in {msg}!')
