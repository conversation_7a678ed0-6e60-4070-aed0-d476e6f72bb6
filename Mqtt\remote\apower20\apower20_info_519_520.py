from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# aPower2.0信息召唤

app_name = "device"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 519

cmd_type_c2s = 520

attr_map_s2c = {

    'update': ('', 'int'),

}

attr_map_c2s = {

    'ratedPwr': ('rated_power', 'int'),
    'ratedPwrBuf': ('rated_power_list', 'list'),
    'rateBatCap': ('rated_battery_list', 'list'),

}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class APower20Info(object):

    def apower20_info_get(self, *args, **kwargs):
        """  apower20  info get-T519/520

        Args:
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Kwargs:
            | update | int | 1 :召唤apower2.0相关信息 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${result} | apower20 info get |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('update', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def apower20_info_get_check(self, *args, **kwargs):
        """  apower20 info get check-T519/520

        Args：
            | None |
        Kwargs:
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | apower20 info get Check | rated_power=0 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        func_get = self.apower20_info_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apower20_info_get_from_aws_get(self, *args, **kwargs):
        """  apower20 info get from AWS,the message get-T519

        Args：
            | update | int | 1 :召唤apower2.0相关信息 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | apower20 info get From AWS Get | update |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def apower20_info_get_from_aws_get_check(self, **kwargs):
        """  apower20 info get get from AWS,the message get check-T519

        Kwargs:
            | update | int | 1 :召唤apower2.0相关信息 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | apower20 info get From AWS Get Check | update=1 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.apower20_info_get_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apower20_info_get_from_device_get(self, *args, **kwargs):
        """   apower20 info get response from device,the message get-T520

        Args:
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | apower20 info get From Device Get | rated_power |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def apower20_info_get_from_device_get_check(self, **kwargs):
        """  apower20 info get response from device,the message get check-T520

        Kwargs:
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | apower20 info get From Device Get Check | rated_power=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.apower20_info_get_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def apower20_info_get_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T519/520

        Args for T519:
            | update | int | 1 :召唤apower2.0相关信息 |
        Args for T520：
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 519/520 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=519 | rx_time_window=300 |
        | ${status} | apower20 info get From Msg Get | update | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apower20_info_get_from_device_get, cmd_type_s2c: self.apower20_info_get_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def apower20_info_get_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T519/520

        Args for T519:
            | update | int | 1 :召唤apower2.0相关信息 |
        Kwargs for T520：
            | rated_power | int | 额定功率，0:默认(aPower1.x为5kw，aPower2.x为10kw) 1:2.5kw(铭牌2.9KVA);2:5.0kw(铭牌5.8KVA) 3:6.7kw(铭牌7.7KVA) 5:8.4kw(铭牌9.6KVA) 4:10kw(铭牌11.5KVA) |
            | rated_power_list | list | 额定功率数组,单位：w，10K额定功率上送为10000 |
            | rated_battery_list | list | 额定电池容量，单位：wh，13.6kwh上送为13600 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 519/520 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=520 | rx_time_window=300 | filter_mode=and |
        | ${status} | apower20  info get From Msg Get Check | rated_power=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.apower20_info_get_from_device_get_check, cmd_type_s2c: self.apower20_info_get_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
