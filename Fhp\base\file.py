import difflib

from robot.api import logger


class File(object):

    def file_compare(self, src, dst, compare_file=False, output='increment'):

        logger.info(f'the user parameters: scr:{src},dst:{dst},compare_file:{compare_file}, output:{output}')

        if compare_file:

            with open(src, 'r') as file1:

                file1_text = file1.readlines()

            with open(dst, 'r') as file2:

                file2_text = file2.readlines()
        else:

            file1_text = src

            file2_text = dst

        if compare_file:

            diff = difflib.Differ().compare(file1_text, file2_text)

        else:

            diff = difflib.unified_diff(file1_text.splitlines(), file2_text.splitlines())

        result = '\n'.join(list(diff))

        logger.info(f'the result:{result}')

        if output == 'increment':

            ret_context = list()

            tmp = result.split('\n')

            for i in tmp:

                if i.startswith('+'):

                    ret_context.append(i)

            logger.debug(f'the return content for {output}:{ret_context}')

            return ret_context

        logger.debug(f'the return content:{result}')

        return result
