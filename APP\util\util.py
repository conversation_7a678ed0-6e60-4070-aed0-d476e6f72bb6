import subprocess
from robot.api import logger


def escape_xpath_value(value):

    if '"' in value and '\'' in value:

        parts_wo_apos = value.split('\'')

        return "concat('%s')" % "', \"'\", '".join(parts_wo_apos)

    if '\'' in value:

        return "\"{value}\""

    return f"'{value}'"


def log_as_html(message):

    logger.info(message, True, False)


def ret_value_processing(args, resp=None, user_dict=None, ret_type='auto', ret_format='list'):

    logger.debug(f"the args:{args}")

    logger.debug(f"the user_dict:{user_dict}")

    ret_list = list()

    ret_dict = dict()

    if args:

        for i in args:

            if ret_format == 'list':

                ret_list.append(user_dict[i])

            else:
                try:
                    ret_dict.update({i: user_dict[i]})

                except KeyError as e:
                    logger.info(f'Fail to find the key: {i} in {user_dict}!')

                    raise e

        if ret_format == 'list':

            if ret_type == 'auto':

                if len(ret_list) == 1:

                    return ret_list[0]

            return ret_list

        else:

            return ret_dict
    else:
        resp = user_dict
        return resp


def reverse_dict(_dict=None):

    return {v: k for k, v in _dict.items()}


def os_command(cmd, **kwargs):

    result = subprocess.Popen(cmd, stdout=subprocess.PIPE, stdin=subprocess.PIPE, stderr=subprocess.PIPE)

    out, err = result.communicate()

    res = out.decode('utf-8')

    if res:

        logger.info(f'The current output is:{res}')

    if err:

        logger.info(f'Sorry, found some error output:{err}')

    return res, err
