from pathlib import Path
import json
import socket
import configparser
import paho.mqtt.client as mqtt
import paho.mqtt.subscribe as subscribe
from time import (sleep, time)
from copy import deepcopy
from paho.mqtt.enums import CallbackAPIVersion
from .pdu.pdu import (Pdu, LocalPdu, Msg_parser, MSG_LIST, pkt_sn_generate)
from .constant import (CA_FILE, CERT_FILE, KEY_FILE, PORT)
from robot.api import logger
from .remote import Remote
from .locals import Locals


class Protocol(Remote, Locals):

    def __init__(self, *args, **kwargs):

        self.pkd_func = pkt_sn_generate()

    def connect_server(self, mode='remote', ip="a6f63crce3ufk-ats.iot.us-west-2.amazonaws.com", port=PORT, protocol=4, keepalive=60, client_id='', clean_session=False, username="admin", password="password", auth_mode="cert", cert_server="test_server", cert_file_enabled=True, cafile=None, certfile=None, keyfile=None, debug=0):
        """  Connect to the remote MQTT broker

        Kwargs：
            | mode |  string | remote(by default)/local | remote:MQTT over AWS, local: local socket to EMS |
            | ip |  string | IPv4(**********,by default,when mode='local') or IPv6 address or host name(a6f63crce3ufk-ats.iot.us-west-2.amazonaws.com,by default,when mode='remote') |
            | port | int | 0~65535 | 8883(by default when mode='remote'),9000(by default when mode='local') |
            | protocol | int enum | 3/4(by default)/5 | 3:MQTTv31,4:MQTTv311,5:MQTTv5 |
            | keepalive | int | maximum period in seconds between communications with the broker,60(by default) |
            | client_id | string | EMS ID,''(by default,it will be generated automatically by the test library,in such case,the parameter 'clean_session' should be True) |
            | clean_session | bool | False(by default),NA when protocol=5 |
            | username | string | admin(by default) |
            | password | string | password(by default) |
            | auth_mode | string | cert(by default)/plain |
            | cert_file_enabled | bool |  True, use the defined cert file to read, False(by default) |
            | cert_server | string | the cert server name for different AWS server,which is indicated in cert file |
            | cafile | string | CA file path,filled by AWS setting by default |
            | certfile | string | CERT file path,filled by AWS setting by default |
            | keyfile | string | key file path,filled by AWS setting by default |
            | debug | int | 1(enabled)/0(disable,by default) PAHO library output debug |
        Return:
            | session | the MQTT client instance |
        Examples：
        | ${session} = | Connect Server | client_id=aGate_8888 |
        """
        logger.info(f"the user parameter:mode:{mode},ip:{ip},port:{port},"
                    f"protocol:{protocol},keepalive:{keepalive},client_id:{client_id},"
                    f"clean_session:{clean_session},username:{username},password:{password},"
                    f"auth_mode:{auth_mode},username:{username},password:{password},"
                    f"cert_server:{cert_server},cert_file_enabled:{cert_file_enabled},"
                    f"cafile:{cafile},certfile:{certfile},keyfile:{keyfile},debug:{debug}"
                    )

        if cert_file_enabled:

            cafile, certfile, keyfile = self._get_cert_files(cert_server)

        else:

            cafile = CA_FILE if cafile is None else cafile

            certfile = CERT_FILE if certfile is None else certfile

            keyfile = KEY_FILE if keyfile is None else keyfile

        if cert_server == "device_test_server":

            ip = "a6f63crce3ufk-ats.iot.us-east-1.amazonaws.com"

        self.mode = mode

        logger.info(f"the current ip:{ip}")

        if mode == 'local':

            self.pkd_func_local = pkt_sn_generate()

            ip = '**********'

            port = 9000

            self.client_id = client_id

            logger.info(f"user parameters:mode:{mode},ip:{ip},port:{port}")

            self.client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

            self.client.connect((ip, port))

        else:

            logger.info(f"user parameters:mode:{mode},ip:{ip},port:{port}")

            logger.debug(f"The current CA file path is {cafile}")

            self.ip = ip

            self.port = int(port)

            self.callback_api_version = CallbackAPIVersion.VERSION2

            self.protocol = int(protocol)

            self.keepalive = int(keepalive)

            self.clean_session = clean_session

            self.client_id = client_id

            self.debug = debug

            ses_dict = dict(callback_api_version=self.callback_api_version, protocol=self.protocol, transport='tcp', manual_ack=False, client_id=self.client_id)

            if self.protocol != 5:

                ses_dict.update(clean_session=clean_session)

            logger.debug(f"the session dict is :{ses_dict}")

            self.client = mqtt.Client(**ses_dict)

            self.client.suppress_exceptions = False

            self.client.on_message = self._on_message

            self.client.on_log = self._on_log

            self.client.on_connect = self._on_connect

            self.client.on_subscribe = self._on_subscribe

            self.client.on_unsubscribe = self._on_unsubscribe

            self.client.on_publish = self._on_publish

            if auth_mode == "cert":

                self.cafile = cafile
                self.certfile = certfile
                self.keyfile = keyfile
                self.client.tls_set(self.cafile, self.certfile, self.keyfile)

            else:
                self.client.username_pw_set(username, password)

            self.client.connect(host=self.ip, port=self.port, keepalive=self.keepalive)

        logger.info(f'the client session:{self.client} is created!')

        return self.client

    def _get_cert_files(self, cert_server):

        conf = configparser.ConfigParser()

        paths = ["cert", "config.ini"]

        _cert_file_path = Path(__file__).parent.joinpath(*paths)

        logger.info(f'the cert file path:{_cert_file_path}')

        conf.read(_cert_file_path, encoding="utf-8")

        cert_server = conf[cert_server]

        _cert_path = cert_server["path"]

        if not _cert_path:

            paths = ["cert"]

            cert_path = Path(__file__).parent.joinpath(*paths)

            logger.info(f'the cert_path:{cert_path}')

            ca = cert_server['ca']

            cert = cert_server['cert']

            key = cert_server['key']

            logger.info(f'the ca:{ca}')
            logger.info(f'the cert:{cert}')
            logger.info(f'the key:{key}')

        cafile = cert_path / Path(ca)

        certfile = cert_path / Path(cert)

        keyfile = cert_path / Path(key)

        return cafile, certfile, keyfile

    def _on_log(self, client, userdata, paho_log_level, messages):

        self.client.enable_logger(logger=None)

        if self.debug:

            print(f"PAHO DEBUG:{paho_log_level}:{messages}")

    def _on_message(self, client, userdata, msg):

        if self.debug:

            print(f"The filtered user data:{userdata}")
            print(f"the filtered msg info:\ntimestamp:{msg.timestamp}\nID:{msg.mid}")
            print(f"topic:{msg.topic}\nqos:{msg.qos}\npayload:{msg.payload}\nstate:{msg.state}")
            print(f"retain:{msg.retain}")

        filter_data = self.client.user_data_get()

        Msg_parser(msg, filter_data=filter_data)

    def _on_connect(self, client, usrdata, connect_flags, reason_code, properties):

        rc_dict = {0: 'connect success!',
                   1: 'protocol version error!',
                   2: 'invalid client ID!',
                   3: 'server is unavaliable!',
                   4: 'wrong username or password!',
                   5: 'unauthorized!'}

        if self.debug:

            print(f"connected with flags:{connect_flags},reason_code:{reason_code},properties:{properties} for client:{client}")

    def _on_disconnect(self, client, userdata, flags, rc):

        if rc:

            if self.debug:

                print(f"Unexpected disconnect error with flags:{flags},rc:{rc} for client:{client}")

        else:
            if self.debug:

                print(f"Disconnected with flags:{flags},rc:{rc} for client:{client}")

    def _on_subscribe(self, client, userdata, mid, reason_code_list, properties):

        if reason_code_list[0].is_failure:

            print(f"Broker rejected you subscription: {reason_code_list[0]}")

        else:

            print(f"Broker granted the following QoS: {reason_code_list[0].value}")

        if self.debug:

            print(f"Subscribe succeed,the userdata:{userdata},the mid:{mid},the reason_code_list:{reason_code_list},properties:{properties}!")

        """subscribe.callback(on_message_print, "paho/test/topic", hostname="mqtt.eclipseprojects.io", userdata={"message_count": 0})"""

    def _on_unsubscribe(self, client, userdata, mid, reason_code_list, properties):

        if len(reason_code_list) == 0 or not reason_code_list[0].is_failure:

            print("unsubscribe succeeded (if SUBACK is received in MQTTv3 it success)")

        else:

            print(f"Broker replied with failure: {reason_code_list[0]}")

        if self.debug:

            print(f"Unsubscribe succeed,the mid:{mid},the reason_code_list:{reason_code_list},properties:{properties}!")

    def _on_publish(self, client, userdata, mid, reason_code, properties):

        logger.info(f"Publish succeed,the userdata:{userdata},the mid is {mid},the reason_code:{reason_code},properties:{properties}!")

    def begin_receive_msg(self, duration=0):
        """  Start to receive MQTT message

        Kwargs：
            | duration | int | 0(by default),the time window in seconds for MQTT message receiving |
        Return:
            | None |
        Examples：
        | Begin Receive Msg | duration=10 |
        """
        logger.info(f"the user parameter:duration:{duration}")

        duration = float(duration)

        self.client.loop_start()

        if duration != 0:

            sleep(duration)

            self.client.loop_stop()

    def stop_receive_msg(self):
        """  Stop to receive MQTT message

        Args：
            | None |
        Return:
            | None |
        Examples：
        | Stop Receive Msg |
        """
        self.client.loop_stop()

    def client_id_get(self):
        """  Get the client ID

        Args：
            | None |
        Return:
            | client ID | string |
        Examples：
        | ${client_id}= | Client Id Get |
        """
        return self.client_id

    def client_id_set(self, client_id):
        """  Set the client ID

        Args：
            | client_id | string |
        Return:
            | None |
        Examples：
        | Client Id Set | client_id=ABCD |
        """
        logger.info(f"the user parameter:client_id:{client_id}")

        self.client.reinitialise(callback_api_version=self.callback_api_version, client_id=client_id, clean_session=self.clean_session)

        self.client_id = client_id

    def subscribe(self, *args, default_prefix='fwh', device_id=None, qos=0, subscribe_time_window=0, subscribe_all=True):
        """  Subscribe the topics,support wildcard '+' and '#'

        Args：
            | args | string | topic tuples like c2s/mng,s2c/mng... |
        Kwargs：
            | default_prefix | string | 'fwh'(by default) |
            | device_id | string | None(by default,will use the client ID of the connection),client ID |
            | qos | int | 0(by default)，0/1/2 |
            | subscribe_time_window | int | 0(by default),the waiting time in seconds after subscribe message is sent out |
            | subscribe_all | bool | True(by default),订阅该设备所有的消息 |
        Return:
            | MQTTErrorCode | int, 0:SUCCESS,4:No connection,13:Unknown error,7:Connection lost |
        Examples：
        | Subscribe | c2s/mng | s2c/mng |
        | Subscribe | c2s/# | s2c/# | match fhp/c2s and fhp/s2c |
        | Subscribe | # |  match fhp/*  |
        | Subscribe | +/+ | s2c/+ | match fhp/+/+/{device_id} and fhp/s2c/+/{device_id}  |
        | Subscribe | c2s/mng | s2c/mng |  device_id=ABC | qos=2 | subscribe_time_window=0.5 |
        """
        logger.info(f"the user parameter:args:{args},qos:{qos},subscribe_time_window:{subscribe_time_window},"
                    f"device_id:{device_id},default_prefix:{default_prefix}"
                    )

        if device_id is None:

            device_id = self.client_id

        filter_list = list()

        qos = int(qos)

        if subscribe_all:

            filter_list.append(tuple((f"{default_prefix}/+/+/{device_id}", qos)))

        else:

            for i in args:

                if default_prefix is not None:

                    filter_list.append(tuple((f"{default_prefix}/{i}/{device_id}", qos)))

                else:
                    filter_list.append(tuple((f"{i}/{device_id}", qos)))

        logger.info(f"the subsribe list:{filter_list}")

        ret = self.client.subscribe(filter_list)

        sleep(float(subscribe_time_window))

        if ret[0] != 0:

            raise ValueError(f"Sorry, fail to subscribe the topics:{filter_list}!")

        else:

            logger.info(f"OK,successfully subscribe the topics:{filter_list}!")

        return ret[0]

    def unsubscribe(self, *args, device_id=None, default_prefix='fwh'):
        """  Unsubscribe the topics

        Args：
            | args | string | topic tuples like c2s/mng,s2c/mng... |
        Kwargs：
            | device_id | string | None(by default,will use the client ID of the connection),client ID |
            | default_prefix | string | 'fwh'(by default) |
        Return:
            | None |
        Examples：
        | Unsubscribe | c2s/mng | s2c/mng |
        """
        logger.info(f"the user parameter:args:{args},device_id:{device_id},default_prefix:{default_prefix}")

        if device_id is None:

            device_id = self.client_id

        for i in args:

            if default_prefix is not None:

                topic = f"{default_prefix}/{i}/{device_id}"

            else:
                topic = f"{i}/{device_id}"

            logger.info(f"the {topic} to unsubscribe")

            try:

                status = self.client.unsubscribe(topic)

                if (status[0] != 0):

                    raise ValueError(f"Sorry, fail to unsubscribe the topic:{topic}!")

                else:
                    logger.info(f"OK,successfully unsubscribe the topic:{topic}!")

            except ValueError as e:

                raise e

    def pdu_build(self, cmdType=0, equipNo=None, **kwargs):
        """  Build MQTT PDU for Tx messages

        Kwargs：
            | cmdType | int | 0(by default) |
            | equipNo | string | device_id,'None'(by default) |
            | kwargs | possible key-value pairs |
        Return:
            | Json string object |
        Examples：
        | ${pdu}= | Pdu Build | cmdType=341 | equipNo=ABCD | opt=1 |
        """
        logger.info(f"the user parameter:cmdType:{cmdType},equipNo:{equipNo},kwargs:{kwargs}")

        if equipNo is None:

            equipNo = self.client_id

        _kwargs = dict()

        _snno = kwargs.pop('snno', None)

        snno = next(self.pkd_func) if _snno is None else _snno

        for k, v in kwargs.items():

            if k.startswith('__'):

                _kwargs.update({k[2:]: v})

            else:

                _kwargs.update({k: v})

        pkt = Pdu(cmdType=cmdType, equipNo=equipNo, len=140, dataArea=_kwargs, snno=snno)

        return pkt.json_format()

    def publish(self, topic, data=None, qos=0, rx_time_window=20, default_prefix='fwh', **kwargs):
        """  Publish MQTT message

        Args：
            | topic | string | 
        Kwargs：
            | qos | int | 0(by default)/1/2 |
            | data | Json data | the payload in MQTT Tx message |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message |
            | default_prefix | string | 'fwh'(by default) |
        Kwargs:
            | kwargs | possible key-value pairs |
        Return:
            | None |
        Examples：
        | ${data}=   | Pdu Build   | cmdType=341  | equipNo=ABCD | ethernet0NetSwitch=${0}	opt=${1} |
        | Publish | s2c/mng | data=${data} |
        """
        logger.info(f"the user parameter:ing:{topic},data:{data},qos:{qos}"
                    f"rx_time_window:{rx_time_window},default_prefix:{default_prefix},kwargs:{kwargs}"
                    )

        kwargs.update({'mode': 'non_couple', 'data': data, 'qos': int(qos), 'rx_time_window': float(rx_time_window)})

        if default_prefix is not None:

            if not topic.startswith(default_prefix):

                topic = f"{default_prefix}/{topic}"

        self.publish_and_receive(topic, **kwargs)

    def receive(self, expected_cmd_type=None, expected_equipNo=None, rx_time_window=20, rx_receive_step=0.1, filter_mode='auto', ret_mode='auto', filter_kwargs=None, **kwargs):
        """  Receive MQTT messages

        Kwargs：
            | expected_cmd_type | int | None(by default) the expected Cmd type in the Rx MQTT message,0:last will message |
            | expected_equipNo | string | None(by default) the expected device SN |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message |
            | rx_receive_step | float | 0.1(by default), the time step(seconds) to detect received MQTT message  |
            | filter_mode | string | 'auto'(by default): 只要检测到expected_cmd_type中的一个匹配则立即返回它,和or模式一样         |
            |             |        | 'and': 必须要检测到所有的expected_cmd_type均至少一次则返回它们 |
            |             |        | 'or': 检测到expected_cmd_type中的任意一个匹配则立即返回它 |
            |             |        | 'all': 必须等待rx_time_window时间，然后返回在此期间内所有捕获的报文，不区分expected_cmd_type |
            | ret_mode    | string | auto(by default,it will directly return the element while the result has only one element) or list |
            | filter_kwargs | dict | Rx filter dict for Rx packet content filter|
            | kwargs | dict | reserved |
        Return:
            | list |
        Examples：
        | ${msgs}= | Receive | rx_time_window=300 | filter_mode=all | #retrieve all MQTT messages within 5mins |
        | ${msg}=  | Receive | expected_cmd_type=341 | rx_time_window=300 | # retrieve the 1st 341 MQTT message in 5mins |
        | ${msgs}= | Receive | expected_cmd_type=341/342 | filter_mode=and | rx_time_window=300 | # retrieve the first 341 and 342 MQTT messages in 5mins |
        | ${msgs}= | Receive | expected_cmd_type=341/342 | filter_mode=or | rx_time_window=300 | # retrieve the first 341 or 342 MQTT messages in 5mins |

        """
        logger.info(f"the user parameter:expected_cmd_type:{expected_cmd_type},rx_time_window:{rx_time_window},"
                    f"rx_receive_step:{rx_receive_step},filter_mode:{filter_mode},ret_mode:{ret_mode},filter_kwargs:{filter_kwargs},kwargs:{kwargs}"
                    )

        rx_receive_step = float(rx_receive_step)

        filter_mode = filter_mode.lower()

        if expected_equipNo is None:

            expected_equipNo = self.client_id

        if expected_cmd_type is None:  # capture all mode

            self.msg_filter_set(cmdType=expected_cmd_type, equipNo=expected_equipNo, filter_mode=filter_mode)

        else:

            if filter_kwargs is not None:

                self.msg_filter_set(cmdType=expected_cmd_type, equipNo=expected_equipNo, filter_mode=filter_mode, **filter_kwargs)

            else:
                self.msg_filter_set(cmdType=expected_cmd_type, equipNo=expected_equipNo, filter_mode=filter_mode)

        if expected_cmd_type:

            _filter_data = str(expected_cmd_type).split("/")

            _filter_data = [int(j) for j in _filter_data]

        try:
            self.begin_receive_msg()

            step = rx_receive_step

            num = int(float(rx_time_window) / step) + 1

            for i in range(num):

                sleep(step)

                if MSG_LIST:

                    logger.info(f"OK,found the MQTT response number:{len(MSG_LIST)}\n")

                    for j in MSG_LIST:

                        logger.info(f"{json.dumps(j, indent=4)}")

                    if filter_mode == 'auto':

                        self.stop_receive_msg()

                        logger.info(f"the new MQTT response number:{len(MSG_LIST)}")

                        ret = MSG_LIST.copy()

                        MSG_LIST.clear()

                        logger.info(f'the ret_mode:{ret_mode},ret:{ret}')

                        if ret_mode == 'auto' and len(ret) == 1:

                            return ret[0]

                        else:

                            return ret

                    elif filter_mode == 'and':

                        if len(MSG_LIST) >= len(_filter_data):

                            _filter_data_tmp = deepcopy(_filter_data)

                            match_flag = True

                            for j in _filter_data_tmp:

                                if j not in [_item['cmdType'] for _item in MSG_LIST]:

                                    match_flag = False

                                    break

                            if match_flag:

                                self.stop_receive_msg()

                                ret = MSG_LIST.copy()

                                MSG_LIST.clear()

                                if ret_mode == 'auto' and len(ret) == 1:

                                    return ret[0]

                                else:

                                    return ret

                    elif filter_mode == 'or':

                        self.stop_receive_msg()

                        ret = MSG_LIST.copy()

                        MSG_LIST.clear()

                        if ret_mode == 'auto' and len(ret) == 1:

                            return ret[0]

                        else:

                            return ret

            self.stop_receive_msg()

            if not MSG_LIST:

                logger.error(f"Sorry,failed to find the MQTT response:{MSG_LIST} in {rx_time_window} seconds ")
            else:

                ret = MSG_LIST.copy()

                MSG_LIST.clear()

                if ret_mode == 'auto' and len(ret) == 1:

                    return ret[0]

                else:

                    return ret

        except ValueError as e:

            logger.error(f"Error,detect error:{e}")

            raise e

    def publish_and_receive(self, topic, data=None, expected_equipNo=None, qos=0, retain=False, properties=None, mode='couple', expected_cmd_type=0, default_prefix='fwh', device_id=None, rx_time_window=20, filter_kwargs=None, **kwargs):
        """  Publish and receive MQTT messages

        Args：
            | topic | int | 0(by default) |
        Kwargs:
            | qos | int | 0(by default)/1/2 |
            | data | Json data | the payload in MQTT Tx message |
            | rx_time_window | int | 20(by default),the waiting time in seconds to receive MQTT message |
            | default_prefix | string | 'fwh'(by default) |
            | retain | False(by default),True:the message will be set as the “last known good”/retained message for the topic |
            | properties | None(by default), appilcable for MQTTv5 |
            | mode | string | 'couple'(by default), MQTT Tx and Rx will be handled together,'non_couple':only do MQTT message Tx  |
            | device_id | string | device ID,None(by default, device_id in the MQTT connection will be used) |
            | expected_cmd_type | int | the expected Cmd type in the Rx MQTT message |
            | filter_kwargs | dict | Rx filter dict for Rx packet content filter|
            | kwargs | dict | reserved |
        Kwargs:
            | kwargs | possible key-value pair |
        Return:
            | Rx MQTT message or empty |
        Examples：
        | ${data}=   | Pdu Build   | cmdType=341  | equipNo=ABCD | ethernet0NetSwitch=${0} | opt=${1} |
        | ${response}= | Publish and Receive | s2c/mng | data=${data}	| expected_cmd_type=342 |
        """
        logger.info(f"the user parameter:topic:{topic},data:{data},retain:{retain},properties:{properties},"
                    f"mode:{mode},expected_cmd_type:{expected_cmd_type},default_prefix:{default_prefix},"
                    f"device_id:{device_id},rx_time_window:{rx_time_window},filter_kwargs:{filter_kwargs}, kwargs:{kwargs}"
                    )
        if expected_equipNo is None:

            expected_equipNo = self.client_id

        if mode == 'couple':

            logger.info(f"the expected_cmd_type:{expected_cmd_type}")

            if filter_kwargs is not None:

                self.msg_filter_set(cmdType=expected_cmd_type, equipNo=expected_equipNo, filter_mode='auto', **filter_kwargs)

            else:

                self.msg_filter_set(cmdType=expected_cmd_type, equipNo=expected_equipNo, filter_mode='auto')

        if default_prefix is not None:

            topic = f"{default_prefix}/{topic}"

        if device_id is None:

            device_id = self.client_id

            topic = f"{topic}/{device_id}"

        try:
            logger.info(f"The publish message to Tx:\nthe topic:{topic}\nthe payload:{data}\n")

            time_start = time()

            result, mid = self.client.publish(topic=topic, payload=data, qos=int(qos), retain=retain, properties=properties)

            logger.info(f"the message publish info:mid:{mid},result:{result}")

            self.begin_receive_msg()

            if result != 0:

                raise RuntimeError(f"FAIL to publish due to {result}")

            step = 0.1

            num = int(float(rx_time_window) / step)

            for i in range(num):

                sleep(step)

                if mode == 'couple':

                    if MSG_LIST:

                        time_end = time()

                        delta = time_end - time_start

                        for j in MSG_LIST:

                            logger.info(f"OK,found the c2s response:\n{json.dumps(j, indent=4)}")

                        logger.info(f"==" * 30)

                        logger.info(f"the Tx->Rx delta time is {delta:.4} second(s)")

                        logger.info(f"==" * 30)

                        ret = MSG_LIST[0] if len(MSG_LIST) == 1 else MSG_LIST[::]

                        self.stop_receive_msg()

                        MSG_LIST.clear()

                        return ret

            self.stop_receive_msg()

            if not MSG_LIST:

                logger.error(f"Sorry,failed to find the c2s response:\n\
                              {MSG_LIST}")

                return ''

        except ValueError as e:

            logger.error(f"Error,detect error:{e}！")

    def get_captured_msg(self, cmdType=None):
        """  Get captured mesages from the captured buff

        Kwargs：
            | cmdType | int or string(with '/' separation) | None(by default),the cmdType(s) to filter for the Rx message |
        Return:
            | list |
        Examples:
        | ${msgs} | Get Captured Msg |
        | ${msgs} | Get Captured Msg | cmdType=341 |
        | ${msgs} | Get Captured Msg | cmdType=341/342/366 |
        """
        if not MSG_LIST:

            logger.info('No captured msgs are found!')

        else:
            if cmdType is not None:

                _cmd_list = cmdType.split('/')

                tmp_MSG_list = list()

                for i in MSG_LIST:

                    if str(i['cmdType']) in _cmd_list:

                        tmp_MSG_list.append(i)

                return tmp_MSG_list

        return MSG_LIST

    def msg_filter_set(self, cmdType=0, equipNo=None, filter_mode='auto', **kwargs):
        """  Filter for Rx MQTT message

        Kwargs：
            | cmdType | int or string(with '/' separation) | 0(by default),the cmdType(s) to filter for the Rx message |
            | equipNo | string | None(by default) |
            | filter_mode | auto(by default)/and/or/all |
            | kwargs | possible key-value pairs in dataArea |
        Return:
            | None |
        Examples:
        | Msg Filter Set | cmdType=341 |
        | Msg Filter Set | cmdType=341/342/366 |
        | Msg Filter Set | cmdType=201 | report_type=${0} |

        """
        logger.info(f"the user parameter in msg_filter_set:cmdType:{cmdType}, equipNo:{equipNo}, filter_mode:{filter_mode},kwargs:{kwargs}")

        if equipNo is None:

            equipNo = self.client_id

        _data_dict = {"cmdType": cmdType, "equipNo": equipNo, "__filter_mode__": filter_mode, **kwargs}

        logger.info(f'the filter data:{_data_dict}')

        self.client.user_data_set(_data_dict)

    def disconnect_server(self, reasoncode=None, properties=None):
        """  Disconnect the remote MQTT broker

        Kwargs：
            | reasoncode | None(by default), appilcable for MQTTv5 |
            | properties | None(by default), appilcable for MQTTv5 |
        Return:
            | MQTTErrorCode | int, 0:SUCCESS,4:No connection,13:Unknown error,7:Connection lost |
        Examples：
        | Disconnect Server |
        """
        logger.info(f"the user parameter:reasoncode:{reasoncode},properties:{properties}")

        if self.mode == 'remote':

            logger.info(f"disconnect_server,reasoncode:{reasoncode},properties:{properties}")

            return self.client.disconnect(reasoncode=reasoncode, properties=properties)

        else:
            logger.info(f"disconnect the local socket:{self.client}")

            self.client.close()

    def connect_to_local_socket(self, cmdtype=1101, sn="00000000", snno=1, payload={'opt': 0, 'minProtocolVer': "V1.10.01"}):
        """  Connect to the device via local socket

        Kwargs：
            | cmdtype | 要发送包的cmdtype, 1101(by default) |
            | sn | 要发送的设备序列号，00000000(by default) |
            | snno | 要发送包的序列号，SNNO, 1(by default) |
            | payload | 要发送包中的paylod, dict,{'opt': 0, 'minProtocolVer': "V1.10.01"}(by default) |
        Return:
            | string | 从设备返回的应答报文 |
        Examples：
        | ${response} | Connect To Local Socket |
        """
        logger.info(f"the user parameter:cmdtype:{cmdtype},sn:{sn},snno:{snno},payload:{payload}")

        return self.build_local_packet_to_send(cmdtype=cmdtype, sn=sn, snno=snno, payload=payload)

    def build_local_packet_to_send(self, cmdtype=None, sn=None, snno=None, payload=None):
        """  build local Tx packet to send

        Kwargs：
            | cmdtype | 要发送包的cmdtype |
            | sn | 要发送的设备序列号 |
            | snno | 要发送包的序列号 |
            | payload | 要发送包中的paylod, dict |
        Return:
            | string | 从设备返回的应答报文 |
        Examples：
        | ${response} | Build Local Packet To Send |
        """
        logger.info(f"the user parameter in build_local_packet_to_send:cmdtype:{cmdtype},sn:{sn},snno:{snno},payload:{payload}")

        if str(cmdtype) != "1101":

            sn = self.client_id

        if snno is None:

            snno = next(self.pkd_func_local)

        pkt = LocalPdu(sn=sn, data=payload)

        _data = pkt.build_local_packet(int(cmdtype), snno)

        # pkt = Pdu(cmdType=cmdType, equipNo=sn, len=140, dataArea=self.data, snno=snno)

        # return pkt.json_format()

        logger.debug(f"Tx packet:{_data}")

        self.client.sendall(_data)

        self.response = self.client.recv(1024)

        logger.debug(f"Got the raw response:{self.response!r}")

        ret = self.get_local_packet_to_parse(self.response)

        logger.info(f"Got Rx packet:{ret}")

        return ret

    def get_local_packet_to_parse(self, response=None):
        """  Get local Rx encrypted packet to Parse

        Kwargs：
            | response | 接收的加密报文 |
        Return:
            | string | 解密后的报文 |
        Examples：
        | ${response} | Get Local Packet To Parse |
        """
        logger.info(f"the user parameter:response:{response}")

        _data = LocalPdu(None, response)

        self.response = _data.decrypt()

        logger.debug(f'got the response:{self.response}')

        return self.response


if __name__ == 'main':

    m = Mqtt()

    m.connect_server(client_id='ABCD')

    m.subscribe()

    m.begin_receive_msg(duration=2)

    m.stop_receive_msg()
