import re
from time import (sleep, time, strptime, mktime)
from robot.api import logger


class UpgradeLogAnalyze(object):

    def analyze_upgrade_log(self, strings, event_dict=None):

        logger.info(f'the strings:{type(strings)},the event_dict:{event_dict}')

        if event_dict is None:

            event_dict = dict()

        result = list()

        if not strings:

            return

        if isinstance(strings, list):

            _strings = strings

        else:

            # _strings = strings.split(', ')

            _strings = strings.split('\n')

        logger.info(f'the strings:{_strings},the length:{len(_strings)}')

        timestamp_pattern = r'\d{8}\s+\d{2}:\d{2}:\d{2}.\d{3}'

        key1 = 'start send bin:'

        key2 = 'send success'

        key3 = '******* ver info  ********'

        key4 = '******* bk ver info  ********'

        sw_info_before_upgrade = False

        sw_info_after_upgrade = False

        len_strings = len(_strings) - 1

        key3_flag_sw_info_before_upgrade = False

        key3_flag_sw_info_after_upgrade = False

        for index, i in enumerate(_strings):

            timestamp = self._get_timestamp(i, pattern=timestamp_pattern)

            if timestamp:

                if 'downLoadPro=0' in i or ' token=' in i:

                    result.append(['SW is downloading', timestamp])

                if 'download ok tar next' in i:

                    result.append(['SW download is completed', timestamp])

                if 'input Zip path:' in i:

                    file = re.findall('''upgrade/(.*)''', i)

                    result.append(['software', file[0], timestamp])

                    sw_info_before_upgrade = True

                if 'tar -jxvf' in i:

                    file = re.findall('''tar -jxvf /home/<USER>/upgrade/(.*) -C''', i)

                    result.append(['software', file[0], timestamp])

                if key1 in i:

                    file_name = re.findall(r'.*?%s(.*?\.bin).*?' % (key1), i)

                    result.append([f'start send bin__{file_name[0]}', timestamp])

                if key2 in i:

                    file_name = result[-1][0].split('__')[-1]

                    result.append([f'stop send bin__{file_name}', timestamp])

                if key3 in i:

                    _index = index

                    if sw_info_before_upgrade:

                        tmp_list = list()

                        sw_info_after_upgrade = True

                        while key4 not in _strings[_index]:

                            _str = _strings[_index]

                            tmp_list.append(_str[2:-1].replace('\\t', ''))

                            _index += 1

                            if _index == len_strings:

                                key3_flag_sw_info_before_upgrade = True

                                break

                        result.append(['the SW info before', tmp_list[1:]])

                    else:

                        tmp_list = list()

                        sw_info_before_upgrade = False

                        while key4 not in _strings[_index]:

                            _str = _strings[_index]

                            tmp_list.append(_str[2:-1].replace('\\t', ''))

                            _index += 1

                            if _index == len_strings:

                                key3_flag_sw_info_after_upgrade = True

                                break

                        result.append(['the SW info after', tmp_list[1:]])

                if 'bin upgrade ok:' in i:

                    key = 'bin upgrade ok:'

                    file = re.findall(f'{key}(.*)', i)

                    result.append([f'{key}{file[0]}', timestamp])

                if 'upgrade success!' in i:

                    result.append(['Upgrade is completed!', timestamp])
            else:

                if key3_flag_sw_info_before_upgrade:

                    if key4 not in _strings[_index]:

                        result['the SW info before'].append(i[2:-1].replace('\\t', ''))

                    else:

                        key3_flag_sw_info_before_upgrade = False

                        continue

                if key3_flag_sw_info_after_upgrade:

                    if key4 not in _strings[_index]:

                        result['the SW info after'].append(i[2:-1].replace('\\t', ''))

                    else:

                        key3_flag_sw_info_after_upgrade = False

                        continue

        logger.debug(f'Got the initial log info:{result}')

        self._anaylze_log(result, event_dict)

        return event_dict

    def _get_timestamp(self, value, pattern=None):

        match = re.findall(r'%s' % (pattern), value)

        if match:

            return match[0]

        return None

    def _anaylze_log(self, result, event_dict):

        logger.info(f'the result:{result}', also_console=True)

        for i in result:

            if 'SW is downloading' in i[0]:

                event_dict.update(SW_download_start=i[1])

                event_dict.update(upgrade_start=i[1])

                logger.console(f'{i[1]}:Software is downloading...')

            if 'SW download is completed' in i[0]:

                logger.console(f'{i[1]}:Downloading software is completed...')

                event_dict.update(SW_download_stop=i[1])

                delta = self.compute_delta_time(event_dict['SW_download_start'], event_dict['SW_download_stop'])

                logger.console(f'{i[1]}:The software download time is {delta}seconds')

                event_dict.pop('SW_download_start')

                event_dict.pop('SW_download_stop')

                event_dict.update({'SW_download_time': f'{delta} seconds'})

            if 'software' in i[0]:

                logger.console(f'{i[2]}:The downloaded software name:{i[1]}')

                event_dict.update({'SW_name': f'{i[1]}'})

            if 'the SW info before' in i[0]:

                logger.console(f'the firmware information before upgrading:')

                for j in i[1]:

                    logger.console(j)

            if 'start send bin_' in i[0]:

                _, file_name = i[0].split('__')

                event_dict.update({f'Bin_send_start_{file_name}': i[1]})

                logger.console(f'{i[1]}:The file {file_name} is downloading...')

            if 'stop send bin__' in i[0]:

                _, file_name = i[0].split('__')

                event_dict.update({f'Bin_send_stop_{file_name}': i[1]})

                event_dict.update({f'Bin_upgrade_start_{file_name}': i[1]})

                delta = self.compute_delta_time(event_dict[f'Bin_send_start_{file_name}'], event_dict[f'Bin_send_stop_{file_name}'])

                logger.console(f'{i[1]}:The file {file_name} is downloaded, the download time is {delta}seconds')

                event_dict.pop(f'Bin_send_start_{file_name}')

                event_dict.pop(f'Bin_send_stop_{file_name}')

                _key = f'{file_name}_download_time'

                event_dict.update({_key: f'{delta} seconds'})

            if 'bin upgrade ok:' in i[0]:

                _, file_name = i[0].split(':')

                event_dict.update({f'Bin_upgrade_{file_name}': i[1]})

                event_dict.update({f'Bin_upgrade_stop_{file_name}': i[1]})

                logger.console(f'{i[1]}:The file {file_name} upgrade is completed!')

                delta = self.compute_delta_time(event_dict[f'Bin_upgrade_start_{file_name}'], event_dict[f'Bin_upgrade_stop_{file_name}'])

                logger.console(f'{i[1]}:The file {file_name} is upgraded, the upgrade time is {delta}seconds')

                event_dict.pop(f'Bin_upgrade_{file_name}')

                event_dict.pop(f'Bin_upgrade_start_{file_name}')

                event_dict.pop(f'Bin_upgrade_stop_{file_name}')

                _key = f'{file_name}_upgrade_time'

                event_dict.update({_key: f'{delta} seconds'})

            if 'the SW info after' in i[0]:

                logger.console(f'the firmware information after upgrading:')

                for j in i[1]:

                    logger.console(j)

            if 'Upgrade is completed' in i[0] or 'upgrade fail!' in i[0]:

                logger.console(f'{i[1]}:OK,the whole upgrade is completed!')

                event_dict.update(upgrade_end=i[1])

                delta = self.compute_delta_time(event_dict['upgrade_start'], event_dict['upgrade_end'])

                logger.console(f'the whole upgrade time is {delta}seconds')

                event_dict.pop(f'upgrade_start')

                event_dict.pop(f'upgrade_end')

                event_dict.update(the_whole_upgrade_time=f'{delta} seconds')

        sw_name = event_dict.pop('SW_name')

        sw_download_time = event_dict.pop('SW_download_time')

        event_dict.update({f'{sw_name}_download_time': sw_download_time})

    def compute_delta_time(self, start, end, time_format='%Y%m%d %H:%M:%S.%f'):

        start = strptime(start, time_format)

        end = strptime(end, time_format)

        return mktime(end) - mktime(start)
