from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 任意模块日志召唤

app_name = "debug"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 612

cmd_type_s2c = 611

common_dict = {

    'fileName': ('file_name', 'string'),

}

attr_map_s2c = {

    'url': ('', 'string'),
    'logTime': ('log_time', 'string'),
    'moduleName': ('module_name', 'string'),

    **common_dict,

}

attr_map_c2s = {

    **attr_map_common_result,

    **common_dict,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class ModuleLog(object):

    def module_log_upload_set(self, *args, **kwargs):
        """  module log upload set-T611/T612

        Args:
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | module_name | string | 模块名称 |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Module Log Upload Set | module_name=aws | url=http://abc.com | file_name=123 | log_time=2024-12-26 |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def module_log_upload_set_result_check(self, _response, **kwargs):
        """  module log upload set result check-T612

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = |  Module Log Upload Set | module_name=aws | url=http://abc.com | file_name=123 | log_time=2024-12-26 |
        | ${status} = | Module Log Upload Set Result Check | ${response} | result=0  |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def module_log_upload_set_from_aws_get(self, *args, **kwargs):
        """  module log upload set from AWS,the message get-T611

        Args：
            | module_name | string | 模块名称 |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | Module Log Upload Set From AWS Get | fault_record_id |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def module_log_upload_set_from_aws_get_check(self, **kwargs):
        """  module log upload set from AWS,the message get check-T611

        Kwargs:
            | module_name | string | 模块名称 |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Module Log Upload Set From AWS Get Check | log_time=2024-12-26 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.module_log_upload_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def module_log_upload_set_from_device_get(self, *args, **kwargs):
        """  module log upload set from device,the message get-T612

        Args：
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Module Log Upload Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def module_log_upload_set_from_device_get_check(self, **kwargs):
        """  module log upload set from device,the message get check-T612

        Kwargs:
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Module Log Upload Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.module_log_upload_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def module_log_upload_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get from T611/612

        Args for T611:
            | module_name | string | 模块名称 |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Args for T612：
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 611/612 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=612 | rx_time_window=300 |
        | ${status} | Module Log Upload Set From Msg Get | file_name | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.module_log_upload_set_from_device_get, cmd_type_s2c: self.module_log_upload_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def module_log_upload_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_c2s, **kwargs):
        """  the monitored messages get check from T611/612

        Kwargs for T611:
            | module_name | string | 模块名称 |
            | url | string | 文件路径，<=128Bytes |
            | log_time | string | 需要召唤的日志时间，例如2024-12-26 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for T612：
            | result | int | 结果，0：成功； 1.：解析失败； 2：模块不存在； 3：压缩失败 |
            | file_name | string | 日志文件名，<=64Bytes |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 611/612 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=612 | rx_time_window=300 | filter_mode=and |
        | ${status} | Module Log Upload Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.module_log_upload_set_from_device_get_check, cmd_type_s2c: self.module_log_upload_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
