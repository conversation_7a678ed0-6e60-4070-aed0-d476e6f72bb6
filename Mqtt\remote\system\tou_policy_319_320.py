from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# ToU策略配置/查询

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 319

cmd_type_c2s = 320

attr_map_s2c = {

    **attr_map_common_opt,

    'yearStra': ('year_policy', 'list'),
    'TouStrategy ': ('tou_policy', 'int'),
    'strategyNum ': ('tou_policy_number', 'int'),
    'strategy1': ('policy1', 'list'),
    'strategy2': ('policy2', 'list'),
    'strategy3': ('policy3', 'list'),
    'strategy4': ('policy4', 'list'),
    'strategy5': ('policy5', 'list'),
    'strategy6': ('policy6', 'list'),
    'strategy7': ('policy7', 'list'),
    'strategy8': ('policy8', 'list'),
    'strategy9': ('policy9', 'list'),
    'strategy10': ('policy10', 'list'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class TouPolicy(object):

    def tou_policy_set(self, *args, **kwargs):
        """  TOU Policy set-T319/320

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | TOU Policy Set | opt=0 |
        | ${status} = | TOU Policy Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def tou_policy_set_result_check(self, _response, **kwargs):
        """  system set result check-T320

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | TOU Policy Set | opt=1 |
        | ${status} = | TOU Policy Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def tou_policy_set_from_aws_get(self, *args, **kwargs):
        """  system set from AWS,the message get-T319

        Args：
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   TOU Policy Set From AWS Get | year_policy |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def tou_policy_set_from_aws_get_check(self, **kwargs):
        """  system set from AWS,the message get check-T319

        Kwargs:
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | TOU Policy Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.global_system_parameter_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def tou_policy_set_from_device_get(self, *args, **kwargs):
        """   system set response from device,the message get-T320

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | TOU Policy Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def tou_policy_set_from_device_get_check(self, **kwargs):
        """  system set response from device,the message get check-T320

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  TOU Policy Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.system_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def tou_policy_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T319/320

        Args for T319:
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Args for T320：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 319/320 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=319 | rx_time_window=300 |
        | ${status} | TOU Policy Set From Msg Get | tou_policy | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.tou_policy_set_from_device_get, cmd_type_s2c: self.tou_policy_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def tou_policy_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T319/320

        Kwargs for T319:
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs for T320：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:年策略表方案，2：设置策略表 |
            | year_policy | list | 年策略表方案，366个，表示1年366天 |
            | tou_policy | int | 0：平衡(优先供载)，1：经济(优先储电) |
            | tou_policy_number | int | 策略表数量 |
            | policy1 | dict  | 策略表1数据域 |
            | policy2 | dict  | 策略表2数据域 |
            | policy3 | dict  | 策略表3数据域 |
            | policy4 | dict  | 策略表4数据域 |
            | policy5 | dict  | 策略表5数据域 |
            | policy6 | dict  | 策略表6数据域 |
            | policy7 | dict  | 策略表7数据域 |
            | policy8 | dict  | 策略表8数据域 |
            | policy9 | dict  | 策略表9数据域 |
            | policy10 | dict  | 策略表10数据域 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 319/320 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=320 | rx_time_window=300 | filter_mode=and |
        | ${status} | TOU Policy Set From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.tou_policy_set_from_device_get_check, cmd_type_s2c: self.tou_policy_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
