from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import msg_parser, build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_result

# 实时数据补传

app_name = "rt"

topic_c2s = f"c2s/{app_name}"

topic_s2c = f"s2c/{app_name}"

cmd_type_c2s = 209

cmd_type_s2c = 210

attr_map_s2c = {
    **attr_map_common_result,
}

attr_map_c2s = {
    'date': ('', 'string'),
    'UsrRtFile': ('user_file', 'string'),
    'sign': ('', 'string'),
}

com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class RealTimeDataReissue(object):

    def real_time_data_reissue_from_aws_get(self, *args, **kwargs):
        """  the real time data reissue from AWS,the message get-T210

        Args：
            | result | int | 操作结果，0：成功，1：失败 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Data Reissue From AWS Get | opt | refresh_data |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_data_reissue_from_aws_get_check(self, **kwargs):
        """  the real time data reissue from AWS,the message get check-T210

        Kwargs:
            | result | int | 操作结果，0：成功，1：失败 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Real Time Data Reissue From AWS Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_data_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_data_reissue_from_device_get(self, *args, **kwargs):
        """  the real time data reissue from device,the message get-T209

        Args：
            | user_file |  string | <=128Bytes,用户文件上传URL |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | <=20Bytes,yyyy-mm-dd |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |  Real Time Data Reissue From Device Get | user_file |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def real_time_data_reissue_from_device_get_check(self, **kwargs):
        """  the real time data reissue from device,the message get check-T209

        Kwargs:
            | user_file |  string | <=128Bytes,用户文件上传URL |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | <=20Bytes,yyyy-mm-dd |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  Real Time  Data Reissue From Device Get Check | date=2024-12-10 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.real_time_data_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def real_time_data_reissue_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T209/210

        Args for T209:
            | user_file |  string | <=128Bytes,用户文件上传URL |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | <=20Bytes,yyyy-mm-dd |
        Args for T210：
            | result | int | 操作结果，0：成功，1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 209/210 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=209 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Data Reissue From Msg Get | user_file | msg=${packets} |
        | ${status} | Real Time Data Reissue From Msg Get | user_file | sign | msg=${packets} | ret_format=dict |
        | ${packets} | Receive | expected_cmd_type=210 | rx_time_window=300 |
        | ${status} | Real Time Data Reissue From Msg Get | result | msg=${packets}[0] |
        """
        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_data_reissue_from_device_get, cmd_type_s2c: self.real_time_data_reissue_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def real_time_data_reissue_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T209/210

        Kwargs for T209:
            | user_file |  string | <=128Bytes,用户文件上传URL |  |
            | sign | string | 32位，厂商文件 MD5 checksum |
            | date | string | <=20Bytes,yyyy-mm-dd |
        Kwargs for T210：
            | result | int | 操作结果，0：成功，1：失败 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 209/210 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=210 | rx_time_window=300 | filter_mode=and |
        | ${status} | Real Time Data Reissue From Msg Get Check | result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.real_time_data_reissue_from_device_get_check, cmd_type_s2c: self.real_time_data_reissue_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
