from APP.base import Base
from robot.api import logger
from .locator import get_locator


class Detection(Base):

    def go_to_commission_network_setting_detection_page(self):
        """ Go to system->commission->network setting->Network Detection page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Go To Commission Network Setting 4G Page |
        """
        value = "locator_network_detection"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True, will_do_loading_detect=True)

    def exit_commission_network_setting_detection_page(self):
        """ exit system->commission->network setting->Network Detection page and go back to the system->commission->network setting page

        Kwargs:
            | None |
        Return:
            | None |
        Examples:
            | Exit Commission Network Setting 4G Page |
        """
        self._general_backward_upper_page()

    def start_commission_network_setting_detection(self, interface='ethernet', wifi_hotspot=None, wifi_password=None):
        """ do commission->network setting->Network Detection

        Kwargs:
            | interface | ethernet/wifi/4g |
        Return:
            | dict | 网络检测结果 |
        Examples:
            | Do Commission Network Setting Detection |
        """
        logger.info(f'the user parameters:interface:{interface}')

        ret_dict = {}

        locator_dict = {'ethernet': 'locator_network_setting_ethernet',
                        '4g': 'locator_network_detection_4g',
                        'wifi': 'locator_network_detection_wifi',
                        }

        value = locator_dict[interface]

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        if interface == 'ethernet':

            self._system_complete_locator("locator_general_confirm")

            self._system_complete_locator("locator_general_connect")

            self._commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=2)

            self._general_backward_upper_page()

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        if interface == 'wifi':

            self._system_complete_locator("locator_general_confirm")

            self._system_complete_locator("locator_general_connect")

            self._commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=2)

            self._general_backward_upper_page()

            self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        if interface == '4g':

            ele = self._system_wait_to_show("locator_general_confirm", timeout=1, default_max_timeout=1)

            if ele:

                self._system_complete_locator("locator_general_confirm")

                self._system_complete_locator("locator_general_connect")

                self._commission_network_wifi_agate_connect(wifi_hotspot=wifi_hotspot, wifi_password=wifi_password, retry=2)

                self._general_backward_upper_page()

                self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        value = "locator_network_detection_start_detection"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._do_network_test(interface, ret_dict)

        logger.info(f'OK,the network test result:{ret_dict}')

        if not ret_dict:

            self.save_screenshot()

            raise ValueError('Fail to get test results!')

        return ret_dict

    def _do_network_test(self, interface, ret_dict):

        self._system_wait_to_show()

        if interface == '4g':

            locator_name = "locator_network_detection_4g_result"

        else:

            locator_name = "locator_network_detection_eth_result"

        eles = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element, multiple=True)

        if interface == 'ethernet':

            for index, value in enumerate(eles):

                _value = self.get_element_attribute(value, "content-desc")

                if index == 0:
                    key = 'LAN_packet_loss_rate'

                else:
                    key = 'end_to_end_packet_loss_rate'

                ret_dict.update({key: _value})

        elif interface == 'wifi':

            for index, value in enumerate(eles):

                _value = self.get_element_attribute(value, "content-desc")

                if index == 0:
                    key = 'signal_strength'

                elif index == 1:
                    key = 'LAN_packet_loss_rate'

                elif index == 2:
                    key = 'end_to_end_packet_loss_rate'

                ret_dict.update({key: _value})

            locator_name = "locator_network_detection_wifi_result_signal_value"

            ele = self._get_locator_and_find_element(locator_name, locator_fun=get_locator, ele_find_func=self.find_element)

            _value = self.get_element_attribute(ele, "content-desc")

            ret_dict.update({'signal_value': _value})

        elif interface == '4g':

            for index, value in enumerate(eles):

                _value = self.get_element_attribute(value, "content-desc")

                if index == 3:
                    key = 'signal_strength'
                    ret_dict.update({key: _value})

                elif index == 5:
                    key = 'RSRP'
                    ret_dict.update({key: _value})

                elif index == 7:
                    key = 'RSRQ'
                    ret_dict.update({key: _value})

                elif index == 9:
                    key = 'RSSI'
                    ret_dict.update({key: _value})

                elif index == 11:
                    key = 'SINR'
                    ret_dict.update({key: _value})

                elif index == 13:
                    key = 'end_to_end_packet_loss_rate'
                    ret_dict.update({key: _value})

    def do_commission_network_setting_detection(self, interface='ethernet'):
        """ do commission->network setting->Network Detection

        Kwargs:
            | interface | ethernet/wifi/4g |
        Return:
            | dict | 网络检测结果 |
        Examples:
            | Do Commission Network Setting Detection |
        """
        logger.info(f'the user parameters:interface:{interface}')

        ret_dict = {}

        self._system_detect_locator()

        value = "locator_network_detection_start_detection"

        self._get_locator_and_find_element(value, locator_fun=get_locator, ele_find_func=self.find_element, click=True)

        self._do_network_test(interface, ret_dict)

        logger.info(f'OK,the network test result:{ret_dict}')

        return ret_dict

    def complete_commission_network_setting_detection(self, interface='ethernet'):
        """ do commission->network setting->Network Detection

        Kwargs:
            | interface | ethernet/wifi/4g |
        Return:
            | dict | 网络检测结果 |
        Examples:
            | Do Commission Network Setting Detection |
        """
        logger.info(f'the user parameters:interface:{interface}')

        self._system_complete_locator()
