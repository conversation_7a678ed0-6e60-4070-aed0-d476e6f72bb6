from robot.api import logger
from Mqtt.util.base import Base
from Mqtt.util.util import build_s2c_c2s_dict, logger, get_func_name, build_tx_dict
from ..constant import attr_map_common_opt, attr_map_common_result

# WiFi查询配置

app_name = "mng"

topic_s2c = f"s2c/{app_name}"

topic_c2s = f"c2s/{app_name}"

cmd_type_s2c = 321

cmd_type_c2s = 322

attr_map_s2c = {

    **attr_map_common_opt,

    'ap_SSID': ('AP_SSID', 'string'),
    'ap_Pw': ('AP_password', 'string'),

}

attr_map_c2s = {

    **attr_map_common_result,

    **attr_map_s2c,

}


com_dict_c2s, com_dict_s2c = build_s2c_c2s_dict(cmd_type_c2s, topic_c2s, attr_map_c2s, cmd_type_s2c, topic_s2c, attr_map_s2c)


class WifiSet(object):

    def wifi_set(self, *args, **kwargs):
        """  aGate WiFi AP set-T321/322

        Args：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | WiFi Set | opt=0 |
        | ${status} = | WiFi Set | result | opt=0 |
        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.setdefault('opt', 1)

        build_tx_dict(kwargs, attr_map_s2c, com_dict_s2c, com_dict_c2s)

        return self._get(*args, **kwargs)

    def wifi_set_result_check(self, _response, **kwargs):
        """  aGate WiFi AP set result check-T322

        Args：
            | _response | string | set操作后设备返回的响应包 |
        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
            | ret_type | string enum | 'auto'(by default) or 'list' |
            | ret_format | string enum | list(by default) or dict |
            | rx_time_window | int | 5(by default),the waiting time in seconds to receive MQTT message |
        Return:
            | string | string: one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${response} = | WiFi Set | opt=1 |
        | ${status} = | WiFi Set Result Check | ${response} | result=0  | opt=0 |
        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        mapping_dict = attr_map_c2s

        return self._check_dict_response(_response, mapping_dict, expected_dict=kwargs)

    def wifi_set_from_aws_get(self, *args, **kwargs):
        """ aGate WiFi AP set from AWS,the message get-T321

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = |   WiFi Set From AWS Get | AP_SSID |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_s2c, **com_dict_s2c)

        return self._get_expected_msgs(*args, **kwargs)

    def wifi_set_from_aws_get_check(self, **kwargs):
        """  aGate WiFi AP set from AWS,the message get check-T321

        Kwargs:
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = | WiFi Set From AWS Get Check | opt=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.wifi_set_from_aws_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def wifi_set_from_device_get(self, *args, **kwargs):
        """   aGate WiFi AP set response from device,the message get-T322

        Args：
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs:
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${status} = | WiFi Set From Device Get | result |

        """
        logger.info(f"the user args in {get_func_name()} is {args}\r\nkwargs is:{kwargs}")

        kwargs.update(expected_cmd_type=cmd_type_c2s, **com_dict_c2s)

        return self._get_expected_msgs(*args, **kwargs)

    def wifi_set_from_device_get_check(self, **kwargs):
        """  aGate WiFi AP set response from device,the message get check-T322

        Kwargs:
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${status} = |  WiFi Set From Device Get Check | result=0 |

        """
        logger.info(f"the user kwargs in {get_func_name()} is:{kwargs}")

        func_get = self.wifi_set_from_device_get

        kwargs.update(func_get=func_get)

        return self._get_check(**kwargs)

    def wifi_set_from_msg_get(self, *args, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get from T321/322

        Args for T321:
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Args for T322：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 321/322 |
            | ret_type | string enum | 'auto'(by default) or 'list' | lib control parameter |
            | ret_format | string enum | list(by default) or dict | lib control parameter |
            | rx_time_window | int | 300(by default),the waiting time in seconds to receive MQTT message | lib control parameter |
        Return:
            | string | one single arg is given when ret_format=list is applied |
            | list   | multiple args are given when ret_format=list is applied |
            | dict   | no args are given or ret_format=dict is applied |
        Examples：
        | ${packets} | Receive | expected_cmd_type=321 | rx_time_window=300 |
        | ${status} | WiFi Set From Msg Get | AP_SSID | msg=${packets}[0] |
        """

        logger.info(f"the user kwargs in {get_func_name()} is: arg:{args},msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.wifi_set_from_device_get, cmd_type_s2c: self.wifi_set_from_aws_get}

        return self._get_from_msg(func_pair, msg, cmd_type, *args, **kwargs)

    def wifi_set_from_msg_get_check(self, msg=None, cmd_type=cmd_type_s2c, **kwargs):
        """  the monitored messages get check from T321/322

        Kwargs for T321:
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs for T322：
            | result | int | 结果，0:成功，1:失败 |
            | opt | int | 操作类型，0:查询，1:设置 |
            | AP_SSID | string | aGate AP热点的SSID，长度：8~31字节 |
            | AP_password | string | aGate AP热点的密码，长度：8~31字节 |
        Kwargs for common:
            | msg | list/dict | None(by default), the single protocol msg dict or msg list |
            | cmd_type | int | 321/322 |
            | raise_error | true(by default):always raise exception when the comparison result is not matched, false:disable expection raising | lib control parameter |
        Return:
            | bool | True: the result is matching, False:the result is not matched while raise_error=False |
        Examples：
        | ${packets} | Receive | expected_cmd_type=322 | rx_time_window=300 | filter_mode=and |
        | ${status} | WiFi Set From Msg Get Check| result=0 | msg=${packets} |
        """

        logger.info(f"the user kwargs in {get_func_name()} is:msg:{msg},cmd_Type:{cmd_type},kwargs:{kwargs}")

        func_pair = {cmd_type_c2s: self.wifi_set_from_device_get_check, cmd_type_s2c: self.wifi_set_from_aws_get_check}

        return self._get_check_from_msg(func_pair, msg, cmd_type, **kwargs)
